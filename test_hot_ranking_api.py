#!/usr/bin/env python3
"""
测试新的热榜API
"""
import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.cognition.database import get_cognition_database
from backend.auth.models import UserDB

async def test_hot_ranking_api():
    """测试热榜API的核心逻辑"""
    print("开始测试热榜API...")
    
    # 获取数据库连接
    cognition_db = await get_cognition_database().__anext__()
    
    # 模拟用户
    mock_user = UserDB(id="test_user", username="test", email="<EMAIL>")
    
    try:
        # 测试获取当前时间段数据
        print("1. 测试获取今日数据...")
        current_cognitions = await cognition_db.get_cognitions_paginated(
            skip=0,
            limit=100,
            time_filter='day',
            source_filter='all',
            user_id=str(mock_user.id)
        )
        print(f"   获取到 {len(current_cognitions)} 条今日认知")
        
        # 测试获取对比数据
        print("2. 测试获取昨日数据...")
        comparison_cognitions = await cognition_db.get_cognitions_paginated(
            skip=0,
            limit=100,
            time_filter='yesterday',
            source_filter='all',
            user_id=str(mock_user.id)
        )
        print(f"   获取到 {len(comparison_cognitions)} 条昨日认知")
        
        # 测试话题统计
        print("3. 测试话题统计...")
        def calculate_topic_stats(cognitions):
            topic_stats = {}
            for cognition in cognitions:
                topics = []
                if cognition.get('primary_topic'):
                    topics.append(cognition['primary_topic'])
                if cognition.get('related_topic'):
                    if isinstance(cognition['related_topic'], list):
                        topics.extend(cognition['related_topic'])
                    else:
                        topics.append(cognition['related_topic'])
                
                for topic in topics:
                    if topic:
                        topic_stats[topic] = topic_stats.get(topic, 0) + 1
            return topic_stats
        
        current_topic_stats = calculate_topic_stats(current_cognitions)
        comparison_topic_stats = calculate_topic_stats(comparison_cognitions)
        
        print(f"   今日话题数: {len(current_topic_stats)}")
        print(f"   昨日话题数: {len(comparison_topic_stats)}")
        
        # 显示前5个热门话题
        if current_topic_stats:
            print("4. 前5个热门话题:")
            for i, (topic, count) in enumerate(sorted(current_topic_stats.items(), key=lambda x: x[1], reverse=True)[:5]):
                previous_count = comparison_topic_stats.get(topic, 0)
                change = count - previous_count
                print(f"   {i+1}. {topic}: {count} (+{change})")
        
        print("✅ 热榜API测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_hot_ranking_api())
    sys.exit(0 if result else 1)
