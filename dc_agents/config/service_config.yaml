llms:
  # 配置说明：
  # - model: 实际使用的模型ID或名称
  # - api_key: 访问API的密钥
  # - api_url: API端点URL
  # - is_reasoning_model: 是否是推理模型
  # - timeout: API调用超时时间
  # - input_price: 每1000个输入token的价格（美元）
  # - output_price: 每1000个输出token的价格（美元）

  o3-mini:
    model: o3-mini
    api_key: f847dd7d5eff4fc0bff57d061813a4ab
    api_url: ********************************
    api_version: 2023-03-15-preview
    is_reasoning_model: true
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: azure
    input_price: 0.001 # 每1000个输入token的价格（元）
    output_price: 0.004 # 每1000个输出token的价格（元）

  o3:
    model: o3
    api_key: 115ab1d4b7e9417ebc9b935cc59eb2d2
    api_url: ********************************
    api_version: 2023-03-15-preview
    is_reasoning_model: true
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: azure
    input_price: 0.01 # 每1000个输入token的价格（元）
    output_price: 0.04 # 每1000个输出token的价格（元）

  o4-mini:
    model: o4-mini
    api_key: 115ab1d4b7e9417ebc9b935cc59eb2d2
    api_url: ********************************
    api_version: 2023-03-15-preview
    is_reasoning_model: true
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: azure
    input_price: 0.001 # 每1000个输入token的价格（元）
    output_price: 0.004 # 每1000个输出token的价格（元）

  gpt-4.1-mini:
    model: gpt-4.1-mini
    api_key: f847dd7d5eff4fc0bff57d061813a4ab
    api_url: ********************************
    api_version: 2025-01-01-preview
    is_reasoning_model: false
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: azure
    input_price: 0.0004 # 每1000个输入token的价格（元）
    output_price: 0.0016 # 每1000个输出token的价格（元）

  claude-sonnet-4:
    model: anthropic/claude-sonnet-4
    api_key: 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
    api_url: https://openrouter.ai/api/v1
    is_reasoning_model: false
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: openai
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）

  claude-sonnet-4-thinking:
    model: anthropic/claude-sonnet-4
    api_key: 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
    api_url: https://openrouter.ai/api/v1
    is_reasoning_model: true
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: openai
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）

  claude-3.7-sonnet:
    model: anthropic/claude-3.7-sonnet 
    api_key: 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
    api_url: https://openrouter.ai/api/v1
    is_reasoning_model: false
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: openai
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）

  claude-3.7-sonnet-thinking:
    model: anthropic/claude-3.7-sonnet:thinking
    api_key: 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
    api_url: https://openrouter.ai/api/v1
    is_reasoning_model: true
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: openai
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）

  claude-3-7-sonnet-native:
    model: claude-3-7-sonnet-20250219
    api_key: coUNc4q6cIV82neL36YD6ET3DCLQ7v9ooE4225KYN6j42tqc
    api_url: https://api.myhispreadnlp.com/
    is_reasoning_model: true
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: anthropic_native
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）

  claude-3-7-sonnet-native-no-thinking:
    model: claude-3-7-sonnet-20250219
    api_key: coUNc4q6cIV82neL36YD6ET3DCLQ7v9ooE4225KYN6j42tqc
    api_url: https://api.myhispreadnlp.com/
    is_reasoning_model: false
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: anthropic_native
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）

  gemini-2.5-pro:
    model: google/gemini-2.5-pro-preview-03-25
    api_key: sk-kuZad7QdMaiAGwXfSqLt3tjg9dYjueS4Jg6a4tMXTFkrhhA9
    api_url: http://35.220.164.252:3888/v1
    is_reasoning_model: true
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: openai
    input_price: 0.001 # 每1000个输入token的价格（元）
    output_price: 0.01 # 每1000个输出token的价格（元）
  doubao-lite:
    model: doubao-lite-128k-240828
    api_key: 84174452-7831-4dc3-b8bc-53ffc9624ffd
    api_url: https://ark.cn-beijing.volces.com/api/v3
    is_reasoning_model: false
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: openai
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）
  grok-3:
    model: grok-3
    api_key: sk-kuZad7QdMaiAGwXfSqLt3tjg9dYjueS4Jg6a4tMXTFkrhhA9
    api_url: http://35.220.164.252:3888/v1
    is_reasoning_model: false
    is_mm_model: true
    timeout: 300.0 # 5分钟超时
    provider: openai
    input_price: 0.003 # 每1000个输入token的价格（元）
    output_price: 0.015 # 每1000个输出token的价格（元）

search_service:
  api_key: 42dfffd0b1acf6a6aec681c00320cd0f7e60c3f7
  api_url: https://google.serper.dev/search
  top_k: 10
  timeout: 120.0  # 添加搜索服务的超时设置


cognition_base_opensearch:
  endpoint: https://opensearch-o-00ctn8ofx5w3.escloud.ivolces.com:9200
  k: 10
  user: admin
  password: tfL6JYRev@bbUW3
  index: remote_sementic_dc
  search_pipeline: search_pipeline_dc
  search_field: qa
