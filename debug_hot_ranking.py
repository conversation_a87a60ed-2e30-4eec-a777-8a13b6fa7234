#!/usr/bin/env python3
"""
调试热榜API问题
"""
import sys
import os
import traceback

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def debug_hot_ranking():
    """调试热榜API"""
    try:
        from backend.cognition.database import get_cognition_database
        from backend.auth.models import UserDB
        
        print("1. 测试数据库连接...")
        cognition_db_gen = get_cognition_database()
        cognition_db = await cognition_db_gen.__anext__()
        print("   ✅ 数据库连接成功")
        
        # 模拟用户
        mock_user = UserDB(id="test_user", username="test", email="<EMAIL>")
        
        print("2. 测试获取认知数据...")
        current_cognitions = await cognition_db.get_cognitions_paginated(
            skip=0,
            limit=10,
            time_filter='day',
            source_filter='all',
            user_id=str(mock_user.id)
        )
        print(f"   ✅ 获取到 {len(current_cognitions)} 条认知")
        
        if current_cognitions:
            print("3. 测试第一条认知数据结构...")
            first_cognition = current_cognitions[0]
            print(f"   ID: {first_cognition.get('id', 'N/A')}")
            print(f"   Primary Topic: {first_cognition.get('primary_topic', 'N/A')}")
            print(f"   Raw At: {first_cognition.get('raw_at', 'N/A')}")
            print(f"   Keys: {list(first_cognition.keys())}")
        
        print("4. 测试话题统计...")
        topic_stats = {}
        for cognition in current_cognitions:
            topics = []
            if cognition.get('primary_topic'):
                topics.append(cognition['primary_topic'])
            if cognition.get('related_topic'):
                if isinstance(cognition['related_topic'], list):
                    topics.extend(cognition['related_topic'])
                else:
                    topics.append(cognition['related_topic'])
            
            for topic in topics:
                if topic:
                    topic_stats[topic] = topic_stats.get(topic, 0) + 1
        
        print(f"   ✅ 统计到 {len(topic_stats)} 个话题")
        if topic_stats:
            top_topics = sorted(topic_stats.items(), key=lambda x: x[1], reverse=True)[:3]
            for topic, count in top_topics:
                print(f"   - {topic}: {count}")
        
        print("5. 测试热门认知排序...")
        sorted_cognitions = sorted(current_cognitions, key=lambda x: x.get('raw_at', ''), reverse=True)
        print(f"   ✅ 排序完成，最新认知时间: {sorted_cognitions[0].get('raw_at', 'N/A') if sorted_cognitions else 'N/A'}")
        
        print("✅ 所有测试通过，API应该能正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import asyncio
    result = asyncio.run(debug_hot_ranking())
    sys.exit(0 if result else 1)
