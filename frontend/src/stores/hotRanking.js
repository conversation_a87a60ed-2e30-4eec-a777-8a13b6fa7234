import { cognitionAPI } from '@/api/cognition'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useHotRankingStore = defineStore('hotRanking', () => {
  // 状态
  const hotTopics = ref([])
  const hotCognitions = ref([])
  const loading = ref(false)
  const error = ref('')
  const lastUpdateTime = ref(null)
  const currentTimeFilter = ref('day')

  // 性能监控
  const performanceStats = ref({
    apiCallCount: 0,
    cacheHitCount: 0,
    averageLoadTime: 0,
    lastLoadTime: 0
  })
  
  // 缓存相关 - 优化为更短的缓存时间以提升实时性
  const cacheTimeout = 5 * 60 * 1000 // 5分钟缓存，更适合实时热榜
  const cache = ref(new Map()) // 缓存不同时间筛选的数据
  
  // 从localStorage恢复缓存
  const initCache = () => {
    try {
      const storedCache = localStorage.getItem('hotRankingCache')
      if (storedCache) {
        const parsedCache = JSON.parse(storedCache)
        // 验证缓存是否过期
        for (const [key, value] of Object.entries(parsedCache)) {
          if (Date.now() - value.timestamp < cacheTimeout) {
            cache.value.set(key, value)
          }
        }
        console.log('热榜缓存已从localStorage恢复:', cache.value.size, '条')
      }
    } catch (err) {
      console.warn('恢复热榜缓存失败:', err)
    }
  }
  
  // 保存缓存到localStorage
  const saveCache = () => {
    try {
      const cacheObj = Object.fromEntries(cache.value.entries())
      localStorage.setItem('hotRankingCache', JSON.stringify(cacheObj))
    } catch (err) {
      console.warn('保存热榜缓存失败:', err)
    }
  }
  
  // 计算属性
  const isDataStale = computed(() => {
    if (!lastUpdateTime.value) return true
    return Date.now() - lastUpdateTime.value > cacheTimeout
  })
  


  // 获取热榜数据
  const fetchHotRanking = async (timeFilter = 'day', forceRefresh = false, includeComparison = true) => {
    const startTime = performance.now()
    const cacheKey = `hot-ranking-${timeFilter}`

    // 初始化时尝试恢复缓存
    if (cache.value.size === 0) {
      initCache()
    }

    // 智能缓存策略：优先显示缓存数据，然后后台更新
    if (!forceRefresh && cache.value.has(cacheKey)) {
      const cachedData = cache.value.get(cacheKey)
      const cacheAge = Date.now() - cachedData.timestamp
      const isCacheValid = cacheAge < cacheTimeout

      if (isCacheValid) {
        // 缓存有效，立即显示
        hotTopics.value = cachedData.hotTopics
        hotCognitions.value = cachedData.hotCognitions
        lastUpdateTime.value = cachedData.timestamp
        currentTimeFilter.value = timeFilter

        // 更新性能统计
        performanceStats.value.cacheHitCount++
        performanceStats.value.lastLoadTime = performance.now() - startTime

        // 异步预加载其他时间筛选的数据
        setTimeout(() => preloadOtherFilters(timeFilter), 50)

        return
      } else if (cacheAge < cacheTimeout * 2) {
        // 缓存过期但不太久，先显示缓存数据，然后后台刷新
        hotTopics.value = cachedData.hotTopics
        hotCognitions.value = cachedData.hotCognitions
        lastUpdateTime.value = cachedData.timestamp
        currentTimeFilter.value = timeFilter

        // 更新性能统计
        performanceStats.value.cacheHitCount++
        performanceStats.value.lastLoadTime = performance.now() - startTime

        // 后台静默刷新
        setTimeout(() => fetchHotRanking(timeFilter, true, includeComparison), 100)
        return
      }
    }

    // 只有在没有缓存或缓存太旧时才显示loading
    if (!cache.value.has(cacheKey)) {
      loading.value = true
    }
    error.value = ''

    try {
      // 使用新的热榜API，一次性获取所有需要的数据
      const params = {
        time_filter: timeFilter,
        include_comparison: includeComparison,
        topic_limit: 10,
        cognition_limit: 10
      }

      const response = await cognitionAPI.getHotRanking(params)

      // 更新性能统计
      performanceStats.value.apiCallCount++
      const loadTime = performance.now() - startTime
      performanceStats.value.lastLoadTime = loadTime
      performanceStats.value.averageLoadTime =
        (performanceStats.value.averageLoadTime * (performanceStats.value.apiCallCount - 1) + loadTime) /
        performanceStats.value.apiCallCount

      if (response && response.hot_topics && response.hot_cognitions) {
        // 直接使用后端处理好的数据
        const processedTopics = response.hot_topics
        const processedCognitions = response.hot_cognitions

        // 更新状态
        hotTopics.value = processedTopics
        hotCognitions.value = processedCognitions
        lastUpdateTime.value = Date.now()
        currentTimeFilter.value = timeFilter

        // 缓存数据
        cache.value.set(cacheKey, {
          hotTopics: processedTopics,
          hotCognitions: processedCognitions,
          timestamp: Date.now()
        })

        // 异步保存到localStorage，不阻塞UI
        setTimeout(() => {
          saveCache()
          // 限制缓存大小
          if (cache.value.size > 6) {
            const oldestKey = Array.from(cache.value.keys())[0]
            cache.value.delete(oldestKey)
            saveCache()
          }
        }, 0)

        // 异步预加载其他时间筛选的数据
        setTimeout(() => preloadOtherFilters(timeFilter), 50)

        console.log(`热榜数据已更新 - ${timeFilter}:`, {
          topics: processedTopics.length,
          cognitions: processedCognitions.length,
          comparisonEnabled: response.comparison_enabled,
          loadTime: `${loadTime.toFixed(2)}ms`
        })
      } else {
        throw new Error('API响应数据格式错误')
      }
    } catch (err) {
      console.error('获取热榜数据失败:', err)
      error.value = err.message || '获取热榜数据失败，请稍后重试'

      // 如果有缓存数据，继续使用（即使过期）
      if (cache.value.has(cacheKey)) {
        const cachedData = cache.value.get(cacheKey)
        hotTopics.value = cachedData.hotTopics
        hotCognitions.value = cachedData.hotCognitions
        lastUpdateTime.value = cachedData.timestamp
        currentTimeFilter.value = timeFilter
      } else {
        hotTopics.value = []
        hotCognitions.value = []
      }
    } finally {
      loading.value = false
    }
  }
  
  // 强制刷新数据
  const refreshData = async () => {
    await fetchHotRanking(currentTimeFilter.value, true)
  }

  // 获取带对比数据的热榜（新增方法）
  const fetchHotRankingWithComparison = async (timeFilter = 'day', forceRefresh = false) => {
    return await fetchHotRanking(timeFilter, forceRefresh, true)
  }
  
  // 清空缓存
  const clearCache = () => {
    cache.value.clear()
    try {
      localStorage.removeItem('hotRankingCache')
    } catch (err) {
      console.warn('清理localStorage缓存失败:', err)
    }
    console.log('热榜缓存已清空(包括localStorage)')
  }
  
  // 获取缓存状态信息
  const getCacheInfo = () => {
    return {
      cacheSize: cache.value.size,
      lastUpdate: lastUpdateTime.value,
      isStale: isDataStale.value,
      currentFilter: currentTimeFilter.value
    }
  }

  // 获取性能统计信息
  const getPerformanceStats = () => {
    const cacheHitRate = performanceStats.value.apiCallCount > 0 ?
      (performanceStats.value.cacheHitCount / (performanceStats.value.apiCallCount + performanceStats.value.cacheHitCount) * 100).toFixed(2) : 0

    return {
      ...performanceStats.value,
      cacheHitRate: `${cacheHitRate}%`,
      averageLoadTime: `${performanceStats.value.averageLoadTime.toFixed(2)}ms`,
      lastLoadTime: `${performanceStats.value.lastLoadTime.toFixed(2)}ms`
    }
  }
  
  // 智能预加载其他时间筛选的数据（高性能版）
  const preloadOtherFilters = async (currentFilter) => {
    // 优先级预加载：根据用户使用习惯调整顺序
    const priorityOrder = currentFilter === 'day' ? ['week', 'month'] :
                         currentFilter === 'week' ? ['day', 'month'] :
                         ['day', 'week']

    // 串行预加载，避免同时发起多个请求影响主要功能
    for (const filter of priorityOrder) {
      const cacheKey = `hot-ranking-${filter}`

      // 检查是否已有有效缓存
      if (cache.value.has(cacheKey)) {
        const cachedData = cache.value.get(cacheKey)
        if (Date.now() - cachedData.timestamp < cacheTimeout) {
          continue // 缓存仍然有效，跳过
        }
      }

      try {
        // 静默预加载，使用新的热榜API
        const params = {
          time_filter: filter,
          include_comparison: true,
          topic_limit: 10,
          cognition_limit: 10
        }

        const response = await cognitionAPI.getHotRanking(params)

        if (response && response.hot_topics && response.hot_cognitions) {
          // 缓存预加载的数据
          cache.value.set(cacheKey, {
            hotTopics: response.hot_topics,
            hotCognitions: response.hot_cognitions,
            timestamp: Date.now()
          })

          console.log(`预加载${filter}数据完成`)
        }

        // 添加小延迟，避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (err) {
        console.warn(`预加载${filter}数据失败:`, err.message)
        // 预加载失败不影响主要功能，继续下一个
      }
    }

    // 异步保存缓存
    setTimeout(() => {
      saveCache()
      console.log('智能预加载完成，总缓存大小:', cache.value.size)
    }, 0)
  }
  
  // 兼容旧的preloadData方法
  const preloadData = async () => {
    await preloadOtherFilters(currentTimeFilter.value)
  }
  
  // 重置状态
  const reset = () => {
    hotTopics.value = []
    hotCognitions.value = []
    loading.value = false
    error.value = ''
    lastUpdateTime.value = null
    currentTimeFilter.value = 'day'
    clearCache()
  }
  
      return {
    // 状态
    hotTopics,
    hotCognitions,
    loading,
    error,
    lastUpdateTime,
    currentTimeFilter,
    performanceStats,

    // 计算属性
    isDataStale,

    // 方法
    fetchHotRanking,
    fetchHotRankingWithComparison,
    refreshData,
    clearCache,
    getCacheInfo,
    getPerformanceStats,
    preloadData,
    preloadOtherFilters,
    initCache,
    saveCache,
    reset,


  }
}) 