<template>
  <div class="note-select-container">
    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <v-btn 
        color="primary" 
        :disabled="selectedNodes.length === 0 || selectedNodes.length > 5"
        @click="handleExtractCognition"
        :loading="isExtracting"
        prepend-icon="mdi-brain"
      >
        提取认知 ({{ selectedNodes.length }}/5)
      </v-btn>
      
      <v-btn 
        color="success" 
        @click="handleViewCognitions"
        prepend-icon="mdi-database-eye"
      >
        查看认知
      </v-btn>
      
      <v-btn 
        v-if="selectedNodes.length > 0"
        color="info" 
        size="small"
        @click="clearSelection"
      >
        清空选择
      </v-btn>
    </div>

    <!-- 文档树区域 -->
    <div class="tree-container">
      <v-treeview
        :items="treeData"
        item-key="id"
        item-title="title"
        item-children="children"
        selectable
        return-object
        :selected="selectedNodeIds"
        @update:selected="handleSelectionChange"
        class="note-tree"
      >
        <template v-slot:prepend="{ item }">
          <v-icon v-if="item.has_children" color="amber">
            mdi-folder
          </v-icon>
          <v-icon v-else color="cyan">
            mdi-file-document
          </v-icon>
        </template>
        
        <template v-slot:append="{ item }">
          <span class="node-info text-caption text-grey">
            {{ formatDate(item.updated_at) }}
          </span>
        </template>
      </v-treeview>
    </div>

    <!-- 选择状态提示 -->
    <div v-if="selectedNodes.length > 0" class="selection-info">
      <v-alert
        :title="`已选择 ${selectedNodes.length} 个笔记`"
        :type="selectedNodes.length > 5 ? 'warning' : 'info'"
        :text="selectedNodes.length > 5 ? '最多只能选择5个笔记进行认知提取' : ''"
        variant="tonal"
        closable
      />
      
      <div class="selected-list">
        <v-chip
          v-for="node in selectedNodes.slice(0, 5)"
          :key="node.id"
          :color="selectedNodes.length > 5 ? 'warning' : 'primary'"
          size="small"
          closable
          @click:close="removeSelection(node.id)"
        >
          {{ node.title }}
        </v-chip>
        <v-chip v-if="selectedNodes.length > 5" color="error" size="small">
          +{{ selectedNodes.length - 5 }} 个超出限制
        </v-chip>
      </div>
    </div>

    <!-- 认知提取确认对话框 -->
    <ExtractCognitionDialog
      v-model:visible="showExtractDialog"
      :selected-documents="selectedValidNodes"
      @confirmed="performExtraction"
    />

    <!-- 认知列表对话框 -->
    <DocumentCognitionList
      v-model:visible="showCognitionList"
    />

    <!-- 消息提示 -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      timeout="3000"
    >
      {{ snackbarMessage }}
    </v-snackbar>

    <!-- 确认对话框 -->
    <v-dialog
      v-model="showConfirmDialog"
      max-width="400"
    >
      <v-card>
        <v-card-title>{{ confirmTitle }}</v-card-title>
        <v-card-text>{{ confirmMessage }}</v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showConfirmDialog = false">取消</v-btn>
          <v-btn color="primary" @click="handleConfirm">{{ confirmButtonText }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { documentsApi, cognitionsApi, DocumentTreeNode } from '@/api/documents'
import ExtractCognitionDialog from './ExtractCognitionDialog.vue'
import DocumentCognitionList from './DocumentCognitionList.vue'

// 数据
const treeData = ref<DocumentTreeNode[]>([])
const selectedNodes = ref<DocumentTreeNode[]>([])
const selectedNodeIds = ref<string[]>([])
const isExtracting = ref(false)
const showExtractDialog = ref(false)
const showCognitionList = ref(false)

// 消息系统
const showSnackbar = ref(false)
const snackbarMessage = ref('')
const snackbarColor = ref('error')

// 确认对话框
const showConfirmDialog = ref(false)
const confirmTitle = ref('')
const confirmMessage = ref('')
const confirmButtonText = ref('确定')
const confirmCallback = ref<(() => void) | null>(null)

// 计算属性
const selectedValidNodes = computed(() => {
  return selectedNodes.value.filter(node => !node.has_children).slice(0, 5)
})

// 消息提示方法
const showMessage = (message: string, color: string = 'error') => {
  snackbarMessage.value = message
  snackbarColor.value = color
  showSnackbar.value = true
}

// 确认对话框方法
const showConfirm = (title: string, message: string, buttonText: string = '确定'): Promise<boolean> => {
  return new Promise((resolve) => {
    confirmTitle.value = title
    confirmMessage.value = message
    confirmButtonText.value = buttonText
    showConfirmDialog.value = true
    confirmCallback.value = () => {
      showConfirmDialog.value = false
      resolve(true)
    }
  })
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
}

// 方法
const loadDocumentTree = async () => {
  try {
    const data = await documentsApi.getDocumentTree()
    treeData.value = buildTree(data)
  } catch (error) {
    console.error('加载文档树失败:', error)
    showMessage('加载文档树失败')
  }
}

const buildTree = (flatData: DocumentTreeNode[]): DocumentTreeNode[] => {
  const nodeMap = new Map<string, DocumentTreeNode & { children?: DocumentTreeNode[] }>()
  const rootNodes: DocumentTreeNode[] = []

  // 创建节点映射
  flatData.forEach(item => {
    nodeMap.set(item.id, { ...item, children: [] })
  })

  // 构建树结构
  flatData.forEach(item => {
    const node = nodeMap.get(item.id)!
    if (item.parent_id && nodeMap.has(item.parent_id)) {
      const parent = nodeMap.get(item.parent_id)!
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(node)
    } else {
      rootNodes.push(node)
    }
  })

  return rootNodes
}

const handleSelectionChange = (selectedItems: DocumentTreeNode[]) => {
  // 过滤掉文件夹，只保留文件
  const validNodes = selectedItems.filter(node => !node.has_children)
  selectedNodes.value = validNodes
  selectedNodeIds.value = validNodes.map(node => node.id)
  
  // 如果选择超过5个，给出提示
  if (validNodes.length > 5) {
    showMessage('最多只能选择5个笔记进行认知提取', 'warning')
  }
}

const removeSelection = (nodeId: string) => {
  selectedNodes.value = selectedNodes.value.filter(node => node.id !== nodeId)
  selectedNodeIds.value = selectedNodes.value.map(node => node.id)
}

const clearSelection = () => {
  selectedNodes.value = []
  selectedNodeIds.value = []
}

const handleExtractCognition = () => {
  if (selectedValidNodes.value.length === 0) {
    showMessage('请选择要提取认知的笔记', 'warning')
    return
  }
  
  if (selectedValidNodes.value.length > 5) {
    showMessage('最多只能选择5个笔记进行认知提取', 'warning')
    return
  }
  
  showExtractDialog.value = true
}

const performExtraction = async () => {
  if (selectedValidNodes.value.length === 0) return
  
  isExtracting.value = true
  try {
    const documentIds = selectedValidNodes.value.map(node => node.id)
    const result = await cognitionsApi.extractCognitions(documentIds)
    
    if (result.success) {
      showMessage(result.message, 'success')
      
      // 显示提取结果详情
      const details = [
        `处理文档: ${result.total_documents} 个`,
        `提取认知: ${result.total_cognitions} 个`
      ]
      
      if (result.synthesized_cognition) {
        details.push('✨ 已生成综合认知')
      }
      
      await showConfirm(
        '认知提取完成',
        details.join('\n'),
        '查看认知'
      )
      
      showCognitionList.value = true
      
      // 清空选择
      clearSelection()
    } else {
      showMessage(result.message || '认知提取失败')
    }
  } catch (error) {
    console.error('认知提取失败:', error)
    showMessage('认知提取失败，请重试')
  } finally {
    isExtracting.value = false
    showExtractDialog.value = false
  }
}

const handleViewCognitions = () => {
  showCognitionList.value = true
}

const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })
  }
}

// 生命周期
onMounted(() => {
  loadDocumentTree()
})
</script>

<style scoped>
.note-select-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: rgb(var(--v-theme-surface-variant));
  border-radius: 8px;
}

.tree-container {
  flex: 1;
  overflow: auto;
  border: 1px solid rgb(var(--v-theme-outline-variant));
  border-radius: 8px;
  padding: 12px;
  background: rgb(var(--v-theme-surface));
}

.note-tree {
  --v-treeview-node-height: 36px;
}

.node-info {
  margin-left: auto;
  font-size: 12px;
}

.selection-info {
  padding: 16px;
  background: rgb(var(--v-theme-surface-variant));
  border-radius: 8px;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }
  
  .note-tree {
    --v-treeview-node-height: 40px;
  }
  
  .node-info {
    display: none;
  }
}
</style> 