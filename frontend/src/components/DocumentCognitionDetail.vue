<template>
  <v-dialog v-model="dialogVisible" max-width="800px">
    <v-card class="cognition-detail">
      <!-- 渐变头部栏 -->
      <div class="dialog-gradient-bar"></div>
      
      <!-- 头部 -->
      <v-card-title class="detail-header">
        <div class="header-content">
          <!-- 作者信息 -->
          <div class="author-info">
            <div class="author-avatar">
              {{ getAuthorInitial() }}
            </div>
            <div class="author-details">
              <div class="author-name">{{ cognition?.user_name || '未知用户' }}</div>
              <div class="publish-time">{{ formatDate(cognition?.created_at) }}</div>
            </div>
          </div>
          
          <!-- 认知类型标签 -->
          <div class="cognition-badges">
            <v-chip
              v-if="cognition?.is_synthesized"
              size="small"
              color="primary"
              variant="tonal"
              class="synthesis-chip"
            >
              <v-icon start size="14">mdi-merge</v-icon>
              综合认知
            </v-chip>
            
            <!-- 验证/类型标签 -->
            <v-chip
              v-if="cognition?.source === 'Document Extraction' || cognition?.tag === '笔记'"
              size="small"
              color="teal"
              variant="tonal"
              class="verification-chip"
            >
              <v-icon start size="14">mdi-file-document-edit</v-icon>
              {{ cognition?.tag === '笔记' ? '笔记' : '文档提取' }}
            </v-chip>
          </div>
        </div>
        
        <v-btn icon variant="text" @click="handleClose" class="close-btn">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text class="detail-content" v-if="cognition">
        <!-- 话题标签区域 -->
        <div class="section" v-if="hasTopics">
          <h3 class="section-title">
            <v-icon color="primary">mdi-tag-multiple</v-icon>
            话题标签
          </h3>
          <div class="topics-container">
            <!-- Primary Topic -->
            <div 
              v-if="cognition.primary_topic" 
              class="topic-tag primary-topic"
            >
              <v-icon start size="14">mdi-tag</v-icon>
              {{ formatTopicName(cognition.primary_topic) }}
            </div>
            
            <!-- Related Topics -->
            <div 
              v-for="topic in cognition.related_topic || []" 
              :key="topic"
              class="topic-tag related-topic"
            >
              <v-icon start size="14">mdi-tag-outline</v-icon>
              {{ formatTopicName(topic) }}
            </div>
          </div>
        </div>
        
        <!-- 认知摘要 - 移到最上面 -->
        <div class="section" v-if="cognition.abstract_zh">
          <h3 class="section-title">
            <v-icon color="deep-purple">mdi-lightbulb-variant-outline</v-icon>
            认知摘要
          </h3>
          <div class="content-text abstract-text">
            <MarkdownRenderer 
              :content="cognition.abstract_zh" 
              :is-complete="true" 
            />
          </div>
        </div>
        
        <!-- 核心问题 -->
        <div class="section">
          <h3 class="section-title">
            <v-icon color="primary">mdi-help-circle-outline</v-icon>
            问题
          </h3>
          <div class="content-text question-text">
            <MarkdownRenderer 
              :content="cognition.question_zh || ''" 
              :is-complete="true" 
            />
          </div>
        </div>

        <!-- 答案 -->
        <div class="section" v-if="cognition.answer_zh">
          <h3 class="section-title">
            <v-icon color="green">mdi-lightbulb-outline</v-icon>
            洞察
          </h3>
          <div class="content-text answer-text">
            <MarkdownRenderer 
              :content="cognition.answer_zh" 
              :is-complete="true" 
            />
          </div>
        </div>

        <!-- 思考过程 -->
        <div class="section" v-if="cognition.think_zh">
          <h3 class="section-title">
            <v-icon color="purple">mdi-brain</v-icon>
            思考过程
          </h3>
          <div class="content-text think-text">
            <MarkdownRenderer 
              :content="cognition.think_zh" 
              :is-complete="true" 
            />
          </div>
        </div>

        <!-- 元数据信息 -->
        <div class="section">
          <h3 class="section-title">
            <v-icon color="info">mdi-information-outline</v-icon>
            认知信息
          </h3>
          <div class="meta-info">
            <!-- 来源文档 -->
            <div class="meta-item" v-if="cognition.source_document_titles && cognition.source_document_titles.length > 0">
              <h4 class="meta-title">
                <v-icon color="blue">mdi-file-document-multiple-outline</v-icon>
                来源文档
              </h4>
              <div class="source-documents">
                <v-chip
                  v-for="(title, index) in cognition.source_document_titles"
                  :key="index"
                  color="blue"
                  variant="tonal"
                  size="small"
                  class="mr-2 mb-2"
                >
                  <v-icon start size="16">mdi-file-document</v-icon>
                  {{ title }}
                </v-chip>
              </div>
            </div>
          </div>
        </div>
      </v-card-text>
      
      <!-- 操作区域 -->
      <v-card-actions class="action-area">
        <!-- 上传状态显示 -->
        <div class="upload-status-info">
          <v-chip
            v-if="cognition?.upload_status === 'not_uploaded'"
            size="small"
            color="grey"
            variant="tonal"
          >
            <v-icon start size="16">mdi-cloud-off</v-icon>
            未上传
          </v-chip>
          <v-chip
            v-else-if="cognition?.upload_status === 'pending_review'"
            size="small"
            color="orange"
            variant="tonal"
          >
            <v-icon start size="16">mdi-clock-outline</v-icon>
            审核中
          </v-chip>
          <v-chip
            v-else-if="cognition?.upload_status === 'approved'"
            size="small"
            color="green"
            variant="tonal"
          >
            <v-icon start size="16">mdi-check-circle</v-icon>
            已上传
          </v-chip>
          <v-chip
            v-else-if="cognition?.upload_status === 'rejected'"
            size="small"
            color="red"
            variant="tonal"
          >
            <v-icon start size="16">mdi-close-circle</v-icon>
            审核不通过
          </v-chip>
        </div>
        
        <v-spacer />
        
        <!-- 上传认知按钮 -->
        <v-btn
          v-if="cognition?.upload_status === 'not_uploaded'"
          color="primary"
          variant="elevated"
          @click="showUploadDialog = true"
          size="small"
        >
          <v-icon start>mdi-cloud-upload</v-icon>
          上传认知
        </v-btn>
        
        <!-- 审核意见显示 -->
        <v-btn
          v-if="cognition?.upload_status === 'rejected' && cognition?.review_comment"
          color="grey"
          variant="text"
          @click="showReviewDialog = true"
          size="small"
        >
          <v-icon start>mdi-message-outline</v-icon>
          查看审核意见
        </v-btn>
      </v-card-actions>
      
    </v-card>
    
    <!-- 上传确认对话框 -->
    <v-dialog v-model="showUploadDialog" max-width="400">
      <v-card>
        <v-card-title>确认上传认知</v-card-title>
        <v-card-text>
          <p>您确定要将此认知上传到认知平台供其他用户查看吗？</p>
          <v-alert type="info" variant="tonal" class="mt-3">
            上传后的认知需要管理员审核通过才会公开展示。
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" @click="showUploadDialog = false">取消</v-btn>
          <v-btn
            color="primary"
            @click="handleUploadCognition"
            :loading="uploadLoading"
          >
            确认上传
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
    <!-- 审核意见对话框 -->
    <v-dialog v-model="showReviewDialog" max-width="500">
      <v-card>
        <v-card-title>审核意见</v-card-title>
        <v-card-text>
          <div class="review-info">
            <div class="review-status">
              <v-chip
                :color="cognition?.upload_status === 'approved' ? 'green' : 'red'"
                variant="tonal"
              >
                {{ cognition?.upload_status === 'approved' ? '审核通过' : '审核不通过' }}
              </v-chip>
            </div>
            <div class="review-time" v-if="cognition?.reviewed_at">
              审核时间：{{ formatDate(cognition.reviewed_at) }}
            </div>
            <div class="review-comment" v-if="cognition?.review_comment">
              <h4>审核意见：</h4>
              <p>{{ cognition.review_comment }}</p>
            </div>
            <div class="reviewer" v-if="cognition?.reviewer_name">
              审核人：{{ cognition.reviewer_name }}
            </div>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="showReviewDialog = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { DocumentCognition, cognitionsApi } from '@/api/documents'
import MarkdownRenderer from './MarkdownRenderer.vue'

// Props
interface DocumentCognitionDetailProps {
  visible: boolean
  cognition: DocumentCognition | null
}

const props = defineProps<DocumentCognitionDetailProps>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'close': []
  'upload-success': []
}>()

// 响应式变量
const showUploadDialog = ref(false)
const showReviewDialog = ref(false)
const uploadLoading = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

const handleUploadCognition = async () => {
  if (!props.cognition) return
  
  uploadLoading.value = true
  try {
    const result = await cognitionsApi.uploadCognition(props.cognition.id)
    if (result.success) {
      // 创建一个新的认知对象来更新状态
      const updatedCognition = {
        ...props.cognition,
        upload_status: result.upload_status,
        upload_requested_at: result.upload_requested_at
      }
      
      // 更新本地认知对象
      Object.assign(props.cognition, updatedCognition)
      
      showUploadDialog.value = false
      emit('upload-success')
      
      // 显示成功消息
      console.log('认知上传成功:', result.message)
    } else {
      throw new Error(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传认知失败:', error)
    
    // 显示错误消息
    let errorMessage = '上传认知失败'
    if (error.response && error.response.data && error.response.data.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }
    
    console.error('错误详情:', errorMessage)
    alert(errorMessage)
  } finally {
    uploadLoading.value = false
  }
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '未知时间'
  
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getAuthorInitial = () => {
  if (!props.cognition || !props.cognition.user_id) return 'A'
  return props.cognition.user_id.charAt(0).toUpperCase()
}

const hasTopics = computed(() => {
  return props.cognition && (props.cognition.primary_topic || (props.cognition.related_topic && props.cognition.related_topic.length > 0))
})

const formatTopicName = (topic: string) => {
  return topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<style scoped>
.cognition-detail {
  background: linear-gradient(145deg, #ffffff 0%, #fafbff 100%);
  border-radius: 20px !important;
  overflow: hidden;
  box-shadow: 0 24px 48px rgba(102, 103, 171, 0.15);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.dialog-gradient-bar {
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.detail-header {
  background: transparent;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  padding: 24px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex-shrink: 0;
  flex: 1;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.author-details {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.publish-time {
  font-size: 14px;
  color: #7f8c8d;
}

.cognition-badges {
  display: flex;
  gap: 8px;
}

.synthesis-chip {
  font-weight: 600;
  border-radius: 8px;
}

.verification-chip {
  font-weight: 600;
  border-radius: 8px;
}

.close-btn {
  flex-shrink: 0;
}

.detail-content {
  padding: 24px !important;
  overflow-y: auto;
  flex-grow: 1;
}

.section {
  margin-bottom: 32px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.content-text {
  font-size: 15px;
  line-height: 1.7;
  color: #34495e;
}

.question-text {
  background: rgba(52, 152, 219, 0.08);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #3498db;
  font-style: italic;
}

.answer-text {
  background: rgba(46, 204, 113, 0.08);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #2ecc71;
}

.think-text {
  background: rgba(155, 89, 182, 0.08);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #9b59b6;
  font-style: italic;
  color: #5a5a7d;
}

.abstract-text {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  padding: 24px;
  border-radius: 16px;
  border-left: 4px solid #667eea;
  font-weight: 500;
  color: #4a5568;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.source-documents {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.topics-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.topic-tag {
  padding: 8px 16px;
  border-radius: 16px;
  background-color: rgba(102, 126, 234, 0.08);
  color: #667eea;
  font-weight: 600;
}

.primary-topic {
  background-color: rgba(52, 152, 219, 0.08);
  border-left: 4px solid #3498db;
}

.related-topic {
  background-color: rgba(155, 89, 182, 0.08);
  border-left: 4px solid #9b59b6;
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.meta-item {
  flex: 1;
}

.meta-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cognition-detail {
    margin: 16px;
    max-width: calc(100vw - 32px);
    max-height: calc(100vh - 32px);
    border-radius: 16px !important;
  }
  
  .detail-header {
    padding: 20px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .author-info {
    width: 100%;
  }
  
  .detail-content {
    padding: 20px !important;
  }
  
  .section {
    margin-bottom: 24px;
  }
  
  .section-title {
    font-size: 16px;
  }
  
  .content-text {
    font-size: 14px;
  }
}

/* 统一的关闭按钮样式 */
.v-btn.v-btn--icon {
  color: #95a5a6 !important;
  transition: all 0.2s ease;
}

.v-btn.v-btn--icon:hover {
  color: #667eea !important;
  background-color: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
}
</style> 