<template>
  <div class="admin-sidebar" :class="{ 'collapsed': isCollapsed }">
    <div class="sidebar-header">
      <h3 v-if="!isCollapsed">管理控制台</h3>
      <button class="toggle-btn" @click="toggleSidebar">
        <i class="fas" :class="isCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
      </button>
    </div>
    
    <div class="sidebar-menu">
      <div 
        v-for="(item, index) in menuItems" 
        :key="index"
        class="menu-item"
        :class="{ 'active': activeMenu === item.key }"
        @click="selectMenu(item.key)"
      >
        <i :class="'fas ' + item.icon"></i>
        <span v-if="!isCollapsed">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from 'vue';

// 菜单数据
const menuItems = [
  { key: 'users', label: '用户管理', icon: 'fas fa-users' },
  { key: 'invite-codes', label: '邀请码管理', icon: 'fas fa-ticket-alt' },
  { key: 'cognition-review', label: '认知上传审核', icon: 'fas fa-brain' },
  { key: 'token-usage', label: 'Token用量统计', icon: 'fas fa-chart-line' },
  { key: 'token-sessions', label: '会话Token详情', icon: 'fas fa-list-alt' },
  { key: 'user-token-management', label: '用户Token管理', icon: 'fas fa-coins' },
  { key: 'settings', label: '系统设置', icon: 'fas fa-cog' }
];

const isCollapsed = ref(false);
const activeMenu = ref('users');

// 定义事件
const emit = defineEmits(['menu-change']);

// 切换侧边栏展开/收起状态
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 选择菜单项
const selectMenu = (key: string) => {
  activeMenu.value = key;
  emit('menu-change', key);
};
</script>

<style scoped>
.admin-sidebar {
  display: flex;
  flex-direction: column;
  width: 240px;
  height: 100%;
  background-color: #1e1e2d;
  color: #a2a3b7;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.admin-sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 15px;
  border-bottom: 1px solid #2e2e40;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.toggle-btn {
  background: transparent;
  border: none;
  color: #a2a3b7;
  cursor: pointer;
  padding: 5px;
}

.sidebar-menu {
  padding: 15px 0;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background-color: #282838;
  color: #ffffff;
}

.menu-item.active {
  background-color: #3699ff;
  color: #ffffff;
}

.menu-item i {
  font-size: 16px;
  width: 24px;
  text-align: center;
}

.menu-item span {
  margin-left: 12px;
  font-size: 14px;
  white-space: nowrap;
}
</style> 