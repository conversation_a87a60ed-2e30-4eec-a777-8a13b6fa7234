<template>
    <div 
      class="tree-view"
      @dragover.prevent="onTreeDragOver"
      @drop="onTreeDrop"
      @dragleave="onTreeDragLeave"
    >
      <!-- 添加创建根节点按钮 -->
      <div v-if="!parentId" class="create-root-btn">
        <button class="tree-node-btn add-btn" @click="addRootNote">
          <span class="btn-icon">+</span>
          <span class="btn-text">新建笔记</span>
        </button>
      </div>
      
      <!-- 根节点新增输入框 -->
      <div v-if="!parentId && showRootInput" class="tree-node new-node">
        <div class="tree-node-content">
          <div class="tree-node-header">
            <span class="tree-node-toggle">•</span>
            <input
              v-model="newNodeName"
              class="tree-node-input"
              placeholder="输入笔记名称"
              @blur="confirmAddRootChild"
              @keyup.enter="confirmAddRootChild"
              @keyup.escape="cancelAddRootChild"
              ref="rootNodeInput"
              @click.stop
            />
          </div>
        </div>
      </div>
      
      <div
        v-for="(node, index) in currentLevelNodes"
        :key="node.id"
        class="tree-node"
        :class="{ 'is-root': !node.parentId }"
      >
        <!-- 拖拽插入位置指示器 - 节点上方 -->
        <div 
          v-if="dropIndicator.show && dropIndicator.nodeId === node.id && dropIndicator.position === 'before'"
          class="drop-indicator before"
        >
          <div class="drop-line"></div>
          <div class="drop-text">在此处插入</div>
        </div>
        
        <div
          class="tree-node-content"
          draggable="true"
          @dragstart="onDragStart(node, $event)"
          @dragover.prevent="onDragOver(node, $event)"
          @dragleave="onDragLeave(node, $event)"
          @drop="onDrop(node, $event)"
          @contextmenu.prevent="showContextMenu(node, $event)"
          :class="{
            'is-dragging': draggingNode?.id === node.id,
            'can-drop-inside': dragOverState.nodeId === node.id && dragOverState.position === 'inside',
            'can-drop-before': dragOverState.nodeId === node.id && dragOverState.position === 'before', 
            'can-drop-after': dragOverState.nodeId === node.id && dragOverState.position === 'after',
            'is-selected': selectedNodeId === node.id
          }"
          :data-node-id="node.id"
        >
          <div class="tree-node-header">
            <!-- 多选框（仅在显示多选框时显示） -->
            <input 
              v-if="showCheckboxes"
              type="checkbox"
              class="node-checkbox"
              :checked="isNodeSelected(node.id)"
              @change="toggleNodeSelection(node)"
              @click.stop
            />
            
            <span 
              class="tree-node-toggle" 
              @click.stop="toggleNode(node)"
            >
              <template v-if="hasChildren(node)">
                <v-icon 
                  size="16" 
                  class="expand-icon"
                  :class="{ 'expanded': node.expanded }"
                >
                  mdi-chevron-right
                </v-icon>
              </template>
              <template v-else>
                <v-icon size="14" class="leaf-icon">mdi-file-document-outline</v-icon>
              </template>
            </span>
            
            <!-- 正常显示状态 -->
            <span 
              v-if="!node.isEditing" 
              class="tree-node-name" 
              @click="selectNode(node)"
            >
              {{ node.name }}
            </span>
            
            <!-- 编辑状态 -->
            <input
              v-else
              v-model="editingName"
              class="tree-node-input"
              @blur="confirmEdit(node)"
              @keyup.enter="confirmEdit(node)"
              @keyup.escape="cancelEdit(node)"
              ref="editInput"
              @click.stop
            />
          </div>
          
          <!-- 拖拽预览信息 -->
          <div v-if="dragOverState.nodeId === node.id && dragOverState.position === 'inside'" class="drag-preview">
            <v-icon size="16">mdi-folder-open</v-icon>
            <span>移动到 "{{ node.name }}" 内</span>
          </div>
        </div>
        
        <!-- 拖拽插入位置指示器 - 节点下方 -->
        <div 
          v-if="dropIndicator.show && dropIndicator.nodeId === node.id && dropIndicator.position === 'after'"
          class="drop-indicator after"
        >
          <div class="drop-line"></div>
          <div class="drop-text">在此处插入</div>
        </div>
        
        <!-- 新增节点的输入框 -->
        <div v-if="node.showAddChild" class="tree-node-children">
          <div class="tree-node new-node">
            <div class="tree-node-content">
              <div class="tree-node-header">
                <span class="tree-node-toggle">•</span>
                <input
                  v-model="newNodeName"
                  class="tree-node-input"
                  placeholder="输入笔记名称"
                  @blur="confirmAddChild(node)"
                  @keyup.enter="confirmAddChild(node)"
                  @keyup.escape="cancelAddChild(node)"
                  ref="newNodeInput"
                  @click.stop
                />
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="node.expanded" class="tree-node-children">
          <tree-view
            v-if="hasChildren(node)"
            :nodes="allNodes"
            :parent-id="node.id"
            :selected-node-id="selectedNodeId"
            :dragging-node="draggingNode"
            :show-checkboxes="showCheckboxes"
            :selected-node-ids="selectedNodeIds"
            @node-selected="$emit('node-selected', $event)"
            @update:nodes="updateNodes"
            @update:selected-node-ids="$emit('update:selected-node-ids', $event)"
            @add-child="$emit('add-child', $event)"
            @delete-node="$emit('delete-node', $event)"
            @rename-node="$emit('rename-node', $event)"
            @move-node="$emit('move-node', $event)"
            @drag-start="$emit('drag-start', $event)"
            @drag-end="$emit('drag-end')"
          />
        </div>
      </div>
      
      <!-- 拖拽到根层级的提示区域 - 只在真正拖拽到该区域时显示 -->
      <div 
        v-if="!parentId && draggingNode && isDraggingOverRoot" 
        class="root-drop-zone active"
      >
        <v-icon>mdi-folder-open</v-icon>
        <span>拖放到根目录</span>
      </div>
      
      <!-- 底部空白区域用于拖拽到根目录 -->
      <div 
        v-if="!parentId && draggingNode" 
        class="tree-bottom-space"
        @dragover.prevent="onBottomSpaceDragOver"
        @dragleave="onBottomSpaceDragLeave"
        @drop="onBottomSpaceDrop"
      ></div>
      
      <!-- 右键菜单 -->
      <ContextMenu
        :visible="contextMenu.visible"
        :x="contextMenu.x"
        :y="contextMenu.y"
        @add-child="handleContextAddChild"
        @rename="handleContextRename"
        @delete="handleContextDelete"
      />
      
      <!-- 拖拽幽灵元素 -->
      <div 
        v-if="draggingNode"
        class="drag-ghost"
        :style="dragGhostStyle"
        ref="dragGhost"
      >
        <v-icon size="16" class="drag-ghost-icon">mdi-file-document-outline</v-icon>
        <span class="drag-ghost-text">{{ draggingNode.name }}</span>
      </div>
    </div>
  </template>
  
  <script>
  import { ref, nextTick, computed, reactive } from 'vue'
  import ContextMenu from './ContextMenu.vue'
  
  export default {
    name: 'TreeView',
    components: {
      ContextMenu
    },
    props: {
      nodes: {
        type: Array,
        required: true
      },
      parentId: {
        type: [Number, String],
        default: null
      },
      selectedNodeId: {
        type: [Number, String],
        default: null
      },
      draggingNode: {
        type: Object,
        default: null
      },
      showCheckboxes: {
        type: Boolean,
        default: false
      },
      selectedNodeIds: {
        type: Array,
        default: () => []
      }
    },
    emits: [
      'node-selected', 
      'update:nodes', 
      'update:selected-node-ids',
      'add-child', 
      'delete-node', 
      'rename-node', 
      'move-node', 
      'drag-start', 
      'drag-end'
    ],
    setup(props, { emit }) {
      // const draggingNode = ref(null) // 移除，改为使用props
      const isDraggingOverRoot = ref(false)
      const contextMenu = ref({
        visible: false,
        x: 0,
        y: 0,
        node: null
      })
      const editingName = ref('')
      const newNodeName = ref('')
      const editInput = ref(null)
      const newNodeInput = ref(null)
      const rootNodeInput = ref(null)
      const showRootInput = ref(false)
      const dragGhost = ref(null)
      
      // 拖拽状态管理
      const dragOverState = reactive({
        nodeId: null,
        position: null // 'inside', 'before', 'after'
      })
      
      // 拖拽位置指示器
      const dropIndicator = reactive({
        show: false,
        nodeId: null,
        position: null
      })
      
      // 拖拽幽灵元素样式
      const dragGhostStyle = ref({
        position: 'fixed',
        top: '-1000px',
        left: '-1000px',
        zIndex: 1000,
        pointerEvents: 'none'
      })
      
      return {
        // const draggingNode = ref(null) // 移除，改为使用props
        isDraggingOverRoot,
        contextMenu,
        editingName,
        newNodeName,
        editInput,
        newNodeInput,
        rootNodeInput,
        showRootInput,
        dragGhost,
        dragOverState,
        dropIndicator,
        dragGhostStyle
      }
    },
    computed: {
      // 获取当前层级的节点
      currentLevelNodes() {
        return this.nodes.filter(node => node.parentId === this.parentId);
      },
      // 所有节点（用于递归传递）
      allNodes() {
        return this.nodes;
      }
    },
    methods: {
      hasChildren(node) {
        return this.nodes.filter(n => n.parentId === node.id).length > 0;
      },
      
      // 判断是否可以拖到该节点
      canDropTo(targetNode, draggedNode = null) {
        const sourceNode = draggedNode || this.draggingNode;
        if (!sourceNode) return false;
        if (sourceNode.id === targetNode.id) return false;
        
        // 不能拖到自己的子孙节点
        const isDescendant = (node, targetId) => {
          if (node.id === targetId) return true;
          const children = this.nodes.filter(n => n.parentId === node.id);
          for (const child of children) {
            if (isDescendant(child, targetId)) return true;
          }
          return false;
        };
        return !isDescendant(sourceNode, targetNode.id);
      },
      
      // 获取拖拽位置
      getDragPosition(event, targetElement) {
        if (!targetElement) return 'inside';
        
        const rect = targetElement.getBoundingClientRect();
        const y = event.clientY - rect.top;
        const height = rect.height;
        
        if (y < height * 0.25) {
          return 'before';
        } else if (y > height * 0.75) {
          return 'after';  
        } else {
          return 'inside';
        }
      },
      
      toggleNode(node) {
        // 创建节点的副本并修改expanded属性
        const updatedNodes = this.nodes.map(n => {
          if (n.id === node.id) {
            return { ...n, expanded: !n.expanded };
          }
          return n;
        });
        
        this.$emit('update:nodes', updatedNodes);
      },
      
      selectNode(node) {
        this.$emit('node-selected', node);
      },
      
      // 新建根笔记
      addRootNote() {
        this.showRootInput = true;
        this.newNodeName = '';
        
        // 聚焦到输入框
        this.$nextTick(() => {
          if (this.$refs.rootNodeInput) {
            this.$refs.rootNodeInput.focus();
          }
        });
      },
      
      // 确认添加根节点
      confirmAddRootChild() {
        if (!this.newNodeName.trim()) {
          this.cancelAddRootChild();
          return;
        }
        
        // 发射添加子节点事件
        this.$emit('add-child', {
          parentNode: null,
          name: this.newNodeName.trim()
        });
        
        this.cancelAddRootChild();
      },
      
      // 取消添加根节点
      cancelAddRootChild() {
        this.showRootInput = false;
        this.newNodeName = '';
      },
      
      // 开始添加子节点
      startAddChild(parentNode) {
        // 清除其他节点的showAddChild状态
        const updatedNodes = this.nodes.map(n => ({ ...n, showAddChild: false }));
        
        if (parentNode) {
          // 确保父节点展开
          const nodeIndex = updatedNodes.findIndex(n => n.id === parentNode.id);
          if (nodeIndex !== -1) {
            updatedNodes[nodeIndex] = { ...updatedNodes[nodeIndex], expanded: true, showAddChild: true };
          }
        }
        
        this.$emit('update:nodes', updatedNodes);
        this.newNodeName = '';
        
        // 聚焦到输入框
        this.$nextTick(() => {
          if (this.$refs.newNodeInput && this.$refs.newNodeInput.length > 0) {
            this.$refs.newNodeInput[this.$refs.newNodeInput.length - 1].focus();
          }
        });
      },
      
      // 确认添加子节点
      confirmAddChild(parentNode) {
        if (!this.newNodeName.trim()) {
          this.cancelAddChild(parentNode);
          return;
        }
        
        // 发射添加子节点事件
        this.$emit('add-child', {
          parentNode: parentNode,
          name: this.newNodeName.trim()
        });
        
        this.cancelAddChild(parentNode);
      },
      
      // 取消添加子节点
      cancelAddChild(parentNode) {
        const updatedNodes = this.nodes.map(n => {
          if (n.id === (parentNode ? parentNode.id : null)) {
            return { ...n, showAddChild: false };
          }
          return n;
        });
        
        this.$emit('update:nodes', updatedNodes);
        this.newNodeName = '';
        this.showRootInput = false;
      },
      
      // 显示右键菜单
      showContextMenu(node, event) {
        this.contextMenu.visible = true;
        this.contextMenu.x = event.clientX;
        this.contextMenu.y = event.clientY;
        this.contextMenu.node = node;
        
        // 隐藏其他右键菜单
        document.addEventListener('click', this.hideContextMenu, { once: true });
      },
      
      // 隐藏右键菜单
      hideContextMenu() {
        this.contextMenu.visible = false;
        this.contextMenu.node = null;
      },
      
      // 右键菜单 - 添加子节点
      handleContextAddChild() {
        if (this.contextMenu.node) {
          this.startAddChild(this.contextMenu.node);
        }
        this.hideContextMenu();
      },
      
      // 右键菜单 - 重命名
      handleContextRename() {
        if (this.contextMenu.node) {
          this.startEdit(this.contextMenu.node);
        }
        this.hideContextMenu();
      },
      
      // 右键菜单 - 删除
      handleContextDelete() {
        if (this.contextMenu.node) {
          this.$emit('delete-node', this.contextMenu.node);
        }
        this.hideContextMenu();
      },
      
      // 开始编辑节点名称
      startEdit(node) {
        const updatedNodes = this.nodes.map(n => {
          if (n.id === node.id) {
            return { ...n, isEditing: true };
          }
          return { ...n, isEditing: false }; // 确保只有一个节点在编辑状态
        });
        
        this.$emit('update:nodes', updatedNodes);
        this.editingName = node.name;
        
        // 聚焦到输入框
        this.$nextTick(() => {
          if (this.$refs.editInput && this.$refs.editInput.length > 0) {
            const input = this.$refs.editInput[this.$refs.editInput.length - 1];
            input.focus();
            input.select();
          }
        });
      },
      
      // 确认编辑
      confirmEdit(node) {
        if (!this.editingName.trim()) {
          this.cancelEdit(node);
          return;
        }
        
        // 发射重命名事件
        this.$emit('rename-node', {
          node: node,
          newName: this.editingName.trim()
        });
        
        this.cancelEdit(node);
      },
      
      // 取消编辑
      cancelEdit(node) {
        const updatedNodes = this.nodes.map(n => {
          if (n.id === node.id) {
            return { ...n, isEditing: false };
          }
          return n;
        });
        
        this.$emit('update:nodes', updatedNodes);
        this.editingName = '';
      },
      
      updateNodes(newNodes) {
        this.$emit('update:nodes', newNodes);
      },
      
      // 更新拖拽幽灵元素位置
      updateDragGhostPosition(event) {
        if (this.draggingNode) {
          this.dragGhostStyle = {
            position: 'fixed',
            top: `${event.clientY + 10}px`,
            left: `${event.clientX + 10}px`, 
            zIndex: 1000,
            pointerEvents: 'none'
          };
        }
      },
      
      onDragStart(node, event) {
        // 发射拖拽开始事件，而不是直接修改状态
        this.$emit('drag-start', node);
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', node.id);
        
        // 设置自定义拖拽图像（透明的）
        const dragImage = document.createElement('div');
        dragImage.style.opacity = '0';
        dragImage.style.position = 'absolute';
        dragImage.style.top = '-1000px';
        document.body.appendChild(dragImage);
        event.dataTransfer.setDragImage(dragImage, 0, 0);
        
        // 显示自定义幽灵元素
        this.$nextTick(() => {
          this.updateDragGhostPosition(event);
        });
        
        // 监听鼠标移动以更新幽灵元素位置
        document.addEventListener('dragover', this.updateDragGhostPosition);
        
        // 清理
        setTimeout(() => {
          document.body.removeChild(dragImage);
        }, 0);
      },
      
      onDragOver(node, event) {
        if (!this.canDropTo(node)) {
          event.dataTransfer.dropEffect = 'none';
          return;
        }
        
        const position = this.getDragPosition(event, event.currentTarget);
        
        // 更新拖拽状态
        this.dragOverState.nodeId = node.id;
        this.dragOverState.position = position;
        
        event.dataTransfer.dropEffect = 'move';
      },
      
      onDragLeave(node, event) {
        // 检查是否真的离开了节点区域
        const rect = event.currentTarget.getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;
        
        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
          if (this.dragOverState.nodeId === node.id) {
            this.dragOverState.nodeId = null;
            this.dragOverState.position = null;
          }
        }
      },
      
      onDrop(targetNode, event) {
        const draggedId = event.dataTransfer.getData('text/plain');
        const position = this.getDragPosition(event, event.currentTarget);
        
        if (draggedId && this.canDropTo(targetNode)) {
          // 根据拖拽位置决定目标父节点
          let targetParentId;
          
          if (position === 'inside') {
            // 拖到节点内部
            targetParentId = targetNode.id;
          } else {
            // 拖到节点前后，使用目标节点的父节点
            targetParentId = targetNode.parentId;
          }
          
          this.$emit('move-node', { 
            draggedNodeId: draggedId, 
            targetNodeId: targetParentId,
            position: position,
            referenceNodeId: position !== 'inside' ? targetNode.id : null
          });
        }
        
        // 清理状态并发射拖拽结束事件
        this.$emit('drag-end');
        this.dragOverState.nodeId = null;
        this.dragOverState.position = null;
        this.dropIndicator.show = false;
        
        document.removeEventListener('dragover', this.updateDragGhostPosition);
      },
      
      // 简化的树容器拖拽事件 - 恢复最初版本
      onTreeDragOver(event) {
        if (!this.parentId && this.draggingNode) {
          this.isDraggingOverRoot = true;
          event.dataTransfer.dropEffect = 'move';
        }
      },
      
      onTreeDragLeave(event) {
        // 检查是否离开了树容器
        const rect = event.currentTarget.getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;
        
        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
          this.isDraggingOverRoot = false;
        }
      },
      
      onTreeDrop(event) {
        if (!this.parentId && this.isDraggingOverRoot) {
          const draggedId = event.dataTransfer.getData('text/plain');
          if (draggedId) {
            // 拖拽到根层级
            this.$emit('move-node', { 
              draggedNodeId: draggedId, 
              targetNodeId: null // null 表示根层级
            });
          }
        }
        
        // 清理状态并发射拖拽结束事件
        this.isDraggingOverRoot = false;
        this.$emit('drag-end');
        document.removeEventListener('dragover', this.updateDragGhostPosition);
      },
      
      // 简化的底部空白区域拖拽事件
      onBottomSpaceDragOver(event) {
        if (this.draggingNode) {
          this.isDraggingOverRoot = true;
          event.dataTransfer.dropEffect = 'move';
        }
      },
      
      onBottomSpaceDragLeave(event) {
        this.isDraggingOverRoot = false;
      },
      
      onBottomSpaceDrop(event) {
        if (this.draggingNode) {
          const draggedId = event.dataTransfer.getData('text/plain');
          if (draggedId) {
            // 拖拽到根层级
            this.$emit('move-node', { 
              draggedNodeId: draggedId, 
              targetNodeId: null // null 表示根层级
            });
          }
        }
        
        // 清理状态并发射拖拽结束事件
        this.isDraggingOverRoot = false;
        this.$emit('drag-end');
        document.removeEventListener('dragover', this.updateDragGhostPosition);
      },
      
      isNodeSelected(nodeId) {
        return this.selectedNodeIds.includes(nodeId);
      },
      
      getNodeAndChildren(nodeId) {
        const result = [nodeId];
        const children = this.nodes.filter(n => n.parentId === nodeId);
        
        for (const child of children) {
          result.push(...this.getNodeAndChildren(child.id));
        }
        
        return result;
      },
      
      toggleNodeSelection(node) {
        const currentSelection = [...this.selectedNodeIds];
        const nodeId = node.id;
        
        if (currentSelection.includes(nodeId)) {
          // 取消选择，同时取消其子节点的选择
          const toRemove = this.getNodeAndChildren(nodeId);
          const newSelection = currentSelection.filter(id => !toRemove.includes(id));
          this.$emit('update:selected-node-ids', newSelection);
        } else {
          // 选择节点
          let toAdd = [nodeId];
          
          // 如果是文件夹（有子节点），自动选择其下所有文档
          if (this.hasChildren(node)) {
            toAdd = this.getNodeAndChildren(nodeId);
          }
          
          // 检查是否超出限制
          const newSelection = [...currentSelection, ...toAdd].filter((id, index, arr) => 
            arr.indexOf(id) === index // 去重
          );
          
          if (newSelection.length > 5) {
            alert('最多只能选择5个文档进行认知提取');
            return;
          }
          
          this.$emit('update:selected-node-ids', newSelection);
        }
      }
    }
  }
  </script>
  
  <style scoped>
  .tree-view {
    font-size: 14px;
    user-select: none;
    position: relative;
    /* 移除固定的最小高度，让内容决定高度 */
  }
  
  .tree-node {
    margin-bottom: 2px;
    position: relative;
  }
  
  .tree-node-content {
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    position: relative;
  }
  
  .tree-node-content:hover {
    background-color: #E8E6F6;
  }
  
  /* 选中状态样式 - 需要更高的优先级 */
  .tree-node-content.is-selected {
    background-color: #654C8C !important;
    color: white !important;
  }
  
  .tree-node-content.is-selected:hover {
    background-color: #5a4277 !important;
    color: white !important;
  }
  
  .tree-node-content.is-selected .tree-node-name {
    color: white !important;
    font-weight: 600 !important;
  }
  
  .tree-node-content.is-selected .tree-node-toggle {
    color: white !important;
  }
  
  .tree-node-content.is-selected .expand-icon,
  .tree-node-content.is-selected .leaf-icon {
    color: white !important;
  }
  
  /* 拖拽状态样式 */
  .tree-node-content.is-dragging {
    opacity: 0.5;
    background-color: #f0f0f0 !important;
    color: #999 !important;
    cursor: grabbing;
    transform: scale(0.95);
  }
  
  .tree-node-content.can-drop-inside {
    background-color: #E8E6F6 !important;
    border: 2px solid #654C8C !important;
    box-shadow: 0 0 12px rgba(101, 76, 140, 0.3) !important;
  }
  
  .tree-node-content.can-drop-before {
    border-top: 3px solid #654C8C !important;
    background-color: rgba(101, 76, 140, 0.05) !important;
  }
  
  .tree-node-content.can-drop-after {
    border-bottom: 3px solid #654C8C !important;
    background-color: rgba(101, 76, 140, 0.05) !important;
  }
  
  /* 拖拽预览 */
  .drag-preview {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #654C8C;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
  }
  
  /* 拖拽位置指示器 */
  .drop-indicator {
    position: relative;
    height: 4px;
    margin: 2px 0;
    z-index: 5;
  }
  
  .drop-indicator.before {
    margin-bottom: -2px;
  }
  
  .drop-indicator.after {
    margin-top: -2px;
  }
  
  .drop-line {
    height: 2px;
    background-color: #654C8C;
    border-radius: 1px;
    position: relative;
  }
  
  .drop-line::before,
  .drop-line::after {
    content: '';
    position: absolute;
    top: -2px;
    width: 6px;
    height: 6px;
    background-color: #654C8C;
    border-radius: 50%;
  }
  
  .drop-line::before {
    left: -3px;
  }
  
  .drop-line::after {
    right: -3px;
  }
  
  .drop-text {
    position: absolute;
    top: -18px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #654C8C;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    white-space: nowrap;
  }
  
  /* 根目录拖放区域 - 分离基础样式和激活状态 */
  .root-drop-zone {
    margin: 12px 8px 8px 8px;
    padding: 12px 16px;
    border: 2px dashed rgba(101, 76, 140, 0.4);
    border-radius: 8px;
    text-align: center;
    color: rgba(101, 76, 140, 0.7);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: rgba(101, 76, 140, 0.05);
    font-size: 14px;
    font-weight: 500;
  }
  
  .root-drop-zone.active {
    border-color: #654C8C;
    color: #654C8C;
    background-color: #E8E6F6;
    box-shadow: 0 0 12px rgba(101, 76, 140, 0.2);
  }
  
  /* 底部空白区域 */
  .tree-bottom-space {
    min-height: 60px;
    width: 100%;
    /* 用于拖拽检测的透明区域 */
    border: 1px dashed transparent;
    transition: all 0.2s ease;
  }
  
  /* 当正在拖拽时，给底部区域一些视觉提示 */
  .tree-bottom-space:hover {
    border-color: rgba(101, 76, 140, 0.3);
    background-color: rgba(101, 76, 140, 0.05);
  }
  
  /* 拖拽幽灵元素 */
  .drag-ghost {
    background-color: white;
    border: 1px solid #654C8C;
    border-radius: 4px;
    padding: 6px 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 200px;
    font-size: 14px;
    pointer-events: none;
  }
  
  .drag-ghost-icon {
    color: #654C8C;
  }
  
  .drag-ghost-text {
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .tree-node-header {
    display: flex;
    align-items: center;
    width: 100%;
    min-width: 0; /* 允许flex子项收缩 */
  }
  
  .tree-node-toggle {
    margin-right: 8px;
    color: #4A3670;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s ease, transform 0.2s ease;
    flex-shrink: 0; /* 防止图标被压缩 */
  }
  
  .tree-node-toggle:hover {
    background-color: rgba(101, 76, 140, 0.1);
  }
  
  .expand-icon {
    transition: transform 0.2s ease;
    color: #654C8C;
  }
  
  .expand-icon.expanded {
    transform: rotate(90deg);
  }
  
  .leaf-icon {
    color: #8B7BAE;
    opacity: 0.8;
    transition: color 0.2s ease;
  }
  
  .tree-node-toggle:hover .leaf-icon {
    color: #654C8C;
    opacity: 1;
  }
  
  .tree-node-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333333;
    font-weight: 450;
    max-width: 180px; /* 设置固定最大宽度，确保严格截断 */
    display: block;
    line-height: 1.4;
  }
  
  .tree-node-input {
    flex: 1;
    border: 1px solid #654C8C;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 14px;
    font-weight: 450;
    color: #333333;
    background: white;
    outline: none;
  }
  
  .tree-node-input:focus {
    box-shadow: 0 0 0 2px rgba(101, 76, 140, 0.2);
  }
  
  /* 多选框样式 */
  .node-checkbox {
    margin: 0 8px 0 0;
    cursor: pointer;
    transform: scale(1.1);
    accent-color: #654C8C;
  }
  
  .tree-node-children {
    margin-left: 12px;
    padding-left: 2px;
    border-left: 1px dashed rgba(74, 54, 112, 0.3);
    /* 移除固定高度，让内容决定高度 */
  }
  
  .tree-node.drag-over {
    background-color: #D1CCE8;
    border: 1.5px solid #654C8C;
  }
  
  .is-root > .tree-node-content {
    font-weight: 500;
  }
  
  .create-root-btn {
    margin-bottom: 12px;
    padding: 0 8px;
  }
  
  .create-root-btn .tree-node-btn {
    width: auto;
    padding: 6px 12px;
    background-color: #E8E6F6;
    color: #4A3670;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .create-root-btn .btn-text {
    font-size: 14px;
    font-weight: 500;
  }
  
  .create-root-btn .tree-node-btn:hover {
    background-color: #D1CCE8;
  }
  
  .new-node .tree-node-content {
    background-color: #f8f9fa;
    border: 1px solid #654C8C;
  }
  
  /* 响应式设计：在窄屏幕下进一步限制文本宽度 */
  @media (max-width: 350px) {
    .tree-node-name {
      max-width: 120px;
    }
  }
  
  @media (max-width: 280px) {
    .tree-node-name {
      max-width: 100px;
    }
  }
  </style>