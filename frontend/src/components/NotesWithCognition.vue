<template>
  <div class="notes-with-cognition">
    <div class="notes-panel">
      <h2 class="panel-title">
        <span class="title-icon">📝</span>
        我的笔记
      </h2>
      
      <!-- 笔记选择组件 -->
      <NoteSelect
        :nodes="documentNodes"
        :selected-id="selectedDocumentId"
        :selected-node-ids="selectedNodeIds"
        @node-selected="handleNodeSelected"
        @update:selected-node-ids="handleSelectedNodesChange"
        @extract-cognitions="handleExtractCognitions"
        @view-cognitions="handleViewCognitions"
      />
    </div>

    <!-- 认知提取确认对话框 -->
    <ExtractCognitionDialog
      :visible="showExtractDialog"
      :selected-documents="selectedDocuments"
      @update:visible="showExtractDialog = $event"
      @confirmed="handleExtractionSuccess"
    />

    <!-- 认知列表查看对话框 -->
    <DocumentCognitionList
      :visible="showCognitionList"
      @update:visible="showCognitionList = $event"
      @close="showCognitionList = false"
    />

    <!-- 成功提示 -->
    <div v-if="showSuccessMessage" class="success-message">
      <div class="success-content">
        <span class="success-icon">✅</span>
        <span class="success-text">{{ successMessage }}</span>
        <button class="close-success" @click="showSuccessMessage = false">✕</button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="showErrorMessage" class="error-message">
      <div class="error-content">
        <span class="error-icon">❌</span>
        <span class="error-text">{{ errorMessage }}</span>
        <button class="close-error" @click="showErrorMessage = false">✕</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { documentsApi, DocumentTreeNode } from '@/api/documents';
import NoteSelect from './NoteSelect.vue';
import ExtractCognitionDialog from './ExtractCognitionDialog.vue';
import DocumentCognitionList from './DocumentCognitionList.vue';

interface DocumentNode {
  id: string;
  title: string;
  parent_id?: string;
  expanded?: boolean;
  has_children?: boolean;
}

const documentNodes = ref<DocumentNode[]>([]);
const selectedDocumentId = ref<string | null>(null);
const selectedNodeIds = ref<string[]>([]);
const showExtractDialog = ref(false);
const showCognitionList = ref(false);

// 消息状态
const showSuccessMessage = ref(false);
const successMessage = ref('');
const showErrorMessage = ref(false);
const errorMessage = ref('');

// 计算选中的文档详情 - 修复类型以符合DocumentTreeNode
const selectedDocuments = computed(() => {
  return selectedNodeIds.value.map(nodeId => {
    const node = documentNodes.value.find(n => n.id === nodeId);
    if (node) {
      // 创建符合DocumentTreeNode接口的对象
      return {
        id: nodeId,
        title: node.title || '未知文档',
        parent_id: node.parent_id,
        has_children: node.has_children || false,
        created_at: new Date().toISOString(), // 提供默认值
        updated_at: new Date().toISOString(), // 提供默认值
        creator_id: '', // 提供默认值
        owner_id: '' // 提供默认值
      } as DocumentTreeNode;
    }
    // 如果找不到节点，提供默认的DocumentTreeNode
    return {
      id: nodeId,
      title: '未知文档',
      has_children: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      creator_id: '',
      owner_id: ''
    } as DocumentTreeNode;
  });
});

onMounted(() => {
  loadDocumentTree();
});

/**
 * 加载文档树
 */
const loadDocumentTree = async () => {
  try {
    const treeData = await documentsApi.getDocumentTree();
    
    // 转换数据格式，确保兼容NoteSelect组件
    documentNodes.value = treeData.map(node => ({
      id: node.id,
      title: node.title,
      parent_id: node.parent_id,
      expanded: false,
      has_children: treeData.some(child => child.parent_id === node.id)
    }));
    
    console.log('[NotesWithCognition] 文档树加载完成:', documentNodes.value.length, '个节点');
  } catch (error) {
    console.error('[NotesWithCognition] 加载文档树失败:', error);
    showError('加载文档树失败，请刷新页面重试');
  }
};

/**
 * 处理节点选择
 */
const handleNodeSelected = (node: DocumentNode) => {
  selectedDocumentId.value = node.id;
  console.log('[NotesWithCognition] 选中文档:', node.title);
};

/**
 * 处理多选节点变化
 */
const handleSelectedNodesChange = (nodeIds: string[]) => {
  selectedNodeIds.value = nodeIds;
  console.log('[NotesWithCognition] 选中节点变化:', nodeIds.length, '个节点');
};

/**
 * 处理提取认知
 */
const handleExtractCognitions = () => {
  if (selectedNodeIds.value.length === 0) {
    showError('请先选择要提取认知的文档');
    return;
  }
  
  if (selectedNodeIds.value.length > 5) {
    showError('最多只能选择5个文档进行认知提取');
    return;
  }
  
  console.log('[NotesWithCognition] 开始提取认知:', selectedDocuments.value);
  showExtractDialog.value = true;
};

/**
 * 处理查看认知
 */
const handleViewCognitions = () => {
  console.log('[NotesWithCognition] 查看认知库');
  showCognitionList.value = true;
};

/**
 * 处理提取成功 - 现在处理confirmed事件
 */
const handleExtractionSuccess = (result: any) => {
  console.log('[NotesWithCognition] 认知提取成功:', result);
  
  const { total_cognitions, message } = result;
  showSuccess(`${message}！共生成 ${total_cognitions} 个认知。`);
  
  // 清空选择
  selectedNodeIds.value = [];
};

/**
 * 显示成功消息
 */
const showSuccess = (message: string) => {
  successMessage.value = message;
  showSuccessMessage.value = true;
  // 5秒后自动隐藏
  setTimeout(() => {
    showSuccessMessage.value = false;
  }, 5000);
};

/**
 * 显示错误消息
 */
const showError = (message: string) => {
  errorMessage.value = message;
  showErrorMessage.value = true;
  // 8秒后自动隐藏
  setTimeout(() => {
    showErrorMessage.value = false;
  }, 8000);
};
</script>

<style scoped>
.notes-with-cognition {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  position: relative;
}

.notes-panel {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 24px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.title-icon {
  font-size: 28px;
}

/* 成功消息样式 */
.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1500;
  animation: slideInRight 0.3s ease-out;
}

.success-content {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #d4edda;
  color: #155724;
  padding: 16px 20px;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
}

.success-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.success-text {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.close-success {
  background: none;
  border: none;
  color: #155724;
  font-size: 18px;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.close-success:hover {
  background-color: rgba(21, 87, 36, 0.1);
}

/* 错误消息样式 */
.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1500;
  animation: slideInRight 0.3s ease-out;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8d7da;
  color: #721c24;
  padding: 16px 20px;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
}

.error-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.close-error {
  background: none;
  border: none;
  color: #721c24;
  font-size: 18px;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.close-error:hover {
  background-color: rgba(114, 28, 36, 0.1);
}

/* 动画 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .notes-panel {
    padding: 16px;
  }
  
  .panel-title {
    font-size: 20px;
  }
  
  .success-message,
  .error-message {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .success-content,
  .error-content {
    max-width: none;
  }
}
</style> 