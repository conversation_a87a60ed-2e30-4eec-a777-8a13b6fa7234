<template>
  <div class="cognition-review-manager">
    <v-card>
      <v-card-title class="d-flex align-center justify-between">
        <span>认知上传审核</span>
        <v-btn
          icon
          @click="refreshData"
          :loading="loading"
          size="small"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </v-card-title>
      
      <v-card-text>
        <!-- 统计信息 -->
        <v-row class="mb-4">
          <v-col cols="12" md="3">
            <v-card color="orange-lighten-4" variant="tonal">
              <v-card-text class="text-center">
                <div class="text-h4 font-weight-bold">{{ pagination.total }}</div>
                <div class="text-subtitle-1">待审核认知</div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        
        <!-- 数据表格 -->
        <v-data-table
          :headers="headers"
          :items="cognitions"
          :loading="loading"
          item-value="id"
          class="elevation-1"
          :items-per-page="pagination.size"
          hide-default-footer
        >
          <!-- 用户列 -->
          <template v-slot:item.user="{ item }">
            <div class="user-info">
              <div class="user-avatar">
                {{ getAuthorInitial(item) }}
              </div>
              <div class="user-details">
                <div class="user-name">{{ item.user_name || '未知用户' }}</div>
                <div class="user-id">ID: {{ item.user_id?.substring(0, 8) }}...</div>
              </div>
            </div>
          </template>
          
          <!-- 话题列 -->
          <template v-slot:item.topics="{ item }">
            <div class="topics-column">
              <v-chip
                v-if="item.primary_topic"
                size="x-small"
                color="primary"
                class="mb-1 mr-1"
                variant="tonal"
              >
                <v-icon start size="12">mdi-tag</v-icon>
                {{ formatTopicName(item.primary_topic).substring(0, 12) }}...
              </v-chip>
              <br v-if="item.primary_topic && item.related_topic?.length > 0" />
              <v-chip
                v-for="topic in (item.related_topic || []).slice(0, 2)"
                :key="topic"
                size="x-small"
                color="secondary"
                class="mr-1"
                variant="tonal"
              >
                <v-icon start size="10">mdi-tag-outline</v-icon>
                {{ formatTopicName(topic).substring(0, 10) }}...
              </v-chip>
            </div>
          </template>
          
          <!-- 内容预览列 -->
          <template v-slot:item.content="{ item }">
            <div class="content-preview">
              <div class="question-preview">
                <strong>问题：</strong>
                {{ truncateText(item.question_zh, 50) }}
              </div>
              <div class="answer-preview">
                <strong>答案：</strong>
                {{ truncateText(item.answer_zh, 80) }}
              </div>
            </div>
          </template>
          
          <!-- 来源列 -->
          <template v-slot:item.source="{ item }">
            <div class="source-column">
              <v-chip
                size="small"
                color="info"
                class="mb-1"
                variant="tonal"
              >
                <v-icon start size="12">mdi-file-multiple</v-icon>
                {{ item.source_document_titles.length }}个文档
              </v-chip>
              <div
                v-for="title in item.source_document_titles.slice(0, 2)"
                :key="title"
                class="text-caption source-title"
              >
                {{ truncateText(title, 30) }}
              </div>
            </div>
          </template>
          
          <!-- 时间列 -->
          <template v-slot:item.upload_requested_at="{ item }">
            <div class="time-column">
              <div class="time-main">{{ formatDate(item.upload_requested_at) }}</div>
              <div class="time-detail">{{ formatTimeDetail(item.upload_requested_at) }}</div>
            </div>
          </template>
          
          <!-- 操作列 -->
          <template v-slot:item.actions="{ item }">
            <div class="actions-column">
              <v-btn
                icon
                size="small"
                color="primary"
                @click="viewDetails(item)"
                class="mr-1"
                title="查看详情"
              >
                <v-icon>mdi-eye</v-icon>
              </v-btn>
              <v-btn
                icon
                size="small"
                color="success"
                @click="approveItem(item)"
                :loading="processingItems.includes(item.id)"
                title="通过审核"
              >
                <v-icon>mdi-check</v-icon>
              </v-btn>
              <v-btn
                icon
                size="small"
                color="error"
                @click="rejectItem(item)"
                :loading="processingItems.includes(item.id)"
                class="ml-1"
                title="拒绝审核"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </template>
        </v-data-table>
        
        <!-- 分页 -->
        <v-pagination
          v-if="pagination.pages > 1"
          v-model="pagination.page"
          :length="pagination.pages"
          class="mt-4"
          @update:model-value="changePage"
        />
      </v-card-text>
    </v-card>
    
    <!-- 详情对话框 -->
    <v-dialog v-model="detailDialog" max-width="900">
      <v-card v-if="selectedItem" class="cognition-detail">
        <!-- 渐变头部栏 -->
        <div class="dialog-gradient-bar"></div>
        
        <v-card-title class="detail-header">
          <div class="header-content">
            <!-- 作者信息 -->
            <div class="author-info">
              <div class="author-avatar">
                {{ getAuthorInitial(selectedItem) }}
              </div>
              <div class="author-details">
                <div class="author-name">{{ selectedItem.user_name || '未知用户' }}</div>
                <div class="publish-time">{{ formatDate(selectedItem.upload_requested_at) }}</div>
              </div>
            </div>
            
            <!-- 认知类型标签 -->
            <div class="cognition-badges">
              <v-chip
                v-if="selectedItem.is_synthesized"
                size="small"
                color="primary"
                variant="tonal"
                class="synthesis-chip"
              >
                <v-icon start size="14">mdi-merge</v-icon>
                综合认知
              </v-chip>
              
              <v-chip
                size="small"
                color="purple"
                variant="tonal"
                class="verification-chip"
              >
                <v-icon start size="14">mdi-file-document-edit</v-icon>
                文档提取
              </v-chip>
              
              <v-chip
                size="small"
                color="orange"
                variant="tonal"
                class="status-chip"
              >
                <v-icon start size="14">mdi-clock-outline</v-icon>
                待审核
              </v-chip>
            </div>
          </div>
          
          <v-btn icon variant="text" @click="detailDialog = false" class="close-btn">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-card-text class="detail-content">
          <!-- 话题标签区域 -->
          <div class="section" v-if="hasTopics(selectedItem)">
            <h3 class="section-title">
              <v-icon color="primary">mdi-tag-multiple</v-icon>
              话题标签
            </h3>
            <div class="topics-container">
              <!-- Primary Topic -->
              <div 
                v-if="selectedItem.primary_topic" 
                class="topic-tag primary-topic"
              >
                <v-icon start size="14">mdi-tag</v-icon>
                {{ formatTopicName(selectedItem.primary_topic) }}
              </div>
              
              <!-- Related Topics -->
              <div 
                v-for="topic in selectedItem.related_topic || []" 
                :key="topic"
                class="topic-tag related-topic"
              >
                <v-icon start size="14">mdi-tag-outline</v-icon>
                {{ formatTopicName(topic) }}
              </div>
            </div>
          </div>
          
          <!-- 核心问题 -->
          <div class="section">
            <h3 class="section-title">
              <v-icon color="primary">mdi-help-circle-outline</v-icon>
              问题
            </h3>
            <div class="content-text question-text">
              {{ selectedItem.question_zh }}
            </div>
          </div>
          
          <!-- 答案 -->
          <div class="section">
            <h3 class="section-title">
              <v-icon color="green">mdi-lightbulb-outline</v-icon>
              洞察
            </h3>
            <div class="content-text answer-text">
              {{ selectedItem.answer_zh }}
            </div>
          </div>
          
          <!-- 思考过程 -->
          <div class="section" v-if="selectedItem.think_zh">
            <h3 class="section-title">
              <v-icon color="purple">mdi-brain</v-icon>
              思考过程
            </h3>
            <div class="content-text think-text">
              {{ selectedItem.think_zh }}
            </div>
          </div>
          
          <!-- 元数据信息 -->
          <div class="section">
            <h3 class="section-title">
              <v-icon color="info">mdi-information-outline</v-icon>
              认知信息
            </h3>
            <div class="meta-info">
              <!-- 来源文档 -->
              <div class="meta-item">
                <h4 class="meta-title">
                  <v-icon color="blue">mdi-file-document-multiple-outline</v-icon>
                  来源文档
                </h4>
                <div class="source-documents">
                  <v-chip
                    v-for="title in selectedItem.source_document_titles"
                    :key="title"
                    size="small"
                    color="blue"
                    variant="tonal"
                    class="mr-2 mb-2"
                  >
                    <v-icon start size="14">mdi-file-document</v-icon>
                    {{ title }}
                  </v-chip>
                </div>
              </div>
              
              <!-- 置信度 -->
              <div class="meta-item" v-if="selectedItem.confidence_level">
                <h4 class="meta-title">
                  <v-icon color="orange">mdi-chart-line</v-icon>
                  置信度
                </h4>
                <v-chip 
                  :color="getConfidenceColor(selectedItem.confidence_level)"
                  variant="tonal"
                  size="small"
                >
                  {{ selectedItem.confidence_level }}
                </v-chip>
              </div>
              
              <!-- 复杂度 -->
              <div class="meta-item" v-if="selectedItem.complexity_level">
                <h4 class="meta-title">
                  <v-icon color="teal">mdi-complexity</v-icon>
                  复杂度
                </h4>
                <v-chip 
                  :color="getComplexityColor(selectedItem.complexity_level)"
                  variant="tonal"
                  size="small"
                >
                  {{ selectedItem.complexity_level }}
                </v-chip>
              </div>
            </div>
          </div>
        </v-card-text>
        
        <v-card-actions class="action-area">
          <v-spacer></v-spacer>
          <v-btn color="grey" @click="detailDialog = false">关闭</v-btn>
          <v-btn
            color="success"
            @click="approveItem(selectedItem)"
            :loading="processingItems.includes(selectedItem.id)"
          >
            <v-icon start>mdi-check</v-icon>
            通过
          </v-btn>
          <v-btn
            color="error"
            @click="rejectItem(selectedItem)"
            :loading="processingItems.includes(selectedItem.id)"
            class="ml-2"
          >
            <v-icon start>mdi-close</v-icon>
            拒绝
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
    <!-- 审核对话框 -->
    <v-dialog v-model="reviewDialog" max-width="500">
      <v-card>
        <v-card-title>
          {{ reviewAction === 'approve' ? '通过' : '拒绝' }}认知
        </v-card-title>
        <v-card-text>
          <v-textarea
            v-model="reviewComment"
            :label="'审核意见' + (reviewAction === 'reject' ? '（必填）' : '（可选）')"
            :rules="reviewAction === 'reject' ? [v => !!v || '拒绝时必须填写审核意见'] : []"
            rows="4"
            variant="outlined"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" @click="reviewDialog = false">取消</v-btn>
          <v-btn
            :color="reviewAction === 'approve' ? 'success' : 'error'"
            @click="submitReview"
            :loading="submittingReview"
            :disabled="reviewAction === 'reject' && !reviewComment.trim()"
          >
            {{ reviewAction === 'approve' ? '通过' : '拒绝' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import apiRequest from '@/api/request.js'

// 数据状态
const loading = ref(false)
const cognitions = ref([])
const pagination = ref({
  page: 1,
  size: 20,
  total: 0,
  pages: 0
})
const processingItems = ref([])

// 对话框状态
const detailDialog = ref(false)
const reviewDialog = ref(false)
const selectedItem = ref(null)
const reviewAction = ref('')
const reviewComment = ref('')
const submittingReview = ref(false)

// 表格头部
const headers = [
  { title: '用户', key: 'user', sortable: false, width: '120px' },
  { title: '话题', key: 'topics', sortable: false, width: '300px' },
  { title: '内容预览', key: 'content', sortable: false, width: '300px' },
  { title: '来源', key: 'source', sortable: false, width: '150px' },
  { title: '提交时间', key: 'upload_requested_at', width: '130px' },
  { title: '操作', key: 'actions', sortable: false, width: '150px' }
]

// 加载待审核认知列表
const loadCognitions = async () => {
  if (loading.value) return
  
  loading.value = true
  try {
    const response = await apiRequest({
      url: `/api/documents/admin/pending-cognitions?skip=${(pagination.value.page - 1) * pagination.value.size}&limit=${pagination.value.size}`,
      method: 'GET'
    })
    
    console.log('管理员API响应:', response)
    
    if (response.data) {
      cognitions.value = response.data.items || []
      pagination.value.total = response.data.total || 0
      pagination.value.pages = response.data.pages || 0
    } else {
      console.error('API响应格式不正确:', response)
      cognitions.value = []
    }
  } catch (error) {
    console.error('加载认知列表失败:', error)
    cognitions.value = []
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page) => {
  pagination.value.page = page
  loadCognitions()
}

// 刷新数据
const refreshData = () => {
  loadCognitions()
}

// 查看详情
const viewDetails = (item) => {
  selectedItem.value = item
  detailDialog.value = true
}

// 通过认知
const approveItem = (item) => {
  selectedItem.value = item
  reviewAction.value = 'approve'
  reviewComment.value = ''
  reviewDialog.value = true
}

// 拒绝认知
const rejectItem = (item) => {
  selectedItem.value = item
  reviewAction.value = 'reject'
  reviewComment.value = ''
  reviewDialog.value = true
}

// 提交审核
const submitReview = async () => {
  if (!selectedItem.value || submittingReview.value) return
  
  submittingReview.value = true
  try {
    const response = await apiRequest({
      url: `/api/documents/admin/cognitions/${selectedItem.value.id}/review`,
      method: 'POST',
      data: {
        action: reviewAction.value,
        comment: reviewComment.value
      }
    })
    
    console.log('审核提交响应:', response)
    
    if (response.data && response.data.success) {
      // 成功提示
      alert(`认知审核${reviewAction.value === 'approve' ? '通过' : '不通过'}`)
      
      // 关闭对话框
      reviewDialog.value = false
      
      // 重新加载列表
      await loadCognitions()
    } else {
      throw new Error(response.data?.message || '审核失败')
    }
  } catch (error) {
    console.error('提交审核失败:', error)
    alert(`审核失败: ${error.message || error}`)
  } finally {
    submittingReview.value = false
  }
}

// 文本截断
const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString('zh-CN')
}

// 格式化详细时间
const formatTimeDetail = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit'
  })
}

// 获取作者初始
const getAuthorInitial = (item) => {
  if (!item.user_name) return ''
  return item.user_name.substring(0, 1).toUpperCase()
}

// 判断是否有话题
const hasTopics = (item) => {
  return item.primary_topic || item.related_topic?.length > 0
}

// 格式化话题名称
const formatTopicName = (topic) => {
  if (!topic) return ''
  return topic.name || topic
}

// 获取置信度颜色
const getConfidenceColor = (confidence) => {
  if (confidence < 0.3) return 'error'
  if (confidence < 0.6) return 'warning'
  return 'success'
}

// 获取复杂度颜色
const getComplexityColor = (complexity) => {
  if (complexity < 0.3) return 'error'
  if (complexity < 0.6) return 'warning'
  return 'success'
}

// 组件挂载时获取数据
onMounted(() => {
  loadCognitions()
})
</script>

<style scoped>
.cognition-review-manager {
  padding: 20px;
}

.content-preview {
  font-size: 0.85rem;
}

.question-preview,
.answer-preview {
  margin-bottom: 4px;
}

.question-preview strong,
.answer-preview strong {
  color: #1976d2;
}

.cognition-detail {
  position: relative;
}

.dialog-gradient-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.detail-header {
  padding: 16px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 16px;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: bold;
}

.publish-time {
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.54);
}

.cognition-badges {
  display: flex;
  align-items: center;
}

.synthesis-chip,
.verification-chip,
.status-chip {
  margin-left: 8px;
}

.close-btn {
  margin-left: auto;
}

.detail-content {
  padding: 16px;
}

.section {
  margin-bottom: 24px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 16px;
}

.topics-container {
  display: flex;
  flex-wrap: wrap;
}

.topic-tag {
  background-color: #e0e0e0;
  padding: 4px 8px;
  border-radius: 16px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.primary-topic {
  background-color: #e0e0e0;
  padding: 4px 8px;
  border-radius: 16px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.related-topic {
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 16px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.content-text {
  margin-bottom: 16px;
}

.question-text {
  font-weight: bold;
}

.answer-text {
  font-weight: bold;
}

.think-text {
  font-weight: bold;
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
}

.meta-item {
  width: 50%;
  margin-bottom: 16px;
}

.meta-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.source-documents {
  margin-top: 8px;
}

.action-area {
  padding: 16px;
  justify-content: flex-end;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 16px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: bold;
}

.user-id {
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.54);
}

.topics-column {
  display: flex;
  flex-wrap: wrap;
}

.time-column {
  display: flex;
  flex-direction: column;
}

.time-main {
  font-weight: bold;
}

.time-detail {
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.54);
}

.source-column {
  display: flex;
  flex-direction: column;
}

.actions-column {
  display: flex;
  justify-content: flex-end;
}
</style> 