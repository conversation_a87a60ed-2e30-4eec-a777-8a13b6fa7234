<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="1000px"
    @click:outside="handleClose"
  >
    <v-card class="cognition-dialog">
      <!-- 渐变头部栏 -->
      <div class="dialog-gradient-bar"></div>
      
      <!-- 头部 -->
      <v-card-title class="dialog-header">
        <div class="header-left">
          <v-icon class="mr-3" color="primary">mdi-brain</v-icon>
          <div>
            <h3 class="dialog-title">认知库</h3>
            <p class="dialog-subtitle">浏览您的认知洞察</p>
          </div>
        </div>
        <v-btn icon variant="text" @click="handleClose">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <!-- 工具栏 -->
      <div class="toolbar">
        <v-text-field
          v-model="searchQuery"
          placeholder="搜索认知内容..."
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          density="compact"
          hide-details
          class="search-field"
          @input="handleSearch"
        />
        <v-btn
          color="primary"
          variant="elevated"
          @click="loadCognitions"
          :loading="loading"
          class="refresh-btn"
        >
          <v-icon size="16" class="mr-1">mdi-refresh</v-icon>
          刷新
        </v-btn>
      </div>

      <v-card-text class="cognition-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <v-progress-circular indeterminate color="primary" size="40"></v-progress-circular>
          <p class="mt-4">正在加载认知...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="cognitions.length === 0" class="empty-state">
          <v-icon size="64" color="grey-lighten-1">mdi-brain-outline</v-icon>
          <h4 class="mt-4 mb-2">暂无认知数据</h4>
          <p class="empty-description">开始提取您的第一个认知洞察吧！</p>
          <v-btn color="primary" variant="elevated" @click="$emit('create-first')" class="create-btn">
            立即开始
          </v-btn>
        </div>
        
        <!-- 认知卡片网格 -->
        <div v-else class="cognition-grid">
          <v-card
            v-for="cognition in cognitions"
            :key="cognition.id"
            class="cognition-card"
            @click="handleViewDetail(cognition)"
          >
            <!-- 卡片内容 -->
            <v-card-text class="pa-4">
              <!-- 标题和标签 -->
              <div class="card-header mb-3">
                <h4 class="cognition-question">{{ cognition.question_zh }}</h4>
                <div class="card-badges">
                  <v-chip
                    v-if="cognition.is_synthesized"
                    size="small"
                    color="primary"
                    variant="tonal"
                    class="synthesis-chip"
                  >
                    综合认知
                  </v-chip>
                </div>
              </div>
              
              <!-- 内容预览 -->
              <p class="answer-preview">
                {{ truncateText(cognition.answer_zh, 120) }}
              </p>
              
              <!-- 底部元信息和操作按钮 -->
              <div class="card-footer">
                <div class="footer-left">
                  <div class="source-info">
                    <v-icon size="14" class="mr-1">mdi-file-document-multiple</v-icon>
                    <span class="source-count">{{ cognition.source_document_titles.length }} 个来源</span>
                  </div>
                  
                  <div class="date-info">
                    <span class="creation-date">{{ formatDate(cognition.created_at) }}</span>
                  </div>
                </div>
                
                <!-- 上传状态和按钮 -->
                <div class="footer-right" @click.stop>
                  <!-- 上传状态显示 -->
                  <v-chip
                    v-if="cognition.upload_status === 'not_uploaded'"
                    size="x-small"
                    color="grey"
                    variant="tonal"
                    class="mr-2"
                  >
                    <v-icon start size="12">mdi-cloud-off</v-icon>
                    未上传
                  </v-chip>
                  <v-chip
                    v-else-if="cognition.upload_status === 'pending_review'"
                    size="x-small"
                    color="orange"
                    variant="tonal"
                    class="mr-2"
                  >
                    <v-icon start size="12">mdi-clock-outline</v-icon>
                    审核中
                  </v-chip>
                  <v-chip
                    v-else-if="cognition.upload_status === 'approved'"
                    size="x-small"
                    color="green"
                    variant="tonal"
                    class="mr-2"
                  >
                    <v-icon start size="12">mdi-check-circle</v-icon>
                    已上传
                  </v-chip>
                  <v-chip
                    v-else-if="cognition.upload_status === 'rejected'"
                    size="x-small"
                    color="red"
                    variant="tonal"
                    class="mr-2"
                  >
                    <v-icon start size="12">mdi-close-circle</v-icon>
                    审核不通过
                  </v-chip>
                  
                  <!-- 上传认知按钮 -->
                  <v-btn
                    v-if="cognition.upload_status === 'not_uploaded'"
                    color="primary"
                    variant="elevated"
                    size="x-small"
                    @click="handleUploadCognition(cognition)"
                    :loading="uploadingIds.has(cognition.id)"
                  >
                    <v-icon size="12">mdi-cloud-upload</v-icon>
                    上传
                  </v-btn>
                  
                  <!-- 查看审核意见按钮 -->
                  <v-btn
                    v-if="cognition.upload_status === 'rejected' && cognition.review_comment"
                    color="grey"
                    variant="text"
                    size="x-small"
                    @click="handleShowReviewComment(cognition)"
                  >
                    <v-icon size="12">mdi-message-outline</v-icon>
                    审核意见
                  </v-btn>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </div>
      </v-card-text>

      <!-- 认知详情对话框 -->
      <DocumentCognitionDetail
        v-if="selectedCognition"
        :visible="showDetailDialog"
        :cognition="selectedCognition"
        @close="handleDetailClose"
        @update:visible="showDetailDialog = $event"
        @upload-success="handleUploadSuccess"
      />
      
      <!-- 上传确认对话框 -->
      <v-dialog v-model="showUploadDialog" max-width="400">
        <v-card>
          <v-card-title>确认上传认知</v-card-title>
          <v-card-text>
            <p>您确定要将此认知上传到认知平台供其他用户查看吗？</p>
            <v-alert type="info" variant="tonal" class="mt-3">
              上传后的认知需要管理员审核通过才会公开展示。
            </v-alert>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="grey" @click="showUploadDialog = false">取消</v-btn>
            <v-btn
              color="primary"
              @click="confirmUpload"
              :loading="uploadingIds.size > 0"
            >
              确认上传
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <!-- 审核意见对话框 -->
      <v-dialog v-model="showReviewDialog" max-width="500">
        <v-card v-if="currentUploadingCognition">
          <v-card-title class="d-flex align-center">
            <v-icon color="error" class="mr-2">mdi-close-circle</v-icon>
            审核意见
          </v-card-title>
          <v-card-text>
            <v-alert type="error" variant="tonal" class="mb-3">
              认知未通过审核
            </v-alert>
            <div class="review-comment">
              <h4 class="mb-2">审核意见：</h4>
              <p>{{ currentUploadingCognition.review_comment }}</p>
            </div>
            <div class="review-info mt-3">
              <small class="text-grey">
                审核时间：{{ formatDate(currentUploadingCognition.reviewed_at || '') }}<br>
                审核人员：{{ currentUploadingCognition.reviewer_name || '未知' }}
              </small>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="showReviewDialog = false">知道了</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <!-- 消息提示 -->
      <v-snackbar
        v-model="showSnackbar"
        :color="snackbarColor"
        timeout="3000"
      >
        {{ snackbarMessage }}
        <template v-slot:actions>
          <v-btn
            color="white"
            variant="text"
            @click="showSnackbar = false"
          >
            关闭
          </v-btn>
        </template>
      </v-snackbar>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { cognitionsApi, DocumentCognition } from '@/api/documents'
import DocumentCognitionDetail from './DocumentCognitionDetail.vue'

// Props
interface DocumentCognitionListProps {
  visible: boolean
}

const props = defineProps<DocumentCognitionListProps>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'create-first': []
  'close': []
}>()

// 数据
const cognitions = ref<DocumentCognition[]>([])
const loading = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedCognition = ref<DocumentCognition | null>(null)
const showDetailDialog = ref(false)

// 上传相关状态
const uploadingIds = ref(new Set<string>())
const showUploadDialog = ref(false)
const showReviewDialog = ref(false)
const currentUploadingCognition = ref<DocumentCognition | null>(null)

// 消息提示
const showSnackbar = ref(false)
const snackbarMessage = ref('')
const snackbarColor = ref('error')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 显示消息
const showMessage = (message: string, color: string = 'error') => {
  snackbarMessage.value = message
  snackbarColor.value = color
  showSnackbar.value = true
}

// 方法
const loadCognitions = async () => {
  loading.value = true
  try {
    const skip = (currentPage.value - 1) * pageSize.value
    const response = await cognitionsApi.getUserDocumentCognitions(skip, pageSize.value)
    
    cognitions.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('加载认知列表失败:', error)
    showMessage('加载认知列表失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

const handleSearch = () => {
  currentPage.value = 1
  loadCognitions()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadCognitions()
}

const handleViewDetail = (cognition: DocumentCognition) => {
  selectedCognition.value = cognition
  showDetailDialog.value = true
}

const handleDetailClose = () => {
  showDetailDialog.value = false
  selectedCognition.value = null
}

const handleUploadSuccess = () => {
  currentPage.value = 1
  loadCognitions()
}

const handleUploadCognition = async (cognition: DocumentCognition) => {
  uploadingIds.value.add(cognition.id)
  try {
    const result = await cognitionsApi.uploadCognition(cognition.id)
    if (result.success) {
      // 更新认知的上传状态
      const index = cognitions.value.findIndex(c => c.id === cognition.id)
      if (index !== -1) {
        cognitions.value[index] = {
          ...cognitions.value[index],
          upload_status: result.upload_status || 'pending_review',
          upload_requested_at: result.upload_requested_at
        }
      }
      showMessage('认知上传成功，等待审核', 'success')
    } else {
      showMessage(result.message || '上传失败', 'error')
    }
  } catch (error) {
    console.error('上传认知失败:', error)
    showMessage('上传认知失败，请重试', 'error')
  } finally {
    uploadingIds.value.delete(cognition.id)
  }
}

const handleShowReviewComment = (cognition: DocumentCognition) => {
  currentUploadingCognition.value = cognition
  showReviewDialog.value = true
}

const confirmUpload = async () => {
  if (currentUploadingCognition.value) {
    await handleUploadCognition(currentUploadingCognition.value)
    showUploadDialog.value = false
    currentUploadingCognition.value = null
  }
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 监听对话框打开，加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadCognitions()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadCognitions()
  }
})
</script>

<style scoped>
.cognition-dialog {
  background: linear-gradient(145deg, #ffffff 0%, #fafbff 100%);
  border-radius: 20px !important;
  overflow: hidden;
  box-shadow: 0 24px 48px rgba(102, 103, 171, 0.15);
}

.dialog-gradient-bar {
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.dialog-header {
  background: transparent;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  padding: 24px 24px 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.dialog-subtitle {
  margin: 0;
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: rgba(102, 126, 234, 0.05);
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.search-field {
  flex: 1;
  max-width: 400px;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.refresh-btn:hover {
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.cognition-content {
  padding: 24px !important;
  max-height: 60vh;
  overflow-y: auto;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.loading-state p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-state h4 {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.empty-description {
  color: #95a5a6;
  font-size: 16px;
  margin-bottom: 24px;
}

.create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.create-btn:hover {
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.cognition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.cognition-card {
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4px 16px rgba(102, 103, 171, 0.08);
  overflow: hidden;
}

.cognition-card:hover {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 12px 32px rgba(102, 103, 171, 0.15);
  transform: translateY(-4px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.cognition-question {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  flex: 1;
  color: #2c3e50;
}

.card-badges {
  flex-shrink: 0;
}

.synthesis-chip {
  font-weight: 600;
  border-radius: 8px;
}

.answer-preview {
  margin: 0 0 16px 0;
  color: #546e7a;
  line-height: 1.6;
  font-size: 14px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.source-info {
  display: flex;
  align-items: center;
  color: #667eea;
  font-size: 13px;
  font-weight: 500;
}

.source-count {
  color: #667eea;
}

.date-info {
  text-align: right;
}

.creation-date {
  font-size: 12px;
  color: #95a5a6;
  font-weight: 500;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cognition-dialog {
    margin: 16px;
    max-width: calc(100vw - 32px);
    max-height: calc(100vh - 32px);
    border-radius: 16px !important;
  }
  
  .cognition-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px;
  }
  
  .search-field {
    max-width: none;
  }
  
  .card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .date-info {
    text-align: left;
  }
  
  .cognition-content {
    padding: 16px !important;
  }
}

/* 统一的关闭按钮样式 */
.v-btn.v-btn--icon {
  color: #95a5a6 !important;
  transition: all 0.2s ease;
}

.v-btn.v-btn--icon:hover {
  color: #667eea !important;
  background-color: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
}
</style> 