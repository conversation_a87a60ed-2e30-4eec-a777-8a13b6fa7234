<template>
  <v-dialog v-model="dialogVisible" max-width="600px" :persistent="isProcessing" @click:outside="handleClose">
    <v-card class="extract-dialog">
      <!-- 渐变头部栏 -->
      <div class="dialog-gradient-bar"></div>
      
      <!-- 头部 -->
      <v-card-title class="dialog-header">
        <div class="header-left">
          <v-icon class="mr-3" :class="{ 'rotating': isProcessing }" color="primary">
            {{ isProcessing ? 'mdi-brain' : 'mdi-auto-fix' }}
          </v-icon>
          <div>
            <h3 class="dialog-title">
              {{ isProcessing ? '正在提取认知' : (extractionResult ? '提取完成' : '认知提取确认') }}
            </h3>
            <p class="dialog-subtitle">
              {{ isProcessing ? '请耐心等待AI分析' : (extractionResult ? '成功生成认知洞察' : '从选中的笔记中提取认知') }}
            </p>
          </div>
        </div>
        <v-btn icon variant="text" @click="handleClose" :disabled="isProcessing">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      
      <v-card-text class="pa-6">
        <!-- 选中的文档列表 -->
        <div v-if="!isProcessing && !extractionResult" class="section">
          <div class="section-header mb-4">
            <v-icon class="mr-2" color="primary">mdi-file-document-multiple</v-icon>
            <h4>选中的笔记 ({{ selectedDocuments.length }} 个)</h4>
          </div>
          
          <div class="document-list">
            <v-card
              v-for="doc in selectedDocuments"
              :key="doc.id"
              class="document-item mb-3"
              variant="outlined"
            >
              <v-card-text class="pa-4">
                <div class="d-flex align-center">
                  <div class="doc-icon mr-3">
                    <v-icon color="primary">mdi-file-document-outline</v-icon>
                  </div>
                  <div class="flex-grow-1">
                    <div class="doc-title">{{ doc.title }}</div>
                    <div class="doc-meta">{{ formatDate(doc.updated_at) }}</div>
                  </div>
                  <v-chip size="small" color="success" variant="tonal">已选中</v-chip>
                </div>
              </v-card-text>
            </v-card>
          </div>
        </div>

        <!-- 处理进度 -->
        <div v-if="isProcessing" class="section">
          <div class="section-header mb-4">
            <v-icon class="mr-2 rotating" color="primary">mdi-brain</v-icon>
            <h4>AI正在处理</h4>
          </div>
          
          <div class="progress-container">
            <!-- 主进度条 -->
            <div class="mb-4">
              <div class="d-flex justify-space-between align-center mb-2">
                <span class="progress-text">{{ progressText }}</span>
                <span class="progress-percentage">{{ Math.round(progressPercentage) }}%</span>
              </div>
              <v-progress-linear
                :model-value="progressPercentage"
                height="8"
                color="primary"
                rounded
                :striped="isProcessing"
              />
            </div>
            
            <!-- 处理步骤 -->
            <div class="process-steps">
              <div 
                v-for="(step, index) in processSteps" 
                :key="index"
                class="step-item"
                :class="{ 
                  'active': currentStep === index,
                  'completed': currentStep > index,
                  'error': step.error
                }"
              >
                <v-icon class="step-icon" size="20">
                  <template v-if="currentStep > index && !step.error">mdi-check-circle</template>
                  <template v-else-if="step.error">mdi-alert-circle</template>
                  <template v-else-if="currentStep === index">mdi-loading mdi-spin</template>
                  <template v-else>mdi-circle-outline</template>
                </v-icon>
                <span class="step-text">{{ step.text }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 提取结果 -->
        <div v-if="extractionResult" class="section">
          <div class="section-header mb-4">
            <v-icon class="mr-2" color="success">mdi-check-circle</v-icon>
            <h4>提取成功</h4>
          </div>
          
          <div class="result-summary">
            <!-- 统计信息 -->
            <div class="stats-row mb-4">
              <div class="stat-item">
                <div class="stat-number">{{ extractionResult.total_documents }}</div>
                <div class="stat-label">处理文档</div>
              </div>
              <div class="stat-item" v-if="extractionResult.synthesized_cognition">
                <div class="stat-number">1</div>
                <div class="stat-label">生成认知</div>
              </div>
            </div>
            
            <!-- 综合认知预览 -->
            <v-card v-if="extractionResult.synthesized_cognition" class="cognition-preview" variant="outlined">
              <v-card-title class="preview-title">
                <v-icon class="mr-2" color="primary">mdi-lightbulb-outline</v-icon>
                认知预览
              </v-card-title>
              <v-card-text class="pa-4">
                <div class="preview-question mb-3">
                  <div class="preview-label">问题</div>
                  <div class="preview-content">{{ extractionResult.synthesized_cognition.question_zh }}</div>
                </div>
                <div class="preview-answer">
                  <div class="preview-label">洞察</div>
                  <div class="preview-content">{{ truncateText(extractionResult.synthesized_cognition.answer_zh, 200) }}</div>
                </div>
              </v-card-text>
            </v-card>
          </div>
        </div>
      </v-card-text>

      <!-- 对话框操作 -->
      <v-card-actions class="pa-6 pt-0">
        <v-spacer />
        <v-btn 
          @click="handleClose"
          :disabled="isProcessing"
          variant="text"
          class="cancel-btn"
        >
          {{ isProcessing ? '处理中...' : (extractionResult ? '关闭' : '取消') }}
        </v-btn>
        
        <v-btn 
          v-if="!isProcessing && !extractionResult"
          color="primary" 
          @click="handleConfirm"
          variant="elevated"
          class="main-btn"
        >
          开始提取
        </v-btn>
        
        <v-btn 
          v-if="extractionResult"
          color="primary" 
          @click="handleViewResults"
          variant="elevated"
          class="main-btn"
        >
          查看认知库
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { DocumentTreeNode, ExtractCognitionResponse, cognitionsApi } from '@/api/documents'

// Props
interface ExtractCognitionDialogProps {
  visible: boolean
  selectedDocuments: DocumentTreeNode[]
}

const props = defineProps<ExtractCognitionDialogProps>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirmed: [result: ExtractCognitionResponse]
}>()

// 数据
const isProcessing = ref(false)
const currentStep = ref(-1)
const progressPercentage = ref(0)
const progressText = ref('')
const progressStatus = ref<'success' | 'error' | ''>('')
const extractionResult = ref<ExtractCognitionResponse | null>(null)

// 处理步骤
const processSteps = ref([
  { text: '准备处理文档...', error: '' },
  { text: '深度分析内容...', error: '' },
  { text: '提取认知洞察...', error: '' },
  { text: '合成综合认知...', error: '' },
  { text: '保存到认知库...', error: '' }
])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  if (isProcessing.value) return
  
  resetDialog()
  emit('update:visible', false)
}

const handleConfirm = () => {
  startExtraction()
}

const handleViewResults = () => {
  if (extractionResult.value) {
    emit('confirmed', extractionResult.value)
  }
  emit('update:visible', false)
}

const startExtraction = async () => {
  isProcessing.value = true
  currentStep.value = 0
  progressPercentage.value = 0
  progressStatus.value = ''
  extractionResult.value = null
  
  try {
    await performRealExtraction()
  } catch (error) {
    console.error('认知提取失败:', error)
    progressStatus.value = 'error'
    progressText.value = '认知提取失败，请重试'
    
    if (currentStep.value >= 0 && currentStep.value < processSteps.value.length) {
      processSteps.value[currentStep.value].error = '处理失败，请重试'
    }
    
    isProcessing.value = false
  }
}

const performRealExtraction = async () => {
  const totalSteps = processSteps.value.length
  
  // 步骤1: 准备处理文档
  currentStep.value = 0
  progressPercentage.value = 20
  progressText.value = processSteps.value[0].text
  await delay(800)
  
  // 步骤2-3: 调用实际API
  currentStep.value = 1
  progressPercentage.value = 40
  progressText.value = processSteps.value[1].text
  
  // 获取文档ID列表
  const documentIds = props.selectedDocuments.map(doc => doc.id)
  
  // 调用认知提取API
  const result = await cognitionsApi.extractCognitions(documentIds)
  
  // 步骤3: 处理结果
  currentStep.value = 2
  progressPercentage.value = 60
  progressText.value = processSteps.value[2].text
  await delay(1000)
  
  // 步骤4: 合成认知
  currentStep.value = 3
  progressPercentage.value = 80
  progressText.value = processSteps.value[3].text
  await delay(1000)
  
  // 步骤5: 保存完成
  currentStep.value = 4
  progressPercentage.value = 100
  progressText.value = processSteps.value[4].text
  await delay(800)
  
  // 处理完成
  progressStatus.value = 'success'
  progressText.value = '认知提取完成！'
  currentStep.value = totalSteps
  isProcessing.value = false
  extractionResult.value = result
  
  // 自动传递结果给父组件
  emit('confirmed', result)
}

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

const resetDialog = () => {
  isProcessing.value = false
  currentStep.value = -1
  progressPercentage.value = 0
  progressText.value = ''
  progressStatus.value = ''
  extractionResult.value = null
  
  // 重置步骤状态
  processSteps.value.forEach(step => {
    step.error = ''
  })
}

const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 监听对话框关闭，重置状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetDialog()
  }
})
</script>

<style scoped>
.extract-dialog {
  background: linear-gradient(145deg, #ffffff 0%, #fafbff 100%);
  border-radius: 20px !important;
  overflow: hidden;
  box-shadow: 0 24px 48px rgba(102, 103, 171, 0.15);
}

.dialog-gradient-bar {
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.dialog-header {
  background: transparent;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  padding: 24px 24px 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.dialog-subtitle {
  margin: 0;
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.section {
  margin-bottom: 24px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.document-list {
  max-height: 200px;
  overflow-y: auto;
}

.document-item {
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  transition: all 0.2s ease;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
}

.document-item:hover {
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 103, 171, 0.12);
}

.doc-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.doc-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
}

.doc-meta {
  font-size: 13px;
  color: #95a5a6;
  margin-top: 2px;
}

.progress-container {
  padding: 20px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  transition: all 0.2s ease;
  color: #95a5a6;
}

.step-item.active {
  color: #667eea;
}

.step-item.completed {
  color: #27ae60;
}

.step-item.error {
  color: #e74c3c;
}

.step-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.step-text {
  font-size: 14px;
  font-weight: 500;
}

.mdi-spin {
  animation: rotate 1s linear infinite;
}

.result-summary {
  padding: 20px;
  background: rgba(39, 174, 96, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(39, 174, 96, 0.1);
}

.stats-row {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 4px;
  font-weight: 500;
}

.cognition-preview {
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
}

.preview-title {
  background: rgba(102, 126, 234, 0.05);
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.preview-question, .preview-answer {
  margin-bottom: 16px;
}

.preview-question:last-child, .preview-answer:last-child {
  margin-bottom: 0;
}

.preview-label {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
}

.preview-content {
  font-size: 14px;
  line-height: 1.6;
  color: #2c3e50;
}

.cancel-btn {
  color: #95a5a6 !important;
  font-weight: 500;
}

.main-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.main-btn:hover {
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .extract-dialog {
    margin: 16px;
    max-width: calc(100vw - 32px);
    max-height: calc(100vh - 32px);
    border-radius: 16px !important;
  }
  
  .dialog-title {
    font-size: 18px;
  }
  
  .dialog-subtitle {
    font-size: 13px;
  }
  
  .document-list {
    max-height: 150px;
  }
  
  .progress-container,
  .result-summary {
    padding: 16px;
  }
  
  .stats-row {
    gap: 16px;
  }
  
  .stat-item {
    min-width: 80px;
    padding: 12px;
  }
}

/* 统一的关闭按钮样式 */
.v-btn.v-btn--icon {
  color: #95a5a6 !important;
  transition: all 0.2s ease;
}

.v-btn.v-btn--icon:hover {
  color: #667eea !important;
  background-color: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
}
</style> 