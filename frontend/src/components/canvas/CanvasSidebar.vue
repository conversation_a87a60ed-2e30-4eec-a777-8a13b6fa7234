<template>
    <div class="sidebar" :class="{ 'collapsed': !isOpen }">
      <div class="sidebar-back-btn-wrapper">
        <v-btn class="back-home-btn" color="#654C8C" variant="text" @click="$router.push('/')">
          <v-icon left>mdi-arrow-left</v-icon>
          返回主页
        </v-btn>
      </div>
      <button @click="toggleSidebar" class="sidebar-toggle-btn">
        <v-icon>{{ isOpen ? 'mdi-chevron-left' : 'mdi-chevron-right' }}</v-icon>
      </button>
      <div class="sidebar-actual-content" v-show="isOpen">
        <!-- 认知功能操作区域 - 移动到标题上方 -->
        <div class="cognition-actions">
          <div class="action-buttons">
            <button 
              class="action-btn extract-btn"
              @click="handleExtractCognitions"
              :disabled="selectedNodeIds.length === 0 || selectedNodeIds.length > 5"
              :title="getExtractButtonTooltip()"
            >
              提取认知
            </button>
            <button 
              class="action-btn view-btn"
              @click="handleViewCognitions"
            >
              查看认知
            </button>
          </div>
          <div class="selection-info" v-if="selectedNodeIds.length > 0">
            已选择 {{ selectedNodeIds.length }} 个文档
            <span v-if="selectedNodeIds.length > 5" class="error-text">（最多只能选择5个）</span>
          </div>
          <div class="cognition-toggle">
            <label class="toggle-checkbox">
              <input 
                type="checkbox" 
                v-model="showCheckboxes"
                @change="handleToggleCheckboxes"
              >
              <span class="toggle-text">选择笔记</span>
            </label>
          </div>
        </div>
        
        <h3 class="sidebar-title">导航树</h3>
        
        <!-- 美化的提示信息 -->
        <div class="sidebar-tip">
          <v-icon size="18" class="tip-icon">mdi-information-outline</v-icon>
          <span class="tip-text">右键节点查看更多操作</span>
        </div>
        
        <!-- 使用原有的TreeView组件 -->
        <TreeView 
          :nodes="nodes" 
          :selected-node-id="selectedNodeId"
          :dragging-node="draggingNode"
          :show-checkboxes="showCheckboxes"
          :selected-node-ids="selectedNodeIds"
          @node-selected="handleNodeSelected"
          @update:nodes="updateNodes"
          @update:selected-node-ids="handleSelectedNodesChange"
          @add-child="handleAddChild"
          @delete-node="handleDeleteNode"
          @rename-node="handleRenameNode"
          @move-node="handleMoveNode"
          @drag-start="handleDragStart"
          @drag-end="handleDragEnd"
        />
      </div>
    </div>
  </template>
  
  <script>
  import { ref, defineEmits } from 'vue';
  import TreeView from '../TreeView.vue';
  
  export default {
    name: 'CanvasSidebar',
    components: {
      TreeView
    },
    props: {
      nodes: {
        type: Array,
        required: true
      },
      selectedNodeId: {
        type: [Number, String],
        default: null
      }
    },
    emits: [
      'node-selected', 
      'update:nodes', 
      'toggle', 
      'add-child', 
      'delete-node', 
      'rename-node', 
      'move-node',
      'extract-cognitions',
      'view-cognitions'
    ],
    setup(props, { emit }) {
      const isOpen = ref(true);
      const draggingNode = ref(null);
      const showCheckboxes = ref(false);
      const selectedNodeIds = ref([]);
      
      const toggleSidebar = () => {
        isOpen.value = !isOpen.value;
        emit('toggle', isOpen.value);
      };
      
      const handleNodeSelected = (node) => {
        emit('node-selected', node);
      };
      
      const updateNodes = (updatedNodes) => {
        emit('update:nodes', updatedNodes);
      };

      const handleAddChild = (node) => {
        emit('add-child', node);
      };

      const handleDeleteNode = (node) => {
        emit('delete-node', node);
      };

      const handleRenameNode = (data) => {
        emit('rename-node', data);
      };

      const handleMoveNode = (data) => {
        emit('move-node', data);
      };
      
      const handleDragStart = (node) => {
        draggingNode.value = node;
      };
      
      const handleDragEnd = () => {
        draggingNode.value = null;
      };
      
      const handleSelectedNodesChange = (newSelectedNodeIds) => {
        selectedNodeIds.value = newSelectedNodeIds;
      };
      
      const handleExtractCognitions = () => {
        if (selectedNodeIds.value.length === 0) {
          alert('请先选择要提取认知的文档');
          return;
        }
        
        if (selectedNodeIds.value.length > 5) {
          alert('最多只能选择5个文档进行认知提取');
          return;
        }
        
        emit('extract-cognitions', selectedNodeIds.value);
      };
      
      const handleViewCognitions = () => {
        emit('view-cognitions');
      };
      
      const handleToggleCheckboxes = () => {
        if (!showCheckboxes.value) {
          // 如果关闭多选框，清空选择
          selectedNodeIds.value = [];
        }
      };
      
      const getExtractButtonTooltip = () => {
        if (selectedNodeIds.value.length === 0) {
          return '请至少选择1个文档';
        }
        if (selectedNodeIds.value.length > 5) {
          return '最多只能选择5个文档';
        }
        return `提取 ${selectedNodeIds.value.length} 个文档的认知`;
      };
      
      return {
        isOpen,
        draggingNode,
        showCheckboxes,
        selectedNodeIds,
        toggleSidebar,
        handleNodeSelected,
        updateNodes,
        handleAddChild,
        handleDeleteNode,
        handleRenameNode,
        handleMoveNode,
        handleDragStart,
        handleDragEnd,
        handleSelectedNodesChange,
        handleExtractCognitions,
        handleViewCognitions,
        handleToggleCheckboxes,
        getExtractButtonTooltip
      };
    }
  }
  </script>
  
  <style scoped>
  .sidebar {
    width: 280px; /* 侧边栏展开宽度 */
    height: 100%;
    background-color: #f7f6fb; /* 与 interface-card 背景色协调 */
    border-right: 1px solid #d9d6ef; /* 与 interface-card 边框色协调 */
    transition: width 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative; /* 用于绝对定位切换按钮 */
    flex-shrink: 0; /* 防止侧边栏在空间不足时被压缩 */
  }
  
  .sidebar.collapsed {
    width: 60px; /* 侧边栏收起宽度 */
  }
  
  .sidebar-toggle-btn {
    position: absolute;
    top: 15px;
    right: -18px; /* 使按钮部分突出在主内容区边缘 */
    z-index: 100; /* 确保按钮在最上层 */
    background-color: #654C8C; /* Vuetify 主题色 */
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: right 0.3s ease, transform 0.3s ease;
  }
  
  .sidebar.collapsed .sidebar-toggle-btn {
    right: 50%;
    transform: translateX(50%);
  }
  
  .sidebar-actual-content {
    padding: 20px 15px;
    overflow-y: auto; /* 如果树内容过长，允许滚动 */
    flex-grow: 1;
    margin-top: 50px; /* 为切换按钮留出空间，如果按钮在内容区上方 */
  }
  
  .sidebar.collapsed .sidebar-actual-content {
    display: none; /* 或者使用 v-if/v-show 控制 */
  }
  
  .sidebar.collapsed .sidebar-back-btn-wrapper {
    display: none; /* 折叠时隐藏返回主页按钮 */
  }
  
  .sidebar-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #654C8C;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(101, 76, 140, 0.2);
  }
  
  .sidebar-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #f3f1ff 0%, #e8e5ff 100%);
    border: 1px solid rgba(101, 76, 140, 0.15);
    border-radius: 8px;
    font-size: 0.85rem;
    color: #5a4a77;
    transition: all 0.2s ease;
  }
  
  .sidebar-tip:hover {
    background: linear-gradient(135deg, #ebe7ff 0%, #ddd8ff 100%);
    border-color: rgba(101, 76, 140, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(101, 76, 140, 0.1);
  }
  
  .tip-icon {
    color: #654C8C;
    opacity: 0.8;
  }
  
  .tip-text {
    font-weight: 500;
    line-height: 1.3;
  }
  
  .sidebar-back-btn-wrapper {
    padding: 18px 18px 0 18px;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  .back-home-btn {
    font-size: 15px;
    font-weight: 500;
    color: #654C8C;
    background: transparent;
    border-radius: 20px;
    box-shadow: none;
    padding: 6px 18px;
    margin-bottom: 8px;
    transition: color 0.2s;
  }
  .back-home-btn:hover {
    background: transparent;
    color: #4a3670;
  }
  
  /* 认知功能样式 */
  .cognition-actions {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 14px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .extract-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
  
  .extract-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
  
  .extract-btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  .view-btn {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    color: white;
  }
  
  .view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  }
  
  .btn-icon {
    color: currentColor;
  }
  
  .selection-info {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .error-text {
    color: #dc3545;
    font-weight: 600;
  }
  
  .cognition-toggle {
    display: flex;
    align-items: center;
  }
  
  .toggle-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
  }
  
  .toggle-checkbox input[type="checkbox"] {
    margin: 0;
    transform: scale(0.9);
  }
  
  .toggle-text {
    user-select: none;
  }
  </style>