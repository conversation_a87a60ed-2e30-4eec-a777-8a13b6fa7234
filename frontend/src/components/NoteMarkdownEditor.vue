<template>
  <div class="editor-container" @drop.prevent="handleDrop" @paste.prevent="handlePaste">
    <!-- 工具栏 -->
    <div v-if="editor" class="editor-toolbar">
      <!-- 撤销/重做 -->
      <div class="toolbar-group">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              @click="editor.chain().focus().undo().run()"
              :disabled="!editor.can().chain().focus().undo().run()"
            >
              <v-icon size="18">mdi-undo</v-icon>
            </button>
          </template>
          <span>撤销 (Ctrl+Z)</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              @click="editor.chain().focus().redo().run()"
              :disabled="!editor.can().chain().focus().redo().run()"
            >
              <v-icon size="18">mdi-redo</v-icon>
            </button>
          </template>
          <span>重做 (Ctrl+Y)</span>
        </v-tooltip>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 标题 -->
      <div class="toolbar-group">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <select 
              class="toolbar-select"
              v-bind="props"
              @change="setHeading(($event.target as HTMLSelectElement).value)"
            >
              <option value="paragraph">正文</option>
              <option value="1">标题 1</option>
              <option value="2">标题 2</option>
              <option value="3">标题 3</option>
              <option value="4">标题 4</option>
              <option value="5">标题 5</option>
              <option value="6">标题 6</option>
            </select>
          </template>
          <span>设置标题级别</span>
        </v-tooltip>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 文本格式 -->
      <div class="toolbar-group">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              :class="{ 'is-active': editor.isActive('bold') }"
              @click="editor.chain().focus().toggleBold().run()"
            >
              <v-icon size="18">mdi-format-bold</v-icon>
            </button>
          </template>
          <span>加粗 (Ctrl+B)</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              :class="{ 'is-active': editor.isActive('italic') }"
              @click="editor.chain().focus().toggleItalic().run()"
            >
              <v-icon size="18">mdi-format-italic</v-icon>
            </button>
          </template>
          <span>斜体 (Ctrl+I)</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              :class="{ 'is-active': editor.isActive('strike') }"
              @click="editor.chain().focus().toggleStrike().run()"
            >
              <v-icon size="18">mdi-format-strikethrough</v-icon>
            </button>
          </template>
          <span>删除线</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              :class="{ 'is-active': editor.isActive('code') }"
              @click="editor.chain().focus().toggleCode().run()"
            >
              <v-icon size="18">mdi-code-tags</v-icon>
            </button>
          </template>
          <span>行内代码</span>
        </v-tooltip>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 列表和缩进 -->
      <div class="toolbar-group">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              :class="{ 'is-active': editor.isActive('bulletList') }"
              @click="editor.chain().focus().toggleBulletList().run()"
            >
              <v-icon size="18">mdi-format-list-bulleted</v-icon>
            </button>
          </template>
          <span>无序列表</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              :class="{ 'is-active': editor.isActive('orderedList') }"
              @click="editor.chain().focus().toggleOrderedList().run()"
            >
              <v-icon size="18">mdi-format-list-numbered</v-icon>
            </button>
          </template>
          <span>有序列表</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              @click="indentContent()"
            >
              <v-icon size="18">mdi-format-indent-increase</v-icon>
            </button>
          </template>
          <span>增加缩进 (Tab)</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              @click="outdentContent()"
            >
              <v-icon size="18">mdi-format-indent-decrease</v-icon>
            </button>
          </template>
          <span>减少缩进 (Shift+Tab)</span>
        </v-tooltip>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 其他格式 -->
      <div class="toolbar-group">
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              :class="{ 'is-active': editor.isActive('blockquote') }"
              @click="editor.chain().focus().toggleBlockquote().run()"
            >
              <v-icon size="18">mdi-format-quote-close</v-icon>
            </button>
          </template>
          <span>引用块</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              @click="editor.chain().focus().toggleCodeBlock().run()"
            >
              <v-icon size="18">mdi-code-braces</v-icon>
            </button>
          </template>
          <span>代码块</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              @click="addLink()"
            >
              <v-icon size="18">mdi-link</v-icon>
            </button>
          </template>
          <span>插入链接 (Ctrl+K)</span>
        </v-tooltip>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <label
              class="toolbar-btn"
              v-bind="props"
              style="cursor: pointer;"
            >
              <v-icon size="18">mdi-image</v-icon>
              <input
                type="file"
                accept="image/*"
                @change="handleImageUpload"
                style="display: none;"
              />
            </label>
          </template>
          <span>插入图片</span>
        </v-tooltip>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 表格和其他 -->
      <div class="toolbar-group">
        <!-- 插入表格按钮 -->
        <v-menu>
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
            >
              <v-icon size="18">mdi-table-plus</v-icon>
            </button>
          </template>
          
          <v-card min-width="200">
            <v-card-text>
              <div class="table-size-selector">
                <h4 class="mb-2">选择表格大小</h4>
                <div class="size-grid">
                  <div 
                    v-for="row in 5" 
                    :key="row"
                    class="size-row"
                  >
                    <div 
                      v-for="col in 6" 
                      :key="col"
                      class="size-cell"
                      :class="{ 'active': row <= hoverRow && col <= hoverCol }"
                      @mouseenter="hoverRow = row; hoverCol = col"
                      @click="insertCustomTable(row, col)"
                    ></div>
                  </div>
                </div>
                <div class="size-display mt-2">
                  {{ hoverRow }} × {{ hoverCol }} 表格
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-menu>
        
        <!-- 表格操作按钮组 (仅在表格中显示) -->
        <template v-if="editor && editor.isActive('table')">
          <!-- 在上方添加行 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                @click="addRowBefore()"
              >
                <v-icon size="18">mdi-table-row-plus-before</v-icon>
              </button>
            </template>
            <span>在上方添加行</span>
          </v-tooltip>
          
          <!-- 在下方添加行 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                @click="addRowAfter()"
              >
                <v-icon size="18">mdi-table-row-plus-after</v-icon>
              </button>
            </template>
            <span>在下方添加行</span>
          </v-tooltip>
          
          <!-- 删除行 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                @click="deleteRow()"
              >
                <v-icon size="18">mdi-table-row-remove</v-icon>
              </button>
            </template>
            <span>删除当前行</span>
          </v-tooltip>
          
          <div class="toolbar-divider-small"></div>
          
          <!-- 在左侧添加列 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                @click="addColumnBefore()"
              >
                <v-icon size="18">mdi-table-column-plus-before</v-icon>
              </button>
            </template>
            <span>在左侧添加列</span>
          </v-tooltip>
          
          <!-- 在右侧添加列 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                @click="addColumnAfter()"
              >
                <v-icon size="18">mdi-table-column-plus-after</v-icon>
              </button>
            </template>
            <span>在右侧添加列</span>
          </v-tooltip>
          
          <!-- 删除列 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                @click="deleteColumn()"
              >
                <v-icon size="18">mdi-table-column-remove</v-icon>
              </button>
            </template>
            <span>删除当前列</span>
          </v-tooltip>
          
          <div class="toolbar-divider-small"></div>
          
          <!-- 删除表格 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                @click="deleteTable()"
              >
                <v-icon size="18">mdi-table-remove</v-icon>
              </button>
            </template>
            <span>删除整个表格</span>
          </v-tooltip>
          
          <div class="toolbar-divider-small"></div>
          
          <!-- 切换文本换行 -->
          <v-tooltip location="bottom">
            <template v-slot:activator="{ props }">
              <button
                class="toolbar-btn"
                v-bind="props"
                :class="{ 'is-active': isTableTextWrapped() }"
                @click="toggleTableTextWrap()"
              >
                <v-icon size="18">{{ isTableTextWrapped() ? 'mdi-wrap' : 'mdi-wrap-disabled' }}</v-icon>
              </button>
            </template>
            <span>{{ isTableTextWrapped() ? '切换到紧凑模式' : '切换到自动换行' }}</span>
          </v-tooltip>
        </template>
        
        <v-tooltip location="bottom">
          <template v-slot:activator="{ props }">
            <button
              class="toolbar-btn"
              v-bind="props"
              @click="editor.chain().focus().setHorizontalRule().run()"
            >
              <v-icon size="18">mdi-minus</v-icon>
            </button>
          </template>
          <span>插入分割线</span>
        </v-tooltip>
      </div>
    </div>

    <editor-content class="editor-content-wrapper" :editor="editor" />
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeUnmount, shallowRef, onMounted, watch, nextTick, ref, h } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import { NodeViewWrapper } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { Markdown } from 'tiptap-markdown'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import Link from '@tiptap/extension-link'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import Image from '@tiptap/extension-image'
import { common, createLowlight } from 'lowlight'
import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'
import python from 'highlight.js/lib/languages/python'
import java from 'highlight.js/lib/languages/java'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import json from 'highlight.js/lib/languages/json'
import bash from 'highlight.js/lib/languages/bash'
import sql from 'highlight.js/lib/languages/sql'
import AIMarkdownDecorator from './edit/AIMarkdownDecorator'
import AICursorMark from './edit/AICursorMark'
import { documentsApi, isTOSImageUrl, convertTOSImageUrl } from '@/api/documents'

// 自定义缩进扩展
import { Extension } from '@tiptap/core'
import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

// 自定义可调整大小的图片扩展
const ResizableImage = Node.create({
  name: 'resizableImage',
  
  group: 'block',
  
  atom: true,
  
  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: element => element.getAttribute('src'),
        renderHTML: attributes => {
          if (!attributes.src) {
            return {}
          }
          return {
            src: attributes.src,
          }
        },
      },
      alt: {
        default: null,
        parseHTML: element => element.getAttribute('alt'),
        renderHTML: attributes => {
          if (!attributes.alt) {
            return {}
          }
          return {
            alt: attributes.alt,
          }
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {}
          }
          return {
            title: attributes.title,
          }
        },
      },
      width: {
        default: null,
        parseHTML: element => element.getAttribute('width'),
        renderHTML: attributes => {
          if (!attributes.width) {
            return {}
          }
          return {
            width: attributes.width,
          }
        },
      },
      height: {
        default: null,
        parseHTML: element => element.getAttribute('height'),
        renderHTML: attributes => {
          if (!attributes.height) {
            return {}
          }
          return {
            height: attributes.height,
          }
        },
      },
    }
  },
  
  parseHTML() {
    return [
      {
        tag: 'img[src]',
        getAttrs: element => {
          return {
            src: element.getAttribute('src'),
            alt: element.getAttribute('alt'),
            title: element.getAttribute('title'),
            width: element.getAttribute('width'),
            height: element.getAttribute('height'),
          }
        }
      },
    ]
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['img', mergeAttributes(HTMLAttributes)]
  },
  
  addNodeView() {
    return VueNodeViewRenderer(ResizableImageNodeView)
  },
  
  addCommands() {
    return {
      setImage: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: options,
        })
      },
    }
  },
})

// 创建Vue组件用于渲染可调整大小的图片
const ResizableImageNodeView = defineComponent({
  props: {
    node: {
      type: Object,
      required: true,
    },
    updateAttributes: {
      type: Function,
      required: true,
    },
  },
  
  setup(props) {
    const imageRef = ref<HTMLImageElement>()
    const containerRef = ref<HTMLDivElement>()
    const isResizing = ref(false)
    const resizeDirection = ref('')
    
    const startResize = (direction: string, event: MouseEvent) => {
      event.preventDefault()
      event.stopPropagation()
      
      isResizing.value = true
      resizeDirection.value = direction
      
      const startX = event.clientX
      const startY = event.clientY
      const startWidth = imageRef.value?.offsetWidth || 0
      const startHeight = imageRef.value?.offsetHeight || 0
      
      const handleMouseMove = (e: MouseEvent) => {
        if (!isResizing.value || !imageRef.value) return
        
        const deltaX = e.clientX - startX
        const deltaY = e.clientY - startY
        
        let newWidth = startWidth
        let newHeight = startHeight
        
        // 计算新尺寸
        if (direction.includes('right')) {
          newWidth = Math.max(100, startWidth + deltaX)
        }
        if (direction.includes('left')) {
          newWidth = Math.max(100, startWidth - deltaX)
        }
        if (direction.includes('bottom')) {
          newHeight = Math.max(75, startHeight + deltaY)
        }
        if (direction.includes('top')) {
          newHeight = Math.max(75, startHeight - deltaY)
        }
        
        // 如果是角落拖拽，保持宽高比
        if (direction.includes('right') && direction.includes('bottom')) {
          const aspectRatio = startWidth / startHeight
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            newHeight = newWidth / aspectRatio
          } else {
            newWidth = newHeight * aspectRatio
          }
        }
        
        // 更新属性
        props.updateAttributes({
          width: Math.round(newWidth),
          height: Math.round(newHeight),
        })
      }
      
      const handleMouseUp = () => {
        isResizing.value = false
        resizeDirection.value = ''
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
      
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }
    
    const handleImageLoad = () => {
      if (!props.node.attrs.width && !props.node.attrs.height && imageRef.value) {
        // 如果没有设置尺寸，使用原始尺寸
        const naturalWidth = imageRef.value.naturalWidth
        const naturalHeight = imageRef.value.naturalHeight
        
        // 限制最大宽度为容器的80%
        const maxWidth = 600
        if (naturalWidth > maxWidth) {
          const ratio = maxWidth / naturalWidth
          props.updateAttributes({
            width: maxWidth,
            height: Math.round(naturalHeight * ratio),
          })
        }
      }
    }
    
    return {
      imageRef,
      containerRef,
      isResizing,
      startResize,
      handleImageLoad,
    }
  },
  
  render() {
    return h(NodeViewWrapper, {
      class: ['resizable-image-container', { 'is-resizing': this.isResizing }]
    }, [
      h('div', {
        ref: 'containerRef',
        style: 'position: relative; display: inline-block;'
      }, [
        // 图片元素
        h('img', {
          ref: 'imageRef',
          src: this.node.attrs.src,
          alt: this.node.attrs.alt,
          title: this.node.attrs.title,
          width: this.node.attrs.width,
          height: this.node.attrs.height,
          draggable: false,
          class: 'resizable-image',
          onLoad: this.handleImageLoad
        }),
        
        // 调整手柄容器
        h('div', { class: 'resize-handles' }, [
          // 角落手柄
          h('div', {
            class: 'resize-handle corner top-left',
            onMousedown: (e: MouseEvent) => this.startResize('top-left', e)
          }),
          h('div', {
            class: 'resize-handle corner top-right',
            onMousedown: (e: MouseEvent) => this.startResize('top-right', e)
          }),
          h('div', {
            class: 'resize-handle corner bottom-left',
            onMousedown: (e: MouseEvent) => this.startResize('bottom-left', e)
          }),
          h('div', {
            class: 'resize-handle corner bottom-right',
            onMousedown: (e: MouseEvent) => this.startResize('bottom-right', e)
          })
        ]),
      ])
    ])
  }
})

const lowlight = createLowlight(common)
lowlight.register('javascript', javascript)
lowlight.register('typescript', typescript)
lowlight.register('python', python)
lowlight.register('java', java)
lowlight.register('css', css)
lowlight.register('html', html)
lowlight.register('json', json)
lowlight.register('bash', bash)
lowlight.register('sql', sql)

// 自定义缩进扩展
const IndentExtension = Extension.create({
  name: 'indent',
  
  addKeyboardShortcuts() {
    return {
      'Tab': () => {
        // 处理列表项缩进
        if (this.editor.isActive('listItem')) {
          return this.editor.commands.sinkListItem('listItem')
        }
        // 处理普通段落缩进
        return this.editor.commands.command(({ tr, state }) => {
          const { selection } = state
          const { from, to } = selection
          
          tr.doc.nodesBetween(from, to, (node, pos) => {
            if (node.type.name === 'paragraph') {
              const attrs = { ...node.attrs }
              const currentIndent = attrs.indent || 0
              attrs.indent = Math.min(currentIndent + 1, 8) // 最大缩进8级
              tr.setNodeMarkup(pos, null, attrs)
            }
          })
          
          return true
        })
      },
      'Shift-Tab': () => {
        // 处理列表项反缩进
        if (this.editor.isActive('listItem')) {
          return this.editor.commands.liftListItem('listItem')
        }
        // 处理普通段落反缩进
        return this.editor.commands.command(({ tr, state }) => {
          const { selection } = state
          const { from, to } = selection
          
          tr.doc.nodesBetween(from, to, (node, pos) => {
            if (node.type.name === 'paragraph') {
              const attrs = { ...node.attrs }
              const currentIndent = attrs.indent || 0
              attrs.indent = Math.max(currentIndent - 1, 0) // 最小缩进0级
              tr.setNodeMarkup(pos, null, attrs)
            }
          })
          
          return true
        })
      }
    }
  },
  
  addGlobalAttributes() {
    return [
      {
        types: ['paragraph'],
        attributes: {
          indent: {
            default: 0,
            parseHTML: element => parseInt(element.getAttribute('data-indent')) || 0,
            renderHTML: attributes => {
              if (!attributes.indent || attributes.indent === 0) {
                return {}
              }
              return {
                'data-indent': attributes.indent,
                style: `margin-left: ${attributes.indent * 24}px;`
              }
            }
          }
        }
      }
    ]
  }
})

// 创建支持class属性的TableCell扩展
const CustomTableCell = TableCell.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          return {
            class: attributes.class,
          }
        },
      },
    }
  },
})

// 创建支持class属性的TableHeader扩展
const CustomTableHeader = TableHeader.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      class: {
        default: null,
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return {}
          }
          return {
            class: attributes.class,
          }
        },
      },
    }
  },
})

export default defineComponent({
  name: 'NoteMarkdownEditor',
  components: {
    EditorContent
  },
  props: {
    initialContent: {
      type: String,
      default: ''
    },
    aiEditingStart: {
      type: Number,
      default: null
    },
    aiEditingEnd: {
      type: Number,
      default: null
    },
    aiEditingEnabled: {
      type: Boolean,
      default: false
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:content', 'edit-command'],
  setup(props, { emit }) {
    const editor = shallowRef<any>(null)
    
    // 表格大小选择器状态
    const hoverRow = ref(3)
    const hoverCol = ref(3)

    // 用于跟踪是否正在内部更新内容
    const isInternalUpdate = ref(false)
    
    // 初始化编辑器
    const initializeEditor = () => {
      editor.value = new Editor({
        extensions: [
          StarterKit.configure({ codeBlock: false }),
          Placeholder.configure({ placeholder: '在此输入Markdown内容...' }),
          IndentExtension, // 添加缩进扩展
          Table.configure({ 
            resizable: true, 
            handleWidth: 5,
            cellMinWidth: 50,
            lastColumnResizable: true,
            HTMLAttributes: { class: 'markdown-table' } 
          }),
          TableRow,
          CustomTableHeader.configure({
            HTMLAttributes: { class: 'markdown-table-header' }
          }),
          CustomTableCell.configure({
            HTMLAttributes: { class: 'markdown-table-cell' }
          }),
          Link.configure({
            openOnClick: true,
            HTMLAttributes: { class: 'markdown-link', target: '_blank', rel: 'noopener noreferrer' },
            autolink: true,
            validate: href => /^https?:\/\//.test(href),
          }),
          CodeBlockLowlight.configure({ lowlight, HTMLAttributes: { class: 'code-block-wrapper' } }),
          ResizableImage, // 使用自定义的可调整大小图片扩展
          Markdown.configure({
            html: true,
            tightLists: true,
            tightListClass: 'tight',
            bulletListMarker: '-',
            linkify: true,
            transformPastedText: true,
            transformCopiedText: true,
          }),
          AIMarkdownDecorator.configure({
            enabled: props.aiEditingEnabled,
            startPosition: props.aiEditingStart,
            endPosition: props.aiEditingEnd,
            marker: '✎',
            markerColor: '#7c4dff',
            backgroundColor: 'rgba(124, 77, 255, 0.1)',
          }),
          AICursorMark.configure({
            markToken: '|AI|',
            cursorColor: '#7c4dff',
            blinkDuration: 800,
            replaceToken: true,
            cursorLabel: 'AI',
          }),
        ],
        content: '', // 先设置为空内容
        autofocus: true,
        editable: true,
        injectCSS: false,
        editorProps: {
          handlePaste: (view, event) => {
            // 只处理文本粘贴，图片粘贴单独处理
            const text = event.clipboardData?.getData('text/plain') || '';
            if (text.includes('#') || text.includes('*') || text.includes('```') || text.includes('-') || text.includes('>') || text.includes('|')) {
              if (editor.value && typeof editor.value.commands.insertMarkdown === 'function') {
                editor.value.commands.insertMarkdown(text);
                return true;
              }
            }
            return false;
          }
        },
        onUpdate: ({ editor }) => {
          // 防止循环更新
          if (isInternalUpdate.value) return;
          
          if (typeof editor.storage.markdown.getMarkdown === 'function') {
            const markdown = editor.storage.markdown.getMarkdown();
            emit('update:content', markdown);
          } else {
            const html = editor.getHTML();
            emit('update:content', html);
          }
        },
        onCreate: ({ editor }) => {
          // 编辑器创建完成后设置初始内容
          if (props.initialContent) {
            nextTick(() => {
              try {
                isInternalUpdate.value = true;
                // 直接使用setContent方法
                editor.commands.setContent(props.initialContent);
                isInternalUpdate.value = false;
              } catch (error) {
                console.error('设置初始内容失败:', error);
                isInternalUpdate.value = false;
              }
            });
          }
        }
      })
    }

    // 初始化编辑器
    initializeEditor()

    // 获取当前编辑器的Markdown内容
    const getCurrentContent = () => {
      if (!editor.value) return '';
      
      try {
        if (typeof editor.value.storage.markdown.getMarkdown === 'function') {
          return editor.value.storage.markdown.getMarkdown();
        } else {
          return editor.value.getHTML();
        }
      } catch (error) {
        console.error('获取当前内容失败:', error);
        return '';
      }
    };

    watch(
      () => props.initialContent,
      (newContent) => {
        if (!editor.value || isInternalUpdate.value) return;
        
        // 获取当前编辑器内容并比较
        const currentContent = getCurrentContent();
        
        // 如果内容没有变化，不需要更新
        if (currentContent === newContent) {
          return;
        }
        
        // 保存当前光标位置
        const currentSelection = editor.value.state.selection;
        const { from, to } = currentSelection;
        
        try {
          isInternalUpdate.value = true;
          
          // 设置新内容
          editor.value.commands.setContent(newContent || '');
          
          // 尝试恢复光标位置
          nextTick(() => {
            try {
              if (editor.value && from >= 0) {
                const docSize = editor.value.state.doc.content.size;
                const newFrom = Math.min(from, docSize);
                const newTo = Math.min(to, docSize);
                
                // 确保位置有效
                if (newFrom >= 0 && newFrom <= docSize) {
                  editor.value.commands.setTextSelection({ from: newFrom, to: newTo });
                }
              }
            } catch (error) {
              console.error('恢复光标位置失败:', error);
            } finally {
              isInternalUpdate.value = false;
            }
          });
        } catch (error) {
          console.error('更新内容失败:', error);
          isInternalUpdate.value = false;
        }
      }
    );

    watch(() => [props.aiEditingStart, props.aiEditingEnd, props.aiEditingEnabled], 
      ([start, end, enabled]) => {
        if (!editor.value) return;
        nextTick(() => {
          editor.value.storage.aiMarkdownDecorator.updateOptions({
            startPosition: start,
            endPosition: end,
            enabled: enabled
          });
          editor.value.view.dispatch(editor.value.view.state.tr);
        });
      }, 
      { immediate: true }
    );

    const setEditable = (editable: boolean) => {
      if (editor.value) {
        editor.value.setEditable(editable);
      }
    };
    watch(() => props.editable, (editable) => {
      setEditable(editable);
    });

    // 图片插入相关
    function insertImageMarkdown(url: string) {
      if (!editor.value) return;
      
      try {
        // 使用ResizableImage扩展插入图片
        const { selection } = editor.value.state;
        const { from } = selection;
        
        // 确保位置有效
        if (from >= 0 && from <= editor.value.state.doc.content.size) {
          editor.value.chain().focus().setImage({ src: url }).run();
        } else {
          // 如果位置无效，在文档末尾插入
          editor.value.chain().focus().insertContentAt(editor.value.state.doc.content.size, {
            type: 'resizableImage',
            attrs: { src: url }
          }).run();
        }
      } catch (error) {
        console.error('插入图片失败:', error);
        // 备用方案：在文档末尾插入
        try {
          editor.value.commands.insertContentAt(editor.value.state.doc.content.size, {
            type: 'resizableImage',
            attrs: { src: url }
          });
        } catch (fallbackError) {
          console.error('备用插入方案也失败:', fallbackError);
        }
      }
    }

    // 拖拽上传图片
    async function handleDrop(e: DragEvent) {
      const files = e.dataTransfer?.files;
      let handled = false;
      if (files && files.length > 0) {
        for (const file of Array.from(files)) {
          if (file.type.startsWith('image/')) {
            const res = await documentsApi.uploadImage(file);
            // uploadImage 已经在API层处理了TOS链接转换
            insertImageMarkdown(res.url);
            handled = true;
          }
        }
      }
      // 处理拖拽的 HTML 图片（支持多个img标签）
      if (!handled) {
        const html = e.dataTransfer?.getData('text/html');
        if (html) {
          // 匹配所有img标签并全部转为markdown
          const imgMatches = [...html.matchAll(/<img[^>]+src=["']([^"'>]+)["']/gi)];
          if (imgMatches.length > 0) {
            imgMatches.forEach(match => {
              // 对拖拽的图片URL也进行TOS链接转换
              const url = match[1];
              const processedUrl = isTOSImageUrl(url) 
                ? convertTOSImageUrl(url) 
                : url;
              insertImageMarkdown(processedUrl);
            });
            handled = true;
          }
        }
      }
      // 处理拖拽的图片链接
      if (!handled) {
        const text = e.dataTransfer?.getData('text/plain');
        if (text && /\.(png|jpe?g|gif|svg|webp)(\?.*)?$/i.test(text)) {
          // 对拖拽的图片链接也进行TOS链接转换
          const processedUrl = isTOSImageUrl(text) 
            ? convertTOSImageUrl(text) 
            : text;
          insertImageMarkdown(processedUrl);
        }
      }
      // 阻止 HTML 片段直接插入
      e.preventDefault();
    }

    // 粘贴上传图片
    async function handlePaste(e: ClipboardEvent) {
      const items = e.clipboardData?.items;
      let handled = false;
      if (items) {
        for (const item of items) {
          if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            if (file) {
              const res = await documentsApi.uploadImage(file);
              // uploadImage 已经在API层处理了TOS链接转换
              insertImageMarkdown(res.url);
              handled = true;
            }
          }
        }
      }
      // 处理粘贴的 HTML 图片（支持多个img标签）
      if (!handled) {
        const html = e.clipboardData?.getData('text/html');
        if (html) {
          // 匹配所有img标签并全部转为markdown
          const imgMatches = [...html.matchAll(/<img[^>]+src=["']([^"'>]+)["']/gi)];
          if (imgMatches.length > 0) {
            imgMatches.forEach(match => {
              // 对粘贴的图片URL也进行TOS链接转换
              const url = match[1];
              const processedUrl = isTOSImageUrl(url) 
                ? convertTOSImageUrl(url) 
                : url;
              insertImageMarkdown(processedUrl);
            });
            handled = true;
          }
        }
      }
      // 处理粘贴的图片链接
      if (!handled) {
        const text = e.clipboardData?.getData('text/plain');
        if (text && /\.(png|jpe?g|gif|svg|webp)(\?.*)?$/i.test(text)) {
          // 对粘贴的图片链接也进行TOS链接转换
          const processedUrl = isTOSImageUrl(text) 
            ? convertTOSImageUrl(text) 
            : text;
          insertImageMarkdown(processedUrl);
        }
      }
      // 阻止 HTML 片段直接插入
      e.preventDefault();
    }

    // 工具栏方法
    const setHeading = (level: string) => {
      if (!editor.value) return;
      
      if (level === 'paragraph') {
        editor.value.chain().focus().setParagraph().run();
      } else {
        const headingLevel = parseInt(level) as 1 | 2 | 3 | 4 | 5 | 6;
        editor.value.chain().focus().toggleHeading({ level: headingLevel }).run();
      }
    };

    // 处理图片上传
    const handleImageUpload = async (event: Event) => {
      const target = event.target as HTMLInputElement;
      const file = target.files?.[0];
      if (file && file.type.startsWith('image/')) {
        try {
          const res = await documentsApi.uploadImage(file);
          insertImageMarkdown(res.url);
          // 清空文件输入框
          target.value = '';
        } catch (error) {
          console.error('图片上传失败:', error);
          // 这里可以添加错误提示
        }
      }
    };

    const indentContent = () => {
      if (!editor.value) return;
      
      if (editor.value.isActive('listItem')) {
        editor.value.chain().focus().sinkListItem('listItem').run();
      } else {
        // 自定义缩进逻辑
        editor.value.commands.command(({ tr, state }) => {
          const { selection } = state;
          const { from, to } = selection;
          
          tr.doc.nodesBetween(from, to, (node, pos) => {
            if (node.type.name === 'paragraph') {
              const attrs = { ...node.attrs };
              const currentIndent = attrs.indent || 0;
              attrs.indent = Math.min(currentIndent + 1, 8);
              tr.setNodeMarkup(pos, null, attrs);
            }
          });
          
          return true;
        });
      }
    };

    const outdentContent = () => {
      if (!editor.value) return;
      
      if (editor.value.isActive('listItem')) {
        editor.value.chain().focus().liftListItem('listItem').run();
      } else {
        // 自定义减少缩进逻辑
        editor.value.commands.command(({ tr, state }) => {
          const { selection } = state;
          const { from, to } = selection;
          
          tr.doc.nodesBetween(from, to, (node, pos) => {
            if (node.type.name === 'paragraph') {
              const attrs = { ...node.attrs };
              const currentIndent = attrs.indent || 0;
              attrs.indent = Math.max(currentIndent - 1, 0);
              tr.setNodeMarkup(pos, null, attrs);
            }
          });
          
          return true;
        });
      }
    };

    const addLink = () => {
      if (!editor.value) return;
      
      const url = window.prompt('请输入链接地址:');
      
      if (url) {
        editor.value.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
      }
    };

    const insertTable = () => {
      if (!editor.value) return;
      
      editor.value.chain().focus().insertTable({ 
        rows: 3, 
        cols: 3, 
        withHeaderRow: true 
      }).run();
    };

    // 表格操作方法
    const addRowBefore = () => {
      if (!editor.value) return;
      editor.value.chain().focus().addRowBefore().run();
    };
    
    const addRowAfter = () => {
      if (!editor.value) return;
      editor.value.chain().focus().addRowAfter().run();
    };
    
    const deleteRow = () => {
      if (!editor.value) return;
      editor.value.chain().focus().deleteRow().run();
    };
    
    const addColumnBefore = () => {
      if (!editor.value) return;
      editor.value.chain().focus().addColumnBefore().run();
    };
    
    const addColumnAfter = () => {
      if (!editor.value) return;
      editor.value.chain().focus().addColumnAfter().run();
    };
    
    const deleteColumn = () => {
      if (!editor.value) return;
      editor.value.chain().focus().deleteColumn().run();
    };
    
    const deleteTable = () => {
      if (!editor.value) return;
      editor.value.chain().focus().deleteTable().run();
    };
    
    // 检查是否在表格中
    const isInTable = () => {
      return editor.value && editor.value.isActive('table');
    };

    // 插入自定义大小的表格
    const insertCustomTable = (rows: number, cols: number) => {
      if (!editor.value) return;
      
      editor.value.chain().focus().insertTable({ 
        rows: rows, 
        cols: cols, 
        withHeaderRow: true 
      }).run();
    };
    
    // 检查是否在表格中且为紧凑模式（不换行）
    const isTableTextWrapped = () => {
      if (!editor.value || !editor.value.isActive('table')) return false;
      
      // 检查当前选中的单元格是否有no-wrap类（紧凑模式）
      const { state } = editor.value;
      const { selection } = state;
      const { $from } = selection;
      
      // 查找当前单元格
      let cell = null;
      for (let depth = $from.depth; depth > 0; depth--) {
        const node = $from.node(depth);
        if (node.type.name === 'tableCell' || node.type.name === 'tableHeader') {
          cell = node;
          break;
        }
      }
      
      // 返回true表示当前是正常换行模式，false表示紧凑模式
      return !(cell && cell.attrs.class && cell.attrs.class.includes('no-wrap'));
    };

    // 切换表格文本换行模式
    const toggleTableTextWrap = () => {
      if (!editor.value || !editor.value.isActive('table')) return;
      
      editor.value.commands.command(({ tr, state, dispatch }) => {
        const { selection } = state;
        const { $from, $to } = selection;
        
        // 找到所有选中的表格单元格
        const updates: Array<{ pos: number; attrs: any }> = [];
        
        state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {
          if (node.type.name === 'tableCell' || node.type.name === 'tableHeader') {
            const currentClass = node.attrs.class || '';
            const hasNoWrapClass = currentClass.includes('no-wrap');
            
            let newClass;
            if (hasNoWrapClass) {
              // 当前是紧凑模式，切换到正常换行模式
              newClass = currentClass.replace(/\s*no-wrap\s*/g, '').trim();
            } else {
              // 当前是正常模式，切换到紧凑模式
              newClass = currentClass ? `${currentClass} no-wrap` : 'no-wrap';
            }
            
            updates.push({
              pos,
              attrs: { ...node.attrs, class: newClass || null }
            });
          }
        });
        
        // 应用所有更新
        updates.forEach(({ pos, attrs }) => {
          tr.setNodeMarkup(pos, null, attrs);
        });
        
        if (dispatch) {
          dispatch(tr);
        }
        
        return true;
      });
    };

    onBeforeUnmount(() => {
      // 清理编辑器
      if (editor.value) {
        editor.value.destroy()
      }
    })

    return {
      editor,
      handleDrop,
      handlePaste,
      setHeading,
      indentContent,
      outdentContent,
      addLink,
      insertTable,
      addRowBefore,
      addRowAfter,
      deleteRow,
      addColumnBefore,
      addColumnAfter,
      deleteColumn,
      deleteTable,
      isInTable,
      insertCustomTable,
      hoverRow,
      hoverCol,
      handleImageUpload,
      isTableTextWrapped,
      toggleTableTextWrap
    }
  }
})
</script>

<style scoped>
.editor-container {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 2px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #374151;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
}

.toolbar-btn:hover {
  background: #e5e7eb;
  color: #111827;
}

.toolbar-btn:active {
  background: #d1d5db;
}

.toolbar-btn.is-active {
  background: #dbeafe;
  color: #1d4ed8;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn:disabled:hover {
  background: transparent;
}

.toolbar-select {
  height: 32px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  padding: 0 8px;
  cursor: pointer;
  min-width: 100px;
}

.toolbar-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background: #d1d5db;
  margin: 0 4px;
}

.editor-content-wrapper {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  background: white;
  border-radius: 0 0 8px 8px;
}

:deep(.markdown-image) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.markdown-image:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ProseMirror) {
  outline: none;
  padding: 24px;
  min-height: calc(100vh - 300px);
  overflow-y: auto;
}

:deep(.ProseMirror:focus) {
  outline: none;
}

/* 标题样式 */
:deep(.ProseMirror h1) {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin: 2rem 0 1rem 0;
  line-height: 1.2;
}

:deep(.ProseMirror h2) {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 1.5rem 0 0.75rem 0;
  line-height: 1.3;
}

:deep(.ProseMirror h3) {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 1.25rem 0 0.5rem 0;
  line-height: 1.4;
}

:deep(.ProseMirror h4),
:deep(.ProseMirror h5),
:deep(.ProseMirror h6) {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
}

/* 段落样式 */
:deep(.ProseMirror p) {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* 列表样式 */
:deep(.ProseMirror ul),
:deep(.ProseMirror ol) {
  color: #374151;
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

:deep(.ProseMirror li) {
  margin-bottom: 0.25rem;
}

/* 引用块样式 */
:deep(.ProseMirror blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  color: #6b7280;
  font-style: italic;
  margin: 1rem 0;
}

/* 代码样式 */
:deep(.ProseMirror code) {
  background-color: #000000;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
  font-size: 0.875rem;
  color: #dc2626;
}

:deep(.ProseMirror pre) {
  background-color: #282c34 !important;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

:deep(.ProseMirror pre code) {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

/* 表格样式 */
:deep(.ProseMirror table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  border: 1px solid #e5e7eb;
  table-layout: fixed; /* 固定表格布局，防止列宽随内容变化 */
  position: relative;
}

:deep(.ProseMirror th),
:deep(.ProseMirror td) {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
  vertical-align: top;
  word-wrap: break-word; /* 长文本换行 */
  overflow-wrap: break-word;
  position: relative;
}

:deep(.ProseMirror th) {
  background-color: #f9fafb;
  font-weight: 600;
}

/* 表格列调整手柄样式 */
:deep(.ProseMirror .tableWrapper) {
  overflow-x: auto;
  margin: 1rem 0;
}

:deep(.ProseMirror .resize-cursor) {
  cursor: col-resize;
}

/* 列调整手柄 */
:deep(.ProseMirror .column-resize-handle) {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  cursor: col-resize;
  background-color: #3b82f6;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

:deep(.ProseMirror .column-resize-handle:hover),
:deep(.ProseMirror .column-resize-handle.dragging) {
  opacity: 1;
}

/* 当鼠标悬停在表格单元格上时显示调整手柄 */
:deep(.ProseMirror th:hover .column-resize-handle),
:deep(.ProseMirror td:hover .column-resize-handle) {
  opacity: 0.6;
}

/* 表格选中状态 */
:deep(.ProseMirror .selectedCell) {
  background: rgba(59, 130, 246, 0.1);
  border: 2px solid #3b82f6;
}

/* 表格头部样式增强 */
:deep(.ProseMirror .markdown-table-header) {
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #d1d5db;
}

/* 表格单元格样式增强 */
:deep(.ProseMirror .markdown-table-cell) {
  position: relative;
}

/* 确保表格响应式 */
:deep(.ProseMirror .markdown-table) {
  max-width: 100%;
  overflow-x: auto;
}

/* 表格内容超出处理 */
:deep(.ProseMirror table td),
:deep(.ProseMirror table th) {
  min-width: 50px; /* 最小列宽 */
  max-width: none;
  white-space: normal; /* 允许正常换行 */
  word-wrap: break-word; /* 长单词换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
  hyphens: auto; /* 自动断字 */
}

/* 紧凑模式：不换行，显示省略号 */
:deep(.ProseMirror table td.no-wrap),
:deep(.ProseMirror table th.no-wrap) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 允许表格内容换行（当需要时） */
:deep(.ProseMirror table td.wrap-content),
:deep(.ProseMirror table th.wrap-content) {
  white-space: normal;
  word-break: break-word;
  line-height: 1.5;
  padding: 0.75rem 0.5rem; /* 增加垂直间距以适应多行文本 */
}

/* 表格拖拽调整时的视觉反馈 */
:deep(.ProseMirror table.resize-active) {
  border: 2px solid #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 表格容器样式 */
:deep(.ProseMirror .tableWrapper) {
  position: relative;
  margin: 1rem 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

/* 链接样式 */
:deep(.ProseMirror a) {
  color: #3b82f6;
  text-decoration: underline;
}

:deep(.ProseMirror a:hover) {
  color: #1d4ed8;
}

/* 分割线样式 */
:deep(.ProseMirror hr) {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

/* 加粗和斜体样式 */
:deep(.ProseMirror strong) {
  font-weight: 700;
}

:deep(.ProseMirror em) {
  font-style: italic;
}

:deep(.ProseMirror s) {
  text-decoration: line-through;
}

/* 缩进段落样式 */
:deep(.ProseMirror p[data-indent]) {
  margin-left: calc(var(--indent-level, 0) * 24px);
}

/* 表格大小选择器样式 */
.table-size-selector {
  padding: 8px;
}

.size-grid {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin: 8px 0;
}

.size-row {
  display: flex;
  gap: 2px;
}

.size-cell {
  width: 16px;
  height: 16px;
  border: 1px solid #d1d5db;
  background: white;
  cursor: pointer;
  transition: all 0.15s ease;
}

.size-cell:hover,
.size-cell.active {
  background: #3b82f6;
  border-color: #3b82f6;
}

.size-display {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

/* 小分割线样式 */
.toolbar-divider-small {
  width: 1px;
  height: 16px;
  background: #d1d5db;
  margin: 0 2px;
}

/* 可调整大小的图片容器样式 */
:deep(.resizable-image-container) {
  position: relative;
  display: inline-block;
  margin: 1em 0;
  cursor: default;
  max-width: 100%;
}

:deep(.resizable-image-container:hover) {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

:deep(.resizable-image-container.is-resizing) {
  outline: 2px solid #7c4dff;
  outline-offset: 2px;
}

:deep(.resizable-image) {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
  cursor: pointer;
}

:deep(.resizable-image:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 调整手柄样式 */
:deep(.resize-handles) {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

:deep(.resizable-image-container:hover .resize-handles) {
  opacity: 1;
  pointer-events: auto;
}

:deep(.resize-handle) {
  position: absolute;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

:deep(.resize-handle:hover) {
  background: #2563eb;
  transform: scale(1.2);
}

/* 角落手柄 */
:deep(.resize-handle.corner) {
  width: 12px;
  height: 12px;
}

:deep(.resize-handle.corner.top-left) {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

:deep(.resize-handle.corner.top-right) {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

:deep(.resize-handle.corner.bottom-left) {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

:deep(.resize-handle.corner.bottom-right) {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}
</style> 