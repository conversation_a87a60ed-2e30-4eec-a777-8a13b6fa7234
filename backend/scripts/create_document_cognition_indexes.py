#!/usr/bin/env python3
"""
创建文档认知数据库索引脚本

运行方式:
python backend/scripts/create_document_cognition_indexes.py
"""

import asyncio
import logging
from motor.motor_asyncio import AsyncIOMotorClient
from backend.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_document_cognition_indexes():
    """创建文档认知集合的索引"""
    try:
        # 连接MongoDB
        mongo_uri = settings.MONGO_URI
        if "mongodb://" in mongo_uri and "@" not in mongo_uri:
            mongo_uri = mongo_uri.replace("mongodb://", "mongodb://dair:Dair1234@")
        
        client = AsyncIOMotorClient(mongo_uri)
        db = client[settings.MONGO_DB_NAME]
        collection = db["document_cognitions"]
        
        logger.info("开始创建文档认知集合索引...")
        
        # 1. 基础索引 - 用户ID
        await collection.create_index([("user_id", 1)], name="idx_user_id")
        logger.info("✓ 创建用户ID索引")
        
        # 2. 时间索引 - 创建时间和更新时间
        await collection.create_index([("created_at", -1)], name="idx_created_at_desc")
        await collection.create_index([("updated_at", -1)], name="idx_updated_at_desc")
        logger.info("✓ 创建时间索引")
        
        # 3. 来源文档索引
        await collection.create_index([("source_document_ids", 1)], name="idx_source_document_ids")
        logger.info("✓ 创建来源文档索引")
        
        # 4. 话题索引
        await collection.create_index([("primary_topic", 1)], name="idx_primary_topic")
        await collection.create_index([("related_topic", 1)], name="idx_related_topic")
        logger.info("✓ 创建话题索引")
        
        # 5. 认知类型索引
        await collection.create_index([("cognition_type", 1)], name="idx_cognition_type")
        logger.info("✓ 创建认知类型索引")
        
        # 6. 评分索引
        await collection.create_index([("value_score", -1)], name="idx_value_score_desc")
        await collection.create_index([("novelty_score", -1)], name="idx_novelty_score_desc")
        logger.info("✓ 创建评分索引")
        
        # 7. 合成认知索引
        await collection.create_index([("is_synthesized", 1)], name="idx_is_synthesized")
        await collection.create_index([("parent_cognition_ids", 1)], name="idx_parent_cognition_ids")
        logger.info("✓ 创建合成认知索引")
        
        # 8. 复合索引 - 用户和时间
        await collection.create_index([
            ("user_id", 1),
            ("created_at", -1)
        ], name="idx_user_created_at")
        logger.info("✓ 创建用户和时间复合索引")
        
        # 9. 复合索引 - 用户和话题
        await collection.create_index([
            ("user_id", 1),
            ("primary_topic", 1)
        ], name="idx_user_primary_topic")
        logger.info("✓ 创建用户和主要话题复合索引")
        
        # 10. 复合索引 - 用户和评分
        await collection.create_index([
            ("user_id", 1),
            ("value_score", -1)
        ], name="idx_user_value_score")
        logger.info("✓ 创建用户和价值评分复合索引")
        
        # 11. 复合索引 - 用户和合成状态
        await collection.create_index([
            ("user_id", 1),
            ("is_synthesized", 1),
            ("created_at", -1)
        ], name="idx_user_synthesized_time")
        logger.info("✓ 创建用户、合成状态和时间复合索引")
        
        # 12. 文本搜索索引 - 为中文内容创建文本索引
        try:
            await collection.create_index([
                ("question_zh", "text"),
                ("answer_zh", "text"),
                ("think_zh", "text"),
                ("abstract_zh", "text")
            ], name="idx_text_search_zh", default_language="none")
            logger.info("✓ 创建中文文本搜索索引")
        except Exception as e:
            logger.warning(f"创建文本搜索索引失败（可能已存在）: {e}")
        
        # 13. 稀疏索引 - 可选字段
        await collection.create_index([("confidence_level", 1)], sparse=True, name="idx_confidence_level_sparse")
        await collection.create_index([("temporal_relevance", 1)], sparse=True, name="idx_temporal_relevance_sparse")
        logger.info("✓ 创建稀疏索引")
        
        # 14. 唯一索引 - 防止重复提取同样的认知
        try:
            await collection.create_index([
                ("user_id", 1),
                ("source_document_ids", 1),
                ("question_zh", 1)
            ], unique=True, name="idx_unique_user_source_question")
            logger.info("✓ 创建唯一索引（防止重复认知）")
        except Exception as e:
            logger.warning(f"创建唯一索引失败（可能已存在或有冲突数据）: {e}")
        
        # 列出所有索引
        indexes = await collection.list_indexes().to_list(length=None)
        logger.info(f"文档认知集合共有 {len(indexes)} 个索引:")
        for idx in indexes:
            logger.info(f"  - {idx.get('name', 'unknown')}: {idx.get('key', {})}")
        
        logger.info("所有索引创建完成！")
        
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        raise
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    asyncio.run(create_document_cognition_indexes()) 