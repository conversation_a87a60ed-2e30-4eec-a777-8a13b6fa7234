from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import Optional, List, Literal
import os
import yaml
from pathlib import Path


class Settings(BaseSettings):
    """应用配置"""
    
    # 环境配置 - 最重要的新增部分
    ENVIRONMENT: Literal["development", "testing", "production"] = Field(
        default="development", 
        description="运行环境: development(开发), testing(测试), production(生产)"
    )
    
    # 可选的外部yaml配置文件路径
    CONFIG_YAML_PATH: Optional[str] = Field(
        default=None, 
        description="外部yaml配置文件路径，用于覆盖默认配置"
    )
    
    # API配置
    API_HOST: str = Field(default="0.0.0.0", description="API主机")
    API_PORT: int = Field(default=8000, description="API端口")
    API_PREFIX: str = Field(default="/api", description="API前缀")
    
    # MongoDB配置
    MONGO_URI: str = Field(default="mongodb://mongoreplica98081f54cfd50.mongodb.ap-southeast-1.ivolces.com:3717,mongoreplica98081f54cfd51.mongodb.ap-southeast-1.ivolces.com:3717", description="MongoDB连接URI")
    MONGO_DB_NAME: str = Field(default="dair", description="MongoDB数据库名称")
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    REDIS_MAX_CONNECTIONS: int = Field(default=100, description="Redis最大连接数")
    REDIS_SOCKET_TIMEOUT: int = Field(default=5, description="Redis套接字超时时间（秒）")
    REDIS_SESSION_TTL: int = Field(default=86400, description="Redis会话缓存过期时间（秒），默认24小时")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE_DIR: str = Field(default="/app/dair/logs", description="日志文件目录")
    LOG_FILE: str = Field(default="/app/dair/logs/dair.log", description="日志文件路径")
    
    # 跨域配置
    CORS_ORIGINS: List[str] = Field(default=["*"], description="允许的跨域来源")
    
    # 管理员用户配置
    ADMIN_USERNAME: str = Field(default="admin", description="管理员用户名")
    ADMIN_EMAIL: str = Field(default="<EMAIL>", description="管理员邮箱")
    ADMIN_PASSWORD: str = Field(default="admin1234", description="管理员密码")
    
    # 文件上传配置
    UPLOAD_PATH: str = Field(default="/app/dair/uploads", description="文件上传路径")
    
    # TOS对象存储配置
    TOS_ENDPOINT: str = Field(default="https://tos-ap-southeast-3.volces.com", description="TOS服务端点")
    TOS_ACCESS_KEY: str = Field(default="AKLTOTEzZGEyOGUxZWZkNGQ1Y2FkYjA2MDNlZjljNmIzYTE", description="TOS访问密钥")
    TOS_SECRET_KEY: str = Field(default="T0RReE5EQTNZVGsyWlRjeU5EVTVZamxrTUdWaVlqRXhZakV6TVRobVpHWQ==", description="TOS密钥")
    TOS_BUCKET_NAME: str = Field(default="deepcognition", description="TOS存储桶名称")
    TOS_REGION: str = Field(default="ap-southeast-1", description="TOS区域")

    # AI服务配置 - 使用cognition platform相同的配置
    # Azure OpenAI配置
    AZURE_API_KEY: str = Field(default="f847dd7d5eff4fc0bff57d061813a4ab", description="Azure OpenAI API密钥")
    AZURE_API_URL: str = Field(default="********************************", description="Azure OpenAI API端点")
    AZURE_API_VERSION: str = Field(default="2025-01-01-preview", description="Azure API版本")
    
    # 认知提取模型配置
    AI_MODEL_COGNITION_EXTRACTION: str = Field(default="gpt-4.1-mini", description="认知提取使用的AI模型")
    AI_MODEL_COGNITION_SYNTHESIS: str = Field(default="gpt-4.1-mini", description="认知合成使用的AI模型")
    AI_REQUEST_TIMEOUT: int = Field(default=180, description="AI请求超时时间（秒）")
    
    # 兼容性：保留OpenRouter配置作为备用
    OPENROUTER_API_KEY: Optional[str] = Field(default=None, description="OpenRouter API密钥（备用）")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化后加载yaml配置
        self._load_yaml_config()
        # 根据环境调整配置
        self._adjust_config_by_environment()

    def _load_yaml_config(self):
        """加载外部yaml配置文件"""
        yaml_path = self.CONFIG_YAML_PATH
        
        if not yaml_path:
            # 如果没有指定路径，尝试几个常见位置
            possible_paths = [
                "/etc/dair/config.yaml",  # 系统配置目录
                "/app/config/config.yaml",  # 容器内配置目录
                "/opt/dair/config.yaml",   # 应用配置目录
                os.path.expanduser("~/.dair/config.yaml"),  # 用户配置目录
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    yaml_path = path
                    break
        
        if yaml_path and os.path.exists(yaml_path):
            try:
                with open(yaml_path, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                
                if yaml_config:
                    # 更新配置，yaml优先级更高
                    for key, value in yaml_config.items():
                        if hasattr(self, key.upper()):
                            setattr(self, key.upper(), value)
                            
                print(f"已加载yaml配置文件: {yaml_path}")
            except Exception as e:
                print(f"加载yaml配置文件失败: {e}")

    def _adjust_config_by_environment(self):
        """根据环境调整配置"""
        if self.ENVIRONMENT == "development":
            # 开发环境配置
            if not self.CONFIG_YAML_PATH:  # 只有未通过yaml设置时才使用默认值
                self.LOG_LEVEL = "INFO"
                self.CORS_ORIGINS = ["http://localhost:3000", "http://127.0.0.1:3000"]
                
        elif self.ENVIRONMENT == "testing":
            # 测试环境配置
            if not self.CONFIG_YAML_PATH:
                self.LOG_LEVEL = "INFO"
                self.MONGO_DB_NAME = "dair_test"
                
        elif self.ENVIRONMENT == "production":
            # 生产环境配置
            if not self.CONFIG_YAML_PATH:
                self.LOG_LEVEL = "WARNING"
                self.CORS_ORIGINS = []  # 生产环境需要明确指定允许的域名
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8"
    }


settings = Settings() 