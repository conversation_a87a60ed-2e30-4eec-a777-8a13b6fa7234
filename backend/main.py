import logging
import asyncio
import yaml
import json
import uuid
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks, Depends, Response, Form, UploadFile, File, Query
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime, timezone, timedelta
import copy
import os
from pathlib import Path
import tempfile
import shutil

import markdown2
from md2pdf import md2pdf
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
from bs4 import BeautifulSoup
import io
import html

from backend.config import settings
from backend.documents.models import Document
from backend.documents.dependencies import get_document_by_id
from dc_agents.src.deep_cognition.research_agent import ResearchAgent, DEBUG_MODE
from dc_agents.src.deep_cognition.browse_agent import BrowseAgent
from dc_agents.src.services.llm_service import LLMService
from dc_agents.src.services.search_service import SearchService
from dc_agents.src.agents.research_memory import ResearchMemory
from dc_agents.src.agents.file_context import FileContext
from backend.auth.dependencies import get_current_active_user, get_current_admin_user, check_user_token_budget, check_user_token_budget
from backend.auth.models import UserDB

# 导入认证路由
from backend.auth.router import router as auth_router
from backend.auth.admin_router import router as admin_token_router  # 管理员token路由
from backend.documents.router import router as documents_router
from backend.documents.router import admin_router  # 管理员认知审核路由
from backend.conversation.router import router as conversation_router
from backend.cognition.router import router as cognition_router
from backend.ppt.router import router as ppt_router

from backend.conversation.session_store import SessionStore

from contextlib import asynccontextmanager
from backend.auth.router import init_admin_user

# 在现有导入后添加
from backend.db.dependencies import startup_mongodb, shutdown_mongodb
from backend.redis.dependencies import reset_redis_for_restart, shutdown_redis_client

# 创建获取东八区（北京时间）的辅助函数
def get_cn_time():
    """返回东八区（北京时间）的当前时间"""
    return datetime.now(timezone(timedelta(hours=8)))

# 配置日志
logging.basicConfig(
    level=settings.LOG_LEVEL,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 设置第三方库的日志级别，避免DEBUG日志过多
logging.getLogger("pymongo").setLevel(logging.WARNING)
logging.getLogger("pymongo.command").setLevel(logging.WARNING)
logging.getLogger("pymongo.connection").setLevel(logging.WARNING)
logging.getLogger("motor").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

# 记录调试模式状态
logger.info(f"DAIR DEBUG MODE: {'启用' if DEBUG_MODE else '禁用'}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 确保日志目录存在
    ensure_directory_exists(settings.LOG_FILE_DIR)
    # 确保上传目录存在
    ensure_directory_exists(settings.UPLOAD_PATH)
    logger.info("应用启动，必要的目录已创建")
    
    # 启动MongoDB连接
    await startup_mongodb()
    logger.info("MongoDB依赖注入服务已启动")
    
    # 重置Redis应用相关数据
    try:
        success = await reset_redis_for_restart()
        if success:
            logger.info("Redis应用数据重置成功")
        else:
            logger.warning("Redis应用数据重置失败，但不影响应用启动")
    except Exception as redis_e:
        logger.error(f"Redis重置失败: {str(redis_e)}，但不影响应用启动")
    
    # 初始化管理员用户
    await init_admin_user()
    
    # 启动事件：确保repository已初始化
    from backend.conversation.dependencies import get_session_repository
    session_repo = get_session_repository()
    logger.info("SessionRepository已启动，批量写入任务已开始")
    
    
    yield
    
    
    # 关闭事件：确保所有缓冲的消息都写入数据库
    try:
        await session_repo.shutdown()
        logger.info("应用关闭完成，所有消息已保存")
    except Exception as e:
        logger.error(f"应用关闭时出错: {str(e)}")
    
    # 关闭Redis连接
    try:
        await shutdown_redis_client()
        logger.info("Redis服务已关闭")
    except Exception as e:
        logger.error(f"关闭Redis连接时出错: {str(e)}")
    
    # 关闭MongoDB连接
    try:
        await shutdown_mongodb()
        logger.info("MongoDB依赖注入服务已关闭")
    except Exception as e:
        logger.error(f"关闭MongoDB连接时出错: {str(e)}")

# 创建FastAPI应用实例
app = FastAPI(
    title="DAIR Research API",
    description="与研究智能体交互的API服务",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# WebSocket心跳配置
WS_HEARTBEAT_INTERVAL = 1.0      # 心跳间隔，单位秒
WS_HEARTBEAT_TIMEOUT = 0.5       # 心跳响应超时，单位秒
WS_MAX_HEARTBEAT_FAILURES = 3    # 最大连续心跳失败次数

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含认证路由
app.include_router(auth_router)
app.include_router(admin_token_router)
app.include_router(documents_router) 
app.include_router(admin_router)  # 管理员认知审核路由
app.include_router(conversation_router)
app.include_router(cognition_router)
app.include_router(ppt_router)

# 创建会话存储
session_store = SessionStore()

# 定义请求和响应模型
class ResearchRequest(BaseModel):
    question: str
    benchmark_id: Optional[int] = None
    enable_cognition_search: Optional[bool] = False  # 添加新的参数

class FeedbackRequest(BaseModel):
    feedback: str

class EditRequest(BaseModel):
    """编辑请求模型"""
    edit_content: str
    edit_type: Optional[str] = "chat_edit"  # 编辑类型：chat_edit（对话编辑）

class ClarificationAnswerRequest(BaseModel):
    answers: Union[List[str], List[Dict[str, Any]]]  # 支持两种格式：简单的字符串列表或包含ID的字典列表

class ResearchResponse(BaseModel):
    session_id: str
    message: str

# 存储活跃的研究会话
active_sessions: Dict[str, Dict[str, Any]] = {}
# 存储会话相关的WebSocket连接
websocket_connections: Dict[str, WebSocket] = {}
# 存储会话相关的队列
message_queues: Dict[str, asyncio.Queue] = {}
# 存储用户反馈
user_feedbacks: Dict[str, List[str]] = {}
# 存储澄清问题
clarification_questions: Dict[str, List[Dict[str, Any]]] = {}


def ensure_directory_exists(directory_path: str) -> None:
    """确保目录存在，如果不存在则创建"""
    try:
        os.makedirs(directory_path, exist_ok=True)
        logger.info(f"目录已创建或已存在: {directory_path}")
    except Exception as e:
        logger.error(f"创建目录失败: {directory_path}, 错误: {str(e)}")
        raise
    
# 创建和获取服务实例
def get_services():
    """创建并返回服务实例"""
    try:
        # 加载配置
        agents_config = yaml.load(open("dc_agents/config/config.yaml"), Loader=yaml.FullLoader)
        service_config = yaml.load(open("dc_agents/config/service_config.yaml"), Loader=yaml.FullLoader)
        
        # # 创建LLM服务
        # llms = {}
        # for model, _config in service_config["llms"].items():
        #     llms[model] = LLMService(_config)
        
        # 初始化浏览智能体
        browse_config = agents_config.get("browse_agent", {})
        browse_agent = BrowseAgent(
            config=browse_config,
            # llm_services=llms
        )
        
        # 初始化搜索服务
        search_service = SearchService(service_config["search_service"])
        
        return {
            # "llms": llms,
            "browse_agent": browse_agent,
            "search_service": search_service,
            "agents_config": agents_config
        }
    except Exception as e:
        logger.error(f"初始化服务出错: {e}")
        raise e

# 初始化研究智能体
def get_research_agent(services=Depends(get_services), conversation_id: str = None):
    """创建并返回研究智能体实例"""
    if not conversation_id:
        raise HTTPException(status_code=400, detail="conversation_id is required")
    research_config = services["agents_config"].get("research_agent", {})
    logger.info(f"research_config: {research_config},conversation_id: {conversation_id}")
    research_agent = ResearchAgent(
        config=research_config,
        # llm_services=services["llms"],
        search_service=services["search_service"],
        browse_agent=services["browse_agent"],
        conversation_id=conversation_id
    )
    return research_agent

# 注意：由于使用了lifespan上下文管理器，@app.on_event装饰器会被忽略
# 所有启动和关闭逻辑都已移至lifespan函数中

# 根路由
@app.get("/")
async def root():
    return {"message": "欢迎使用DAIR Research API"}

# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "ok"}

# API版本
@app.get("/version")
async def get_version():
    return {"version": app.version}

# 获取用户对话历史
@app.get("/api/chats/my")
async def get_my_chats(
    current_user: UserDB = Depends(get_current_active_user),
    skip: int = 0,
    limit: int = 20
):
    """获取我的研究会话 - 支持分页"""
    try:
        # 参数验证
        if limit > 100:
            limit = 100  # 最大限制100条
        if limit < 1:
            limit = 20   # 默认20条
        if skip < 0:
            skip = 0
            
        # 从持久化存储中获取会话列表
        logger.info(f"获取会话列表: 用户{current_user.id}, skip={skip}, limit={limit}")
        sessions = await session_store.list_sessions(
            user_id=str(current_user.id), 
            skip=skip, 
            limit=limit
        )
        
        # 格式化返回结果
        chats = []
        for session in sessions:
            chat = {
                "id": session.session_id,
                "prompt": session.question,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.last_activity.isoformat(),
                "status": session.status
            }
            chats.append(chat)
        
        return {
            "chats": chats,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "count": len(chats)
            }
        }
    except Exception as e:
        logger.error(f"获取会话列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

# 加载benchmark测试数据
def load_benchmark_data():
    """加载benchmark测试数据"""
    try:
        benchmark_file_path = "dc_agents/src/dataset/decrypted_data.json"
        with open(benchmark_file_path, "r", encoding="utf-8") as f:
            benchmark_data = json.load(f)
        return benchmark_data
    except Exception as e:
        logger.error(f"加载benchmark数据出错: {e}")
        return []

# 获取benchmark数据
@app.get("/benchmark/list")
async def get_benchmark_list():
    """获取所有benchmark题目列表"""
    benchmark_data = load_benchmark_data()
    # 返回简化版本，仅包含ID、问题和话题
    simplified_data = [
        {
            "id": idx,
            "problem": item["problem"],
            "topic": item.get("problem_topic", "未知")
        }
        for idx, item in enumerate(benchmark_data)
    ]
    return {"items": simplified_data}

@app.get("/benchmark/{item_id}")
async def get_benchmark_item(item_id: int):
    """获取特定benchmark题目详情"""
    benchmark_data = load_benchmark_data()
    if 0 <= item_id < len(benchmark_data):
        return benchmark_data[item_id]
    raise HTTPException(status_code=404, detail="找不到指定的benchmark题目")

# 定义模型配置请求模型
class UserModelPreferences(BaseModel):
    action_selection_model: Optional[Dict[str, Any]] = None
    report_editing_model: Optional[Dict[str, Any]] = None
    url_selection_model: Optional[Dict[str, Any]] = None

# 定义内容偏好请求模型
class ContentPreferenceRequest(BaseModel):
    conversation_id: str
    url: str
    title: str
    liked: bool
    timestamp: Optional[str] = None

# 获取可用模型列表
@app.get("/api/models/available")
async def get_available_models(current_user: UserDB = Depends(get_current_active_user)):
    """获取可用的模型列表"""
    try:
        # 加载服务配置
        service_config_path = "dc_agents/config/service_config.yaml"
        with open(service_config_path, "r", encoding="utf-8") as f:
            service_config = yaml.safe_load(f)
        
        # 加载当前研究智能体配置
        agent_config_path = "dc_agents/config/config.yaml"
        with open(agent_config_path, "r", encoding="utf-8") as f:
            agent_config = yaml.safe_load(f)
        
        # 获取用户当前模型偏好
        user_preferences = await session_store.get_user_models_preference(str(current_user.id))
        
        
        # 准备返回的数据
        available_models = []
        
        # 从llms配置中获取可用模型
        if "llms" in service_config:
            for model_name, model_config in service_config["llms"].items():
                available_models.append({
                    "id": model_name,
                    "name": model_name,
                    "api_url": model_config.get("api_url", ""),
                    "is_reasoning_model": model_config.get("is_reasoning_model", False)
                })
        
        # 获取当前配置中使用的模型
        current_models = {
            "action_selection_model": agent_config.get("research_agent", {}).get("action_selection_model", {}).get("name", ""),
            "report_editing_model": agent_config.get("research_agent", {}).get("report_editing_model", {}).get("name", ""),
            "url_selection_model": agent_config.get("research_agent", {}).get("url_selection_model", {}).get("name", "")
        }
        
        # 加入用户自定义的配置（如果有）
        if user_preferences:
            if "action_selection_model" in user_preferences and "name" in user_preferences["action_selection_model"]:
                current_models["action_selection_model"] = user_preferences["action_selection_model"]["name"]
            if "report_editing_model" in user_preferences and "name" in user_preferences["report_editing_model"]:
                current_models["report_editing_model"] = user_preferences["report_editing_model"]["name"]
            if "url_selection_model" in user_preferences and "name" in user_preferences["url_selection_model"]:
                current_models["url_selection_model"] = user_preferences["url_selection_model"]["name"]
        
        return {
            "available_models": available_models,
            "current_models": current_models,
            "default_models": {
                "action_selection_model": agent_config.get("research_agent", {}).get("action_selection_model", {}).get("name", ""),
                "report_editing_model": agent_config.get("research_agent", {}).get("report_editing_model", {}).get("name", ""),
                "url_selection_model": agent_config.get("research_agent", {}).get("url_selection_model", {}).get("name", "")
            }
        }
    except Exception as e:
        logger.error(f"获取可用模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取可用模型列表失败: {str(e)}")

# 设置用户模型偏好
@app.post("/api/models/preferences")
async def set_models_preferences(
    preferences: UserModelPreferences,
    current_user: UserDB = Depends(get_current_active_user)
):
    """设置用户模型偏好"""
    try:
        # 验证用户输入的模型偏好
        models_config = {}
        # services = get_services()
        # logger.info(f"设置用户模型偏好(发送过来): {preferences}")
        # 验证并设置行动选择模型
        if preferences.action_selection_model and "name" in preferences.action_selection_model:
            model_name = preferences.action_selection_model["name"]
            # if model_name in services["llms"]:
                # 添加温度和最大token等配置，如果没提供则使用默认值
            action_model_config = {
                "name": model_name,
                "temperature": preferences.action_selection_model.get("temperature", 0.6),
                "max_tokens": preferences.action_selection_model.get("max_tokens", 8000)
            }
            models_config["action_selection_model"] = action_model_config
            # else:
            #     raise HTTPException(status_code=400, detail=f"无效的行动选择模型: {model_name}")
        
        # 验证并设置报告编辑模型
        if preferences.report_editing_model and "name" in preferences.report_editing_model:
            model_name = preferences.report_editing_model["name"]
            # if model_name in services["llms"]:
                # 添加温度和最大token等配置，如果没提供则使用默认值
            report_model_config = {
                "name": model_name,
                "temperature": preferences.report_editing_model.get("temperature", 0.6),
                "max_tokens": preferences.report_editing_model.get("max_tokens", 20000)
            }
            models_config["report_editing_model"] = report_model_config
            # else:
            #     raise HTTPException(status_code=400, detail=f"无效的报告编辑模型: {model_name}")
        
        # 验证并设置URL选择模型
        if preferences.url_selection_model and "name" in preferences.url_selection_model:
            model_name = preferences.url_selection_model["name"]
            # if model_name in services["llms"]:
            # 添加温度和最大token等配置，如果没提供则使用默认值
            url_model_config = {
                "name": model_name,
                "temperature": preferences.url_selection_model.get("temperature", 0.6),
                "max_tokens": preferences.url_selection_model.get("max_tokens", 500)
            }
            models_config["url_selection_model"] = url_model_config
            # else:
            #     raise HTTPException(status_code=400, detail=f"无效的URL选择模型: {model_name}")
        
        # 保存用户模型偏好
        # logger.info(f"保存用户模型偏好: {models_config}")
        success = await session_store.set_user_models_preference(str(current_user.id), models_config)
        
        if not success:
            raise HTTPException(status_code=500, detail="保存用户模型偏好失败")
        
        return {"status": "success", "message": "用户模型偏好已更新", "models_config": models_config}
    
    except HTTPException:
        # 直接抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"设置用户模型偏好失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"设置用户模型偏好失败: {str(e)}")

# 启动研究任务
@app.post("/research/start", response_model=ResearchResponse)
async def start_research(
    request: ResearchRequest,
    background_tasks: BackgroundTasks,
    services=Depends(get_services),
    current_user: UserDB = Depends(check_user_token_budget)
):
    """启动新的研究任务"""
    # 创建会话ID
    session_id = str(uuid.uuid4())
    logger.info(f"用户{current_user.id} 启动研究任务: {session_id}")
    # 创建消息队列
    message_queue = asyncio.Queue()
    message_queues[session_id] = message_queue
    
    # 初始化用户反馈和澄清问题列表
    user_feedbacks[session_id] = []
    clarification_questions[session_id] = []
    
    # 创建研究智能体
    research_agent = get_research_agent(services, session_id)
    
    # 设置认知搜索标志
    research_agent.set_enable_cognition_search(request.enable_cognition_search)
    
    # 获取用户的模型偏好设置
    user_models_config = await session_store.get_user_models_preference(str(current_user.id))
    
    # 如果有用户模型偏好，应用到研究智能体
    if user_models_config:
        research_agent.set_models(user_models_config)
        logger.info(f"应用用户模型偏好: {user_models_config}")
    
    # 保存会话信息到内存(用于临时访问)
    active_sessions[session_id] = {
        "status": "starting",
        "agent": research_agent,
        "last_activity": get_cn_time(),
        "question": request.question,
        "started_at": get_cn_time(),
        "start_time": get_cn_time(),
        "messages": [],
        "report": "",
        "error": "",
        "clarification_questions": [],
        "benchmark_id": request.benchmark_id,
        "user_id": str(current_user.id),
        "models_config": user_models_config,
        "enable_cognition_search": request.enable_cognition_search  # 保存标志
    }
    
    # 创建持久化会话存储
    await session_store.create_session(
        session_id=session_id,
        question=request.question,
        benchmark_id=request.benchmark_id,
        user_id=str(current_user.id),  # 使用当前用户ID
        metadata={"models_config": user_models_config, "enable_cognition_search": request.enable_cognition_search} if user_models_config else None
    )
    
    # 在后台启动研究任务
    background_tasks.add_task(run_research_task, session_id, request, str(current_user.id))
    
    return ResearchResponse(
        session_id=session_id,
        message="研究任务已启动，请通过WebSocket连接获取结果"
    )

# 查询研究状态
@app.get("/research/{session_id}/status")
async def get_research_status(
    session_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取研究状态"""
    # 先检查是否在内存中
    in_memory = session_id in active_sessions
    if in_memory:
        session = active_sessions[session_id]
    else:
        # 从持久化存储中获取状态
        session = await session_store.get_session(session_id)
        if session:
            # 获取会话关联的conversation_id
            conversation_id = session_id
            if not conversation_id:
                raise HTTPException(status_code=400, detail="conversation_id is required")
            # 将会话加载到内存中
            active_sessions[session_id] = {
                "status": session.status,
                "agent": get_research_agent(get_services(), conversation_id),
                "last_activity": get_cn_time(),
                "question": session.question,
                "started_at": session.started_at,
                "user_id": session.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session.messages] if session.messages else [],
                "report": session.report,
                "error": session.error,
                "clarification_questions": session.clarification_questions if session.clarification_questions else [],
                "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False,  # 从元数据中获取标志
                "token_usage": session.token_usage if session.token_usage else {},
                "enable_search": session.metadata.get("enable_search", True) if session.metadata else True  # 从元数据中获取搜索标志
            }
            session = active_sessions[session_id]

    if not session:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    # 验证会话所有权
    if session["user_id"] and session["user_id"] != str(current_user.id):
        raise HTTPException(status_code=403, detail="无权访问此研究会话")
    
    # 如果会话刚从数据库恢复，需要确保异步加载完成
    if not in_memory:
        try:
            # 检查是否需要加载会话数据
            if (hasattr(session["agent"], "_needs_loading") and 
                session["agent"]._needs_loading and 
                hasattr(session["agent"], "memory") and 
                hasattr(session["agent"].memory, "conversation_id")):
                # 显式等待会话加载完成
                loaded = await session["agent"]._load_session(session["agent"].memory.conversation_id)
                if loaded:
                    logger.info(f"状态获取时成功完成会话加载: {session_id}")
                else:
                    logger.warning(f"状态获取时会话加载失败: {session_id}")
        except Exception as e:
            logger.error(f"状态获取时会话加载出错: {str(e)}")
    
    # 获取会话状态
    status_data = {
        "session_id": session_id,
        "status": session["status"],
        "question": session["question"],
        "last_activity": session["last_activity"].isoformat(),
        "in_memory": in_memory,  # 是否在内存中(正在进行),
        "user_preferences": session["agent"].get_user_preferences()
    }
    
    # 如果有报告，添加报告
    if "report" in session and session["report"]:
        status_data["report"] = session["report"]
    
    # 如果有错误，添加错误信息
    if "error" in session and session["error"]:
        status_data["error"] = session["error"]
        
    # 如果有澄清问题，添加澄清问题
    if "clarification_questions" in session and session["clarification_questions"]:
        status_data["clarification_questions"] = session["clarification_questions"]
    
    # 如果有认知搜索状态，添加认知搜索状态
    if "enable_cognition_search" in session and session["enable_cognition_search"]:
        status_data["enable_cognition_search"] = session["enable_cognition_search"]
    else:
        status_data["enable_cognition_search"] = False
    
    # 如果有搜索状态，添加搜索状态
    if "enable_search" in session:
        status_data["enable_search"] = session["enable_search"]
    else:
        status_data["enable_search"] = True  # 默认启用搜索
    
    return status_data

# 提交用户反馈
@app.post("/research/{session_id}/feedback")
async def submit_feedback(
    session_id: str, 
    request: FeedbackRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """提交用户反馈"""
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    # 这里有一个小的问题，就是没有处理数据库关系
    # 默认了当用户反馈的时候， 已经进行过 get_status 等前置操作，读进内存
    
    logger.info(f"收到用户反馈: {session_id}, {request.feedback}")
    
    # 将反馈添加到内存中的反馈列表
    # 虽然我们现在使用持久化存储，但仍保留此逻辑以便快速访问
    if session_id not in user_feedbacks:
        user_feedbacks[session_id] = []
    # 这个是前端主动的，所以不需要发回前端，但要在message里面保存记录，replay的时候需要
    research_agent = active_sessions[session_id]["agent"]
    research_agent.add_user_feedback(request.feedback)
    
    # 创建feedback消息
    feedback_message = {
        "type": "update",
        "content": {
            "agent": "Feedback",
            "content": request.feedback,
            "round": research_agent.memory.round
        }
    }
    
    # 添加到内存中的messages
    active_sessions[session_id]["messages"].append(feedback_message)
    user_feedbacks[session_id].append(request.feedback)
    
    # 将反馈添加到持久化存储的user_feedbacks字段
    await session_store.add_user_feedback(session_id, request.feedback)
    
    # 重要：将feedback消息也保存到数据库的messages字段中
    await session_store.add_message(session_id, "update", {
        "agent": "Feedback", 
        "content": request.feedback,
        "round": research_agent.memory.round
    })
    
    return {"status": "success", "message": "反馈已提交"}

# 提交澄清问题回答
@app.post("/research/{session_id}/clarification_answer")
async def submit_clarification_answer(
    session_id: str, 
    request: ClarificationAnswerRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """提交对澄清问题的回答"""
    # TODO 需要检查 mongodb 当中的session
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    session = active_sessions[session_id]
    research_agent = session["agent"]
    
    logger.info(f"收到澄清问题回答: {session_id}, {len(request.answers)}个回答")
    
    try:
        # 获取澄清问题
        questions = await session_store.get_clarification_questions(session_id)
        if not questions:
            # 如果持久化存储中没有问题，尝试从内存中获取
            if session_id in clarification_questions:
                questions = clarification_questions[session_id]
            else:
                questions = research_agent.get_pending_questions()
        
        # 处理回答
        answers_list = []
        feedback_records = []
        
        for answer_item in request.answers:
            # 确保answer_item是字典且包含必要字段
            if isinstance(answer_item, dict) and "id" in answer_item and "answer" in answer_item:
                q_id = answer_item["id"]
                answer_text = answer_item["answer"]
                
                # 检查问题ID是否有效
                if 0 <= q_id < len(questions):
                    question_content = questions[q_id].get('question_content', questions[q_id].get('question', '未知问题'))
                    feedback = f"问题{q_id+1}: {question_content}\n回答: {answer_text}"
                    feedback_records.append(feedback)
                    # 直接添加到回答列表
                    answers_list.append({"id": q_id, "answer": answer_text})
        
        # 清除内存中的澄清问题
        if session_id in clarification_questions:
            del clarification_questions[session_id]
        
        # 调用研究智能体的回答问题方法
        result = await research_agent.answer_pending_questions(answers_list)
        logger.info(f"处理澄清问题回答成功: {session_id}, 剩余问题: {result.get('remaining_questions', 0)}")
        
        # 重要：将澄清问题回答也保存到数据库的messages字段中
        if feedback_records:
            # 创建澄清问题回答消息
            clarification_answer_message = {
                "type": "update",
                "content": {
                    "agent": "ClarificationAnswer",
                    "content": "\n\n".join(feedback_records),
                    "round": research_agent.memory.round
                }
            }
            
            # 添加到内存中的messages
            if "messages" not in session:
                session["messages"] = []
            session["messages"].append(clarification_answer_message)
            
            # 保存到数据库的messages字段中
            await session_store.add_message(session_id, "update", {
                "agent": "ClarificationAnswer", 
                "content": "\n\n".join(feedback_records),
                "round": research_agent.memory.round
            })
            logger.info(f"澄清问题回答已保存到数据库: {session_id}")
        
        return {"status": "success", "message": "澄清问题回答已提交", "remaining_questions": result.get("remaining_questions", 0)}
    
    except Exception as e:
        logger.error(f"处理澄清问题回答失败: {str(e)}")
        session["status"] = "error"
        await session_store.update_status(session_id, "error", str(e))
        
        return {"status": "error", "message": f"处理澄清问题回答失败: {str(e)}"}

# 获取待处理的澄清问题
@app.get("/research/{session_id}/pending_questions")
async def get_pending_questions(
    session_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取会话中待处理的澄清问题"""
    # TODO 需要检查 mongodb 当中的session
    if session_id not in active_sessions:
        session_from_db = await session_store.get_session(session_id)
        if not session_from_db:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        else:
            active_sessions[session_id] = {
                "status": session_from_db.status,
                "agent": get_research_agent(get_services(), session_id),
                "last_activity": get_cn_time(),
                "question": session_from_db.question,
                "started_at": session_from_db.started_at,
                "user_id": session_from_db.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
                "report": session_from_db.report,
                "error": session_from_db.error,
                "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else []
            }
    
    session = active_sessions[session_id]
    research_agent = session["agent"]
    
    try:
        # 获取待处理的澄清问题
        pending_questions = research_agent.get_pending_questions()
        
        return {
            "status": "success", 
            "pending_questions": pending_questions,
            "count": len(pending_questions)
        }
    except Exception as e:
        logger.error(f"获取待处理澄清问题出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取待处理澄清问题出错: {str(e)}")

# 回答待处理的澄清问题
@app.post("/research/{session_id}/answer_questions")
async def answer_questions(
    session_id: str, 
    answers: List[Dict[str, Any]]
):
    """
    回答待处理的澄清问题
    
    answers: 包含问题ID和回答的列表，例如:
    [
        {"id": 0, "answer": "选项1"},
        {"id": 1, "answer": "自定义回答"}
    ]
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    session = active_sessions[session_id]
    research_agent = session["agent"]
    
    if not answers:
        raise HTTPException(status_code=400, detail="未提供问题回答")
    
    try:
        # 处理澄清问题的回答
        result = await research_agent.answer_pending_questions(answers)
        
        if not result.get("success", False):
            raise HTTPException(
                status_code=400, 
                detail=result.get("error", "处理问题回答失败")
            )
        
        return {
            "status": "success", 
            "message": result.get("message", "成功回答问题"),
            "remaining_questions": result.get("remaining_questions", 0)
        }
    except Exception as e:
        logger.error(f"回答澄清问题出错: {e}")
        raise HTTPException(status_code=500, detail=f"回答澄清问题出错: {str(e)}")

# 暂停研究任务
@app.post("/research/{session_id}/pause")
async def pause_research(
    session_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """暂停研究任务"""
    if session_id in active_sessions:
        session = active_sessions[session_id]
    else:
        # 尝试从持久化存储中获取会话
        session = await session_store.get_session(session_id)
        if session:
            # 获取会话关联的conversation_id
            conversation_id = session_id
            if not conversation_id:
                raise HTTPException(status_code=400, detail="conversation_id is required")
            
            # 将会话加载到内存中
            active_sessions[session_id] = {
                "status": session.status,
                "agent": get_research_agent(get_services(), conversation_id),
                "last_activity": get_cn_time(),
                "question": session.question,
                "started_at": session.started_at,
                "user_id": session.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session.messages] if session.messages else [],
                "report": session.report,
                "error": session.error,
                "clarification_questions": session.clarification_questions if session.clarification_questions else []
            }
            session = active_sessions[session_id]
        else:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    if not session:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    # 检查会话状态
    if session["status"] != "running":
        raise HTTPException(status_code=400, detail=f"研究任务当前状态为 {session['status']}，无法暂停")
    
    # 暂停研究智能体
    research_agent = session["agent"]
    await research_agent.pause()
    
    # 保存研究轨迹
    save_research_trajectory(research_agent, session_id, "暂停")
    
    # 更新会话状态
    session["status"] = "paused"
    await session_store.update_status(session_id, "paused")
    session["last_activity"] = get_cn_time()
    
    return {"status": "success", "message": "研究任务已暂停"}

# 恢复研究任务
@app.post("/research/{session_id}/resume")
async def resume_research(
    session_id: str,
    current_user: UserDB = Depends(check_user_token_budget)
):
    """恢复已暂停的研究任务"""
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    session = active_sessions[session_id]
    logger.info(f"恢复研究任务: {session_id}, 当前状态: {session['status']}")
    if session["status"] != "paused":
        raise HTTPException(status_code=400, detail="研究任务未处于暂停状态")
    
    research_agent = session["agent"]
    
    try:
        # 检查是否有未回答的澄清问题
        pending_questions = research_agent.get_pending_questions()
        if pending_questions:
            raise HTTPException(
                status_code=400, 
                detail=f"还有{len(pending_questions)}个未回答的澄清问题，请先回答这些问题再恢复研究"
            )
        
        # 恢复前保存当前研究轨迹
        save_research_trajectory(research_agent, session_id, "恢复前")
        
        # 更新会话状态
        session["status"] = "running"
        session["last_activity"] = get_cn_time()
        await session_store.update_status(session_id, "running")
        logger.info(f"会话状态已更新为running: {session_id}")
        
        # 向WebSocket发送状态更新消息
        if session_id in message_queues:
            status_message = {
                "type": "status",
                "status": "running",
                "message": "研究任务已恢复"
            }
            await message_queues[session_id].put(status_message)
            logger.info(f"向WebSocket队列发送状态更新消息: {session_id}")
        
        # 恢复研究智能体，传入resume标记
        input_data = {
            "resume": True, 
            "conversation_id": session_id,
            "user_id": str(current_user.id)  # 添加用户ID
        }
        logger.info(f"创建新的研究代理运行任务: {session_id}")
        asyncio.create_task(
            run_agent_and_send_to_websocket(research_agent, input_data, session_id)
        )
        
        return {"status": "success", "message": "研究任务已恢复"}
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"恢复研究任务出错: {e}")
        raise HTTPException(status_code=500, detail=f"恢复研究任务出错: {str(e)}")

# 辅助函数：保存研究轨迹
def save_research_trajectory(research_agent, session_id, status="完成"):
    """保存研究智能体的研究轨迹为JSON文件"""
    try:
        if not hasattr(research_agent, "memory") or not hasattr(research_agent.memory, "research_trajectory"):
            logger.warning(f"研究智能体没有research_trajectory属性，无法保存轨迹: {session_id}")
            return False
            
        # 确保目标目录存在
        trajectory_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "dc_agents", "trajectory")
        os.makedirs(trajectory_dir, exist_ok=True)
        
        # 保存研究轨迹为JSON文件
        trajectory_file = os.path.join(trajectory_dir, f"{session_id}.json")
        with open(trajectory_file, "w", encoding="utf-8") as f:
            json.dump(research_agent.memory.research_trajectory, f, ensure_ascii=False, indent=2)
        logger.info(f"{status}的研究轨迹已保存到: {trajectory_file}")
        return True
    except Exception as e:
        logger.error(f"保存研究轨迹时出错: {e}")
        return False

# region 发送结果到WS
async def run_agent_and_send_to_websocket(research_agent, input_data: Dict[str, Any], session_id: str):
    """运行研究代理并发送结果到WebSocket"""
    try:
        # 获取会话信息
        session = active_sessions[session_id]
        queue = message_queues[session_id]
        
        logger.info(f"开始运行研究代理: {session_id}, 输入数据: {input_data}")
        
        # 初始化token使用统计
        # ! 如果 token usage 不在内存中，则应该从数据库获取，但考虑到，resume的时候，前端会先请求 get status，隐式的载入了session，所以应该目前还好
        if "token_usage" not in session:
            session["token_usage"] = {
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "total_cost": 0,
                "models_usage": {}
            }

        
        # 初始化一个缓冲区用于批量存储消息
        message_buffer = []
        last_db_update = get_cn_time()
        db_update_interval = 5  # 每5秒批量更新一次数据库
        start_msg = {
            "type": "status",
            "content": {
                "status": "running",
                "message": "研究任务已开始"
            }
        }
        message_buffer.append(start_msg)
        await queue.put(start_msg)

        session["status"] = "running"
        await session_store.update_status(session_id, "running")

        # 运行研究流程并处理输出
        async for chunk in research_agent.run(input_data):
            # 处理token使用信息
            if "stage" in chunk and chunk["stage"] == "token_usage" and chunk.get("action") == "token_info":
                # 提取token使用信息
                token_usage = chunk.get("token_usage", {})
                logger.info(f"收到token使用信息: {token_usage}")
                if token_usage:
                    # 更新会话中的token使用统计
                    session["token_usage"] = token_usage
                    
                    # 将token使用信息保存到数据库
                    await session_store.update_token_usage(session_id, token_usage)
                    logger.info(f"更新token使用统计: {session_id}, 总成本: {token_usage.get('total_cost', 0):.4f}")
                    
                    # 实时同步token使用量到用户表
                    try:
                        from backend.auth.dependencies import record_token_usage_for_session
                        
                        # 获取当前session的用户ID
                        user_id = session.get("user_id")
                        if user_id:
                            # 获取本次token使用成本
                            current_cost = token_usage.get("total_cost", 0.0)
                            
                            # 检查是否有之前的token使用记录
                            previous_cost = 0.0
                            if hasattr(session, "previous_token_cost"):
                                previous_cost = session.get("previous_token_cost", 0.0)
                            
                            # 计算增量成本
                            cost_increment = current_cost - previous_cost
                            
                            if cost_increment > 0:
                                # 通过依赖函数记录token使用量到用户表
                                success = await record_token_usage_for_session(
                                    user_id=str(user_id),
                                    session_id=session_id,
                                    token_cost=cost_increment
                                )
                                if success:
                                    logger.info(f"用户 {user_id} token使用量已同步: +{cost_increment:.4f}")
                                else:
                                    logger.warning(f"用户 {user_id} token使用量同步失败")
                            
                            # 更新session中的previous_token_cost
                            session["previous_token_cost"] = current_cost
                            
                    except Exception as e:
                        logger.error(f"同步token使用量到用户表失败: {str(e)}")
                        # 不阻止主流程，只记录错误
                    
                    # 发送token使用信息到客户端
                    token_message = {
                        "type": "token_usage",
                        "token_usage": token_usage
                    }
                    await queue.put(token_message)
                    
                continue

            # 处理澄清问题
            if "stage" in chunk and chunk["stage"] == "clarify" and chunk["action"] == "clarify":
                # 更新会话状态
                session["status"] = "clarification"
                await session_store.update_status(session_id, "clarification")
                logger.info(f"研究代理需要澄清: {session_id}")
                
                # 存储澄清问题
                clarification_content = chunk.get("content", [])
                if clarification_content:
                    # 保存到内存中用于快速访问
                    if session_id not in clarification_questions:
                        clarification_questions[session_id] = []
                    clarification_questions[session_id] = clarification_content
                    
                    # 保存到持久化存储
                    await session_store.set_clarification_questions(session_id, clarification_content)
                    logger.info(f"存储澄清问题: {session_id}, {len(clarification_content)}个问题")
                    
                    # 通过WebSocket发送澄清问题
                    message = {
                        "type": "clarification",
                        "questions": clarification_content,
                        "message": "研究智能体需要您回答一些澄清问题"
                    }
                    logger.info(f"向队列添加澄清消息: {session_id}")
                    await queue.put(message)
                    
                    # ! clarification 消息不加载到数据库
                    # # 添加到消息缓冲区（直接使用字典而非Pydantic模型）
                    # message_buffer.append({
                    #     "type": "clarification",
                    #     "timestamp": get_cn_time(),
                    #     "content": {
                    #         "questions": clarification_content,
                    #         "message": "研究智能体需要您回答一些澄清问题"
                    #     }
                    # })
                    
                    # 模型已自动暂停，状态已在代理内部更新
                    session["status"] = "paused"
                    await session_store.update_status(session_id, "paused")
                    logger.info(f"研究状态更新为暂停: {session_id}")
                    
                    # 立即将缓冲区消息保存到数据库
                    if message_buffer:
                        # 确保所有消息都是字典（在紧急情况下直接确保）
                        safe_messages = []
                        for msg in message_buffer:
                            if isinstance(msg, dict):
                                safe_messages.append(msg)
                            else:
                                # 如果是Pydantic对象，尝试转换为字典
                                try:
                                    safe_messages.append(msg.dict())
                                except:
                                    # 防御性编程，记录错误但不抛出异常
                                    logger.warning(f"无法序列化消息，跳过: {type(msg)}")
                                    
                        await session_store.add_messages_batch(session_id, safe_messages)
                        message_buffer = []
                        last_db_update = get_cn_time()
                    
                continue

            # 检查是否是系统信息
            if "stage" in chunk and chunk["stage"] == "system":
                # logger.info(f"系统信息: {chunk}")
                system_message = {
                    "type": "system",
                    "content": chunk
                }

                await queue.put(system_message)

                message_buffer.append(system_message)

                # 同时更新内存中的session["messages"]
                if "messages" not in session:
                    session["messages"] = []
                session["messages"].append(system_message)
                
                # 立即将缓冲区消息保存到数据库
                if message_buffer:
                    # 确保所有消息都是字典
                    safe_messages = []
                    for msg in message_buffer:
                        if isinstance(msg, dict):
                            safe_messages.append(msg)
                        else:
                            try:
                                safe_messages.append(msg.dict())
                            except:
                                logger.warning(f"无法序列化消息，跳过: {type(msg)}")
                                
                    await session_store.add_messages_batch(session_id, safe_messages)
                    message_buffer = []
                    last_db_update = get_cn_time()
                
                continue

            # 检查是否进入暂停状态
            if "stage" in chunk and chunk["stage"] == "paused" and chunk["action"] == "paused":
                # 更新会话状态为暂停
                session["status"] = "paused"
                await session_store.update_status(session_id, "paused")
                logger.info(f"研究代理暂停: {session_id}")
                
                # 通过WebSocket发送暂停状态
                message = {
                    "type": "status",
                    "status": "paused",
                    "message": chunk.get("content", "研究已暂停")
                }
                logger.info(f"向队列添加暂停状态消息: {session_id}")
                await queue.put(message)
                
                # 添加到消息缓冲区（直接使用字典而非Pydantic模型）
                status_message = {
                    "type": "status",
                    "timestamp": get_cn_time(),
                    "content": {
                        "status": "paused",
                        "message": chunk.get("content", "研究已暂停")
                    }
                }
                message_buffer.append(status_message)
                
                # 同时更新内存中的session["messages"]
                if "messages" not in session:
                    session["messages"] = []
                session["messages"].append(status_message)
                
                # 立即将缓冲区消息保存到数据库
                if message_buffer:
                    # 确保所有消息都是字典
                    safe_messages = []
                    for msg in message_buffer:
                        if isinstance(msg, dict):
                            safe_messages.append(msg)
                        else:
                            try:
                                safe_messages.append(msg.dict())
                            except:
                                logger.warning(f"无法序列化消息，跳过: {type(msg)}")
                                
                    await session_store.add_messages_batch(session_id, safe_messages)
                    message_buffer = []
                    last_db_update = get_cn_time()
                
                continue
            # logger.info(f"chunk: {chunk}")
            # 向客户端发送进度更新
            message = {
                "type": "update",
                "content": chunk
            }
            
            logger.debug(f"向队列添加更新消息: {session_id}, stage={chunk.get('stage', 'unknown')}")
            await queue.put(message)
            
            # 添加到消息缓冲区（直接使用字典而非Pydantic模型）
            update_message = {
                "type": "update",
                "timestamp": get_cn_time(),
                "content": chunk
            }
            message_buffer.append(update_message)
            
            # 同时更新内存中的session["messages"]
            if "messages" not in session:
                session["messages"] = []
            session["messages"].append(update_message)
            
            # 定期批量更新数据库
            now = get_cn_time()
            if (now - last_db_update).total_seconds() > db_update_interval and message_buffer:
                # 确保所有消息都是字典
                safe_messages = []
                for msg in message_buffer:
                    if isinstance(msg, dict):
                        safe_messages.append(msg)
                    else:
                        try:
                            safe_messages.append(msg.dict())
                        except:
                            logger.warning(f"无法序列化消息，跳过: {type(msg)}")
                            
                await session_store.add_messages_batch(session_id, safe_messages)
                message_buffer = []
                last_db_update = now
                logger.debug(f"批量更新会话消息到数据库: {session_id}")
            
            # 更新最后活动时间
            session["last_activity"] = get_cn_time()
        
        # 研究完成，保存剩余的消息缓冲区
        if message_buffer:
            # 确保所有消息都是字典
            safe_messages = []
            for msg in message_buffer:
                if isinstance(msg, dict):
                    safe_messages.append(msg)
                else:
                    try:
                        safe_messages.append(msg.dict())
                    except:
                        logger.warning(f"无法序列化消息，跳过: {type(msg)}")
                        
            await session_store.add_messages_batch(session_id, safe_messages)
            logger.debug(f"保存剩余的消息缓冲区: {session_id}, {len(safe_messages)}条消息")
        message_buffer = []
        
        # 获取最终报告
        final_report = research_agent.memory.get_report_draft()
        logger.info(f"研究完成: {session_id}, 报告长度: {len(final_report)}")

        # 保存研究轨迹为JSON文件
        save_research_trajectory(research_agent, session_id, "完成")

        # 更新会话状态 - 所有完成的任务都设为paused，而不是completed
        session["status"] = "paused"
        session["report"] = final_report
        session["last_activity"] = get_cn_time()
        
        # 更新持久化存储
        await session_store.set_report(session_id, final_report)
        await session_store.update_status(session_id, "paused")  # 确保数据库状态也更新为paused
        
        # 发送完成消息
        complete_message = {
            "type": "paused",
            "report": final_report
        }
        logger.info(f"向队列添加完成消息: {session_id}")
        await queue.put(complete_message)
        
        # 单独保存完成消息（这是重要的状态变更，直接使用字典而非Pydantic模型）
        await session_store.add_message(session_id, "paused", {"report": final_report})
        
    except Exception as e:
        logger.error(f"研究任务出错: {str(e)}", exc_info=True)
        
        # 保存研究轨迹为JSON文件(即使出错也保存)
        save_research_trajectory(research_agent, session_id, "错误")
        
        # 更新会话状态
        session["status"] = "error"
        session["error"] = str(e)
        session["last_activity"] = get_cn_time()
        
        # 更新持久化存储
        await session_store.update_status(session_id, "error", str(e))
        
        # 发送错误消息
        error_message = {
            "type": "error",
            "message": f"研究过程中发生错误: {str(e)}"
        }
        logger.info(f"向队列添加错误消息: {session_id}")
        await queue.put(error_message)
        
        # 单独保存错误消息（直接使用字典而非Pydantic模型）
        await session_store.add_message(session_id, "error", {"message": f"研究过程中发生错误: {str(e)}"})
# endregion


# region WebSocket路由
@app.websocket("/ws/research/{session_id}")
async def websocket_research(websocket: WebSocket, session_id: str):
    """通过WebSocket接收研究进展"""
    logger.info(f"新的WebSocket连接请求: {session_id}")
    await websocket.accept()
    logger.info(f"WebSocket连接已接受: {session_id}")
    
    # 添加心跳失败计数器
    heartbeat_failures = 0
    max_heartbeat_failures = WS_MAX_HEARTBEAT_FAILURES  # 使用配置的最大允许连续失败次数
    
    # 检查会话是否存在
    if session_id in active_sessions:
        session = active_sessions[session_id]
    else:
        session = await session_store.get_session(session_id)
        # 将数据库模型转换为可订阅的字典格式
        if not session_id:
            raise HTTPException(status_code=400, detail="session_id is required")
        session_dict = {
            "status": session.status,
            "agent": get_research_agent(get_services(), session_id),
            "last_activity": get_cn_time(),
            "question": session.question,
            "started_at": session.started_at,
            "user_id": session.user_id,
            "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session.messages] if session.messages else [],
            "report": session.report,
            "error": session.error,
            "clarification_questions": session.clarification_questions if session.clarification_questions else [],
            "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False  # 从元数据中获取标志
        }
        active_sessions[session_id] = session_dict
        session = active_sessions[session_id]
    if not session:
        logger.warning(f"WebSocket连接失败: 找不到会话 {session_id}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "找不到指定的研究会话"
        }))
        await websocket.close()
        return
    
    # 存储WebSocket连接
    websocket_connections[session_id] = websocket
    logger.info(f"WebSocket连接已存储: {session_id}")
    
    try:
        # 获取消息队列
        if session_id not in message_queues:
            logger.info(f"为会话 {session_id} 创建新的消息队列")
            message_queues[session_id] = asyncio.Queue()
        
        queue = message_queues[session_id]
        
        # 发送初始状态消息
        initial_status = {
            "type": "status",
            "status": session["status"],
            "message": f"研究状态: {session.get('status')}"
        }
        logger.info(f"发送初始状态消息: {initial_status}")
        await websocket.send_text(json.dumps(initial_status))
        
        # 如果是断线重连，发送过去的消息
        if session["status"] not in ["starting", "cancelled"]:
            message_count = 0
            # 根据会话状态决定是否发送消息历史
            logger.info(f"会话状态: {session['status']} 决定是否发送消息历史")   
            if session["status"] in ["running", "paused", "clarification"]:
                # 收集所有历史更新消息
                all_messages = []
                # for key, value in session.items():
                #     logger.info(f"收集历史消息: {key} = ")
                for message in session["messages"]:
                    # 只收集update类型消息，其他类型如status等即时生成
                    if message["type"] == "update" or message["type"] == "system":
                        message["content"]["replay"] = True
                        all_messages.append({
                            "type": message["type"],
                            "content": message["content"]
                        })
                        message_count += 1
                
                # 如果有消息，直接一次性发送
                if all_messages:
                    batch_message = {
                        "type": "batch_updates",
                        "messages": all_messages
                    }
                    await websocket.send_text(json.dumps(batch_message))
                    logger.info(f"一次性发送 {message_count} 条历史消息")
            
            # 如果发送了消息，告知客户端历史消息已发送完毕
            if message_count > 0:
                await websocket.send_text(json.dumps({
                    "type": "replay_complete",
                    "message": f"已发送 {message_count} 条历史消息"
                }))
        
        # 如果当前状态是澄清问题，直接发送澄清问题
        if session["status"] == "clarification":
            clarification_content = session["clarification_questions"]
            if clarification_content:
                clarification_message = {
                    "type": "clarification",
                    "questions": clarification_content,
                    "message": "研究智能体需要您回答一些澄清问题"
                }
                logger.info(f"发送存储的澄清问题: {len(clarification_content)}个问题")
                await websocket.send_text(json.dumps(clarification_message))
        
        # 如果会话已paused且有报告，发送报告
        if session["status"] == "paused" and session["report"]:
            paused_message = {
                "type": "paused",
                "report": session["report"]
            }
            logger.info(f"发送paused状态消息和报告")
            await websocket.send_text(json.dumps(paused_message))
        
        # 监听并转发消息
        while True:
            
            # 从队列中获取消息并发送
            try:
                logger.debug(f"等待队列消息: {session_id}")
                message = await asyncio.wait_for(queue.get(), timeout=WS_HEARTBEAT_INTERVAL)
                # logger.info(f"从队列获取消息: {message.get('type', 'unknown')} 准备发送")
                
                # 确保消息是完整的JSON对象
                # 对于clarification类型的消息，确保包含questions字段
                if message.get("type") == "clarification" and "questions" not in message:
                    logger.warning(f"修复不完整的澄清消息: {message}")
                    questions = session["clarification_questions"]
                    if questions:
                        message["questions"] = questions
                
                message_json = json.dumps(message)
                try:
                    await websocket.send_text(message_json)
                    # logger.info(f"已发送WebSocket消息: 类型={message.get('type', 'unknown')}, 长度={len(message_json)}")
                except Exception as e:
                    logger.error(f"发送WebSocket消息失败: {str(e)}")
                    # 尝试重新连接或中断循环
            except asyncio.TimeoutError:
                # 超时，继续下一轮循环
                # 发送心跳消息保持连接活跃，并等待响应来验证连接状态
                try:
                    # 缩短超时检测时间，从1秒减少到0.5秒
                    heartbeat_id = str(uuid.uuid4())[:8]  # 生成唯一ID用于跟踪心跳
                    heartbeat_msg = json.dumps({"type": "heartbeat", "id": heartbeat_id})
                    
                    # 发送心跳消息
                    await websocket.send_text(heartbeat_msg)
                    logger.debug(f"发送心跳消息: {session_id}, id={heartbeat_id}")
                    
                    # 快速检查心跳响应 (使用配置的超时时间)
                    try:
                        # 尝试接收心跳响应或其他消息
                        response = await asyncio.wait_for(websocket.receive_text(), timeout=WS_HEARTBEAT_TIMEOUT)
                        
                        # 验证响应 - 解析响应内容来检查是否匹配发送的心跳ID
                        try:
                            response_data = json.loads(response)
                            # 检查是否是心跳响应
                            if response_data.get("type") == "heartbeat_response" and response_data.get("id") == heartbeat_id:
                                logger.debug(f"收到有效心跳响应: {session_id}, id={heartbeat_id}")
                                # 重置心跳失败计数器
                                heartbeat_failures = 0
                            elif response_data.get("type") == "pong":
                                logger.debug(f"收到Pong响应: {session_id}")
                                # 重置心跳失败计数器
                                heartbeat_failures = 0
                            else:
                                logger.debug(f"收到其他响应，非心跳回应: {session_id}, 类型={response_data.get('type', 'unknown')}")
                                # 不增加失败计数，因为连接仍然有效
                                heartbeat_failures = 0
                        except json.JSONDecodeError:
                            logger.warning(f"无法解析心跳响应JSON: {session_id}, 响应={response[:30]}...")
                            # 收到了数据但无法解析，增加失败计数
                            heartbeat_failures += 1
                        
                    except asyncio.TimeoutError:
                        # 未收到心跳响应，可能连接已断开
                        logger.warning(f"未收到心跳响应，可能连接已断开: {session_id}, 失败次数: {heartbeat_failures+1}/{max_heartbeat_failures}")
                        
                        # 增加失败计数器
                        heartbeat_failures += 1
                        
                        # 如果连续失败次数达到阈值，断开连接
                        if heartbeat_failures >= max_heartbeat_failures:
                            logger.error(f"连续{max_heartbeat_failures}次心跳失败，主动断开连接: {session_id}")
                            break
                        
                        # 尝试发送一次测试消息确认连接状态
                        try:
                            await websocket.send_text(json.dumps({"type": "ping"}))
                        except Exception:
                            logger.error(f"心跳测试失败，连接已断开: {session_id}")
                            break
                    except Exception as e:
                        logger.error(f"接收心跳响应出错: {str(e)}")
                        # 增加失败计数器
                        heartbeat_failures += 1
                        
                        # 如果连续失败次数达到阈值，断开连接
                        if heartbeat_failures >= max_heartbeat_failures:
                            logger.error(f"连续{max_heartbeat_failures}次心跳失败，主动断开连接: {session_id}")
                            break
                        
                except Exception as e:
                    logger.error(f"发送心跳消息失败: {str(e)}")
                    # 增加失败计数器
                    heartbeat_failures += 1
                    
                    # 如果连续失败次数达到阈值，断开连接
                    if heartbeat_failures >= max_heartbeat_failures:
                        logger.error(f"连续{max_heartbeat_failures}次心跳失败，主动断开连接: {session_id}")
                        break
                        
                continue
            except Exception as e:
                logger.error(f"处理队列消息出错: {str(e)}")
                break
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket处理出错: {str(e)}", exc_info=True)
    finally:
        # 清理连接
        if session_id in websocket_connections:
            logger.info(f"清理WebSocket连接: {session_id}")
            del websocket_connections[session_id]
# endregion

# region 后台运行研究任务
async def run_research_task(session_id: str, request: ResearchRequest, user_id: str):
    """在后台运行研究任务"""
    # 获取会话信息
    session = active_sessions[session_id]
    research_agent = session["agent"]
    
    # 再次检查用户模型偏好（以防在会话创建后配置发生变化）
    user_models_config = await session_store.get_user_models_preference(user_id)
    if user_models_config:
        research_agent.set_models(user_models_config)
        logger.info(f"在研究任务启动前再次应用用户模型偏好: {user_models_config}")
        
        # 更新会话中的模型配置记录
        session["models_config"] = user_models_config
        await session_store.set_session_models_config(session_id, user_models_config)
    
    # 准备输入数据，直接传递原始请求数据
    input_data = {
        "question": request.question,
        "user_id": user_id,  # 添加用户ID
    }
    
    # 如果有benchmark_id，添加到输入数据中
    if request.benchmark_id is not None:
        input_data["benchmark_id"] = request.benchmark_id
        
        # 如果是benchmark，尝试加载标准答案
        benchmark_data = load_benchmark_data()
        if 0 <= request.benchmark_id < len(benchmark_data):
            benchmark_item = benchmark_data[request.benchmark_id]
            research_agent.standard_answer = benchmark_item.get("answer", "未提供标准答案")
    
    # 更新会话状态
    session["status"] = "running"
    
    # 使用通用函数运行研究代理
    await run_agent_and_send_to_websocket(research_agent, input_data, session_id)
# endregion

# region 测试路由
@app.get("/test/echo")
async def test_echo(message: str):
    return {"echo": message}

# 调试模式控制
@app.post("/debug/toggle")
async def toggle_debug_mode():
    """切换调试模式"""
    # 注意：这个切换只是临时的，服务重启后会恢复为环境变量定义的值
    import dc_agents.src.deep_cognition.research_agent as research_agent_module
    
    # 反转调试模式状态
    research_agent_module.DEBUG_MODE = not research_agent_module.DEBUG_MODE
    
    logger.info(f"DAIR DEBUG MODE 已切换为: {'启用' if research_agent_module.DEBUG_MODE else '禁用'}")
    
    return {
        "status": "success", 
        "debug_mode": research_agent_module.DEBUG_MODE,
        "message": f"调试模式已{'启用' if research_agent_module.DEBUG_MODE else '禁用'}"
    }

# 获取当前调试模式状态
@app.get("/debug/status")
async def get_debug_status():
    """获取当前调试模式状态"""
    import dc_agents.src.deep_cognition.research_agent as research_agent_module
    
    return {
        "debug_mode": research_agent_module.DEBUG_MODE,
        "message": f"调试模式当前{'启用' if research_agent_module.DEBUG_MODE else '禁用'}"
    }

async def enqueue_websocket_message(session_id: str, message: Dict):
    """将消息放入队列供WebSocket发送"""
    logger.info(f"入队WebSocket消息: 会话={session_id}, 类型={message.get('type', 'unknown')}")
    
    if session_id not in active_sessions:
        logger.warning(f"尝试向不存在的会话发送消息: {session_id}")
        return
    
    # 确保消息队列存在
    if session_id not in message_queues:
        logger.info(f"为会话 {session_id} 创建新的消息队列")
        message_queues[session_id] = asyncio.Queue()
    
    # 处理澄清问题类型的消息
    if message.get("type") == "clarification" and "questions" in message:
        logger.info(f"存储澄清问题: {len(message['questions'])}个问题")
        clarification_questions[session_id] = message["questions"]
        # 更新会话状态为澄清
        active_sessions[session_id]["status"] = "clarification"
        active_sessions[session_id]["last_activity"] = get_cn_time().isoformat()
    
    # 将完成类型的消息也处理为paused状态
    if message.get("type") == "complete":
        logger.info(f"研究完成，设置为paused状态: {session_id}")
        # 修改消息类型为paused
        message["type"] = "paused"
        # 更新会话状态为paused
        active_sessions[session_id]["status"] = "paused"
        active_sessions[session_id]["last_activity"] = get_cn_time().isoformat()
        # 更新数据库状态
        asyncio.create_task(session_store.update_status(session_id, "paused"))
    
    # 处理错误类型的消息
    if message.get("type") == "error":
        logger.warning(f"研究错误: {session_id} - {message.get('message', '未知错误')}")
    
    # 消息深度拷贝确保消息不会在放入队列后被修改
    message_copy = copy.deepcopy(message)
    
    # 将消息放入队列
    try:
        await message_queues[session_id].put(message_copy)
        queue_size = message_queues[session_id].qsize()
        logger.info(f"消息已入队: 会话={session_id}, 当前队列大小={queue_size}")
    except Exception as e:
        logger.error(f"入队消息失败: {str(e)}", exc_info=True)
    
    # 如果没有活跃的WebSocket连接，记录警告
    if session_id not in websocket_connections:
        logger.warning(f"WebSocket连接不存在，消息可能丢失: 会话={session_id}, 类型={message.get('type', 'unknown')}")
        if message.get("type") == "clarification":
            logger.info("澄清问题已存储，将在用户重新连接时发送")
        return

# 获取分享的研究内容
@app.get("/api/research/share/{conversation_id}")
async def get_shared_research(conversation_id: str):
    """获取分享的研究内容"""
    # 先检查内存中的会话
    if conversation_id in active_sessions:
        session = active_sessions[conversation_id]
        return {
            "conversation_id": conversation_id,
            "question": session["question"],
            "status": session["status"],
            "started_at": session["started_at"].isoformat(),
            "last_activity": session["last_activity"].isoformat(),
            "message_count": len(session["messages"]) if "messages" in session else 0
        }
    
    # 如果内存中没有，再从持久化存储中获取
    session = await session_store.get_session(conversation_id)
    if not session:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    # 返回会话基本信息
    return {
        "conversation_id": conversation_id,
        "question": session.question,
        "status": session.status,
        "started_at": session.started_at.isoformat(),
        "last_activity": session.last_activity.isoformat(),
        "message_count": len(session.messages) if session.messages else 0
    }

# WebSocket分享连接，用于重放消息
@app.websocket("/ws/research/share/{conversation_id}")
async def websocket_share_research(websocket: WebSocket, conversation_id: str):
    """通过WebSocket重放研究进展"""
    logger.info(f"新的分享WebSocket连接请求: {conversation_id}")
    await websocket.accept()
    logger.info(f"分享WebSocket连接已接受: {conversation_id}")
    
    try:
        # 先检查内存中的会话
        if conversation_id in active_sessions:
            session = active_sessions[conversation_id]
            messages = session.get("messages", [])
            status = session["status"]
            report = session.get("report", "")
        else:
            # 如果内存中没有，从持久化存储中获取
            session = await session_store.get_session(conversation_id)
            if not session:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "找不到指定的研究会话"
                }))
                await websocket.close()
                return
            
            messages = [msg.dict() if hasattr(msg, 'dict') else msg for msg in session.messages] if session.messages else []
            status = session.status
            report = session.report
        
        # 发送初始状态消息
        initial_status = {
            "type": "status",
            "status": status,
            "message": f"研究状态: {status}"
        }
        await websocket.send_text(json.dumps(initial_status))
        
        # 重放历史消息
        if messages:
            # 收集所有历史更新消息
            all_messages = []
            for message in messages:
                if message["type"] == "update" or message["type"] == "system":
                    message["content"]["replay"] = True
                    all_messages.append({
                        "type": message["type"],
                        "content": message["content"]
                    })
            
            # 如果有消息，直接一次性发送
            if all_messages:
                batch_message = {
                    "type": "batch_updates",
                    "messages": all_messages
                }
                await websocket.send_text(json.dumps(batch_message))
                logger.info(f"一次性发送 {len(all_messages)} 条历史消息")
        
        # 如果会话有报告，发送报告
        if report:
            paused_message = {
                "type": "paused",
                "report": report
            }
            await websocket.send_text(json.dumps(paused_message))
        
        # 发送重放完成消息
        await websocket.send_text(json.dumps({
            "type": "replay_complete",
            "message": "消息重放完成"
        }))
        
    except WebSocketDisconnect:
        logger.info(f"分享WebSocket连接断开: {conversation_id}")
    except Exception as e:
        logger.error(f"分享WebSocket处理出错: {str(e)}", exc_info=True)
    finally:
        await websocket.close()

# 启动服务器
if __name__ == "__main__":
    logger.info(f"当前北京时间: {get_cn_time().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"启动服务器 {settings.API_HOST}:{settings.API_PORT}")
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=True,
    ) 

# 定义Markdown转PDF的请求模型
class MarkdownToPdfRequest(BaseModel):
    markdown_content: str
    filename: str = "document.pdf"

# Markdown转PDF接口
@app.post("/api/markdown-to-pdf")
async def markdown_to_pdf(request: MarkdownToPdfRequest):
    """将Markdown内容转换为PDF文件"""
    try:
        logger.info("开始处理Markdown转PDF请求")
        
        # 确保输入内容是UTF-8编码
        if isinstance(request.markdown_content, str):
            markdown_content = request.markdown_content
            logger.info(f"输入内容类型: str, 长度: {len(markdown_content)}")
        else:
            markdown_content = request.markdown_content.decode('utf-8')
            logger.info(f"输入内容类型: bytes, 已解码为UTF-8, 长度: {len(markdown_content)}")
        
        # 检查内容是否包含中文字符
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in markdown_content)
        logger.info(f"内容是否包含中文字符: {has_chinese}")
        
        # 自定义CSS样式
        css = '''
            @page {
                margin: 2cm;
                @top-right {
                    content: counter(page);
                }
            }
            body {
                font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
            }
            h1 {
                font-size: 24pt;
                color: #2c3e50;
                border-bottom: 2px solid #eee;
                padding-bottom: 0.3em;
                margin-top: 1em;
                margin-bottom: 0.5em;
            }
            h2 {
                font-size: 20pt;
                color: #34495e;
                margin-top: 1em;
                margin-bottom: 0.5em;
            }
            h3 {
                font-size: 16pt;
                color: #2c3e50;
                margin-top: 1em;
                margin-bottom: 0.5em;
            }
            p {
                margin-bottom: 1em;
            }
            code {
                font-family: "Courier New", monospace;
                background-color: #f5f5f5;
                padding: 0.2em 0.4em;
                border-radius: 3px;
            }
            pre {
                background-color: #f5f5f5;
                padding: 1em;
                border-radius: 5px;
                margin: 1em 0;
            }
            pre code {
                background-color: transparent;
                padding: 0;
            }
            blockquote {
                border-left: 4px solid #ddd;
                padding-left: 1em;
                color: #666;
                margin: 1em 0;
            }
            a {
                color: #3498db;
                text-decoration: none;
            }
            a:hover {
                text-decoration: underline;
            }
            ul, ol {
                margin: 1em 0;
                padding-left: 2em;
            }
            li {
                margin-bottom: 0.5em;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 0.5em;
                text-align: left;
            }
            th {
                background-color: #f5f5f5;
            }
            img {
                max-width: 100%;
                height: auto;
            }
        '''
        
        # 创建临时文件路径
        import tempfile
        import os
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        logger.info(f"创建临时目录: {temp_dir}")
        
        pdf_path = os.path.join(temp_dir, request.filename)
        logger.info(f"PDF输出路径: {pdf_path}")
        
        # 创建临时CSS文件
        css_path = os.path.join(temp_dir, 'style.css')
        logger.info(f"创建CSS文件: {css_path}")
        try:
            with open(css_path, 'w', encoding='utf-8') as f:
                f.write(css)
            logger.info("CSS文件写入成功")
        except Exception as e:
            logger.error(f"CSS文件写入失败: {str(e)}")
            raise
        
        # 创建临时Markdown文件
        md_path = os.path.join(temp_dir, 'content.md')
        logger.info(f"创建Markdown文件: {md_path}")
        try:
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            logger.info("Markdown文件写入成功")
        except Exception as e:
            logger.error(f"Markdown文件写入失败: {str(e)}")
            raise
        
        try:
            logger.info("开始调用md2pdf生成PDF")
            # 使用md2pdf生成PDF
            md2pdf(
                pdf_file_path=pdf_path,
                md_file_path=md_path,
                css_file_path=css_path,
                base_url=None
            )
            logger.info("PDF生成成功")
            
            # 检查PDF文件是否存在
            if not os.path.exists(pdf_path):
                raise Exception("PDF文件未生成")
            
            # 读取生成的PDF文件
            with open(pdf_path, 'rb') as f:
                pdf_content = f.read()
            logger.info(f"PDF文件读取成功，大小: {len(pdf_content)} bytes")
            
            # 返回PDF文件
            from urllib.parse import quote
            filename_encoded = quote(request.filename)
            return Response(
                content=pdf_content,
                media_type="application/pdf",
                headers={
                    "Content-Disposition": f'attachment; filename="{filename_encoded}"'
                }
            )
        except Exception as e:
            logger.error(f"PDF生成过程中出错: {str(e)}")
            # 记录详细的错误信息
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise
        finally:
            logger.info("开始清理临时文件")
            # 清理临时文件
            for file_path in [pdf_path, css_path, md_path]:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        logger.info(f"已删除文件: {file_path}")
                    except Exception as e:
                        logger.error(f"删除文件失败 {file_path}: {str(e)}")
            try:
                os.rmdir(temp_dir)
                logger.info(f"已删除临时目录: {temp_dir}")
            except Exception as e:
                logger.error(f"删除临时目录失败 {temp_dir}: {str(e)}")
        
    except Exception as e:
        logger.error(f"Markdown转PDF失败: {str(e)}")
        # 记录详细的错误信息
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Markdown转PDF失败: {str(e)}")

# region context apis
# 定义文件上传请求模型
class FileUploadRequest(BaseModel):
    conversation_id: str
    file_type: Optional[str] = None
    
# 定义删除上下文请求模型
class DeleteContextRequest(BaseModel):
    conversation_id: str
    context_id: str

@app.post("/api/research/upload_file")
async def upload_file(
    conversation_id: str = Form(...),
    file: UploadFile = File(...),
    file_type: Optional[str] = Form(None),
    current_user: UserDB = Depends(get_current_active_user)
):
    """上传文件并注册到研究智能体的内存中"""
    try:
        # 记录上传的文件信息
        logger.info(f"收到文件上传请求:")
        logger.info(f"文件名: {file.filename}")
        logger.info(f"文件类型: {file.content_type}")
        logger.info(f"文件大小: {file.size if hasattr(file, 'size') else '未知'}")
        logger.info(f"是否为UploadFile实例: {isinstance(file, UploadFile)}")
        
        # 先检查内存中的会话
        if conversation_id in active_sessions:
            session = active_sessions[conversation_id]
        else:
            # 从持久化存储中获取会话
            session_from_db = await session_store.get_session(conversation_id)
            if not session_from_db:
                # 如果会话不存在，创建一个新的会话
                logger.info(f"会话 {conversation_id} 不存在，创建新会话")
                
                # 创建消息队列
                message_queue = asyncio.Queue()
                message_queues[conversation_id] = message_queue
                
                # 初始化用户反馈和澄清问题列表
                user_feedbacks[conversation_id] = []
                clarification_questions[conversation_id] = []
                
                # 创建研究智能体
                research_agent = get_research_agent(get_services(), conversation_id)
                
                # 获取用户的模型偏好设置
                user_models_config = await session_store.get_user_models_preference(str(current_user.id))
                
                # 如果有用户模型偏好，应用到研究智能体
                if user_models_config:
                    research_agent.set_models(user_models_config)
                    logger.info(f"应用用户模型偏好: {user_models_config}")
                
                # 保存会话信息到内存
                active_sessions[conversation_id] = {
                    "status": "created",
                    "agent": research_agent,
                    "last_activity": get_cn_time(),
                    "question": "",
                    "started_at": get_cn_time(),
                    "messages": [],
                    "report": "",
                    "error": "",
                    "clarification_questions": [],
                    "user_id": str(current_user.id),
                    "models_config": user_models_config
                }
                
                # 创建持久化会话存储
                await session_store.create_session(
                    session_id=conversation_id,
                    question="",
                    user_id=str(current_user.id),
                    metadata={"models_config": user_models_config} if user_models_config else None
                )
                
                session = active_sessions[conversation_id]
            else:
                # 验证会话所有权
                if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
                    raise HTTPException(status_code=403, detail="无权访问此研究会话")
                
                # 创建新的研究智能体并加载会话状态
                research_agent = get_research_agent(get_services(), conversation_id)
                await research_agent.memory.load_from_mongodb(conversation_id)
                
                # 将会话加载到内存中
                active_sessions[conversation_id] = {
                    "status": session_from_db.status,
                    "agent": research_agent,
                    "last_activity": get_cn_time(),
                    "question": session_from_db.question,
                    "started_at": session_from_db.started_at,
                    "user_id": session_from_db.user_id,
                    "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
                    "report": session_from_db.report,
                    "error": session_from_db.error,
                    "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else []
                }
                session = active_sessions[conversation_id]
        
        # 获取文件存储路径
        file_path = settings.UPLOAD_PATH + "/" + file.filename
        
        # 写入文件内容
        try:
            # 读取上传的文件内容
            contents = await file.read()
            logger.info(f"文件读取成功:")
            logger.info(f"文件大小: {len(contents)} bytes")
            logger.info(f"文件路径: {file_path}")
            
            # 以二进制模式写入文件
            with open(file_path, 'wb') as f:
                f.write(contents)
            logger.info("文件写入成功")
            
            # 使用研究智能体的方法添加文件上下文
            context_id = await session["agent"].add_file_context(str(file_path))
            logger.info(f"文件上下文添加成功，context_id: {context_id}")
            
            return {
                "status": "success",
                "message": "文件已成功上传并注册到研究智能体",
                "context_id": context_id
            }
            
        except Exception as e:
            # 如果处理过程中出错，删除已上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.error(f"文件处理失败，已删除文件: {file_path}")
            raise e
            
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.post("/api/research/select_note/{document_id}")
async def select_note(
    document_id: str,
    conversation_id: str = Query(..., description="会话ID"),
    current_user: UserDB = Depends(get_current_active_user),
    document: Document = Depends(get_document_by_id)
):
    """选择笔记作为上下文"""
    try:
        # 先检查内存中的会话
        if conversation_id in active_sessions:
            session = active_sessions[conversation_id]
        else:
            # 从持久化存储中获取会话
            session_from_db = await session_store.get_session(conversation_id)
            if not session_from_db:
                # 如果会话不存在，创建一个新的会话
                logger.info(f"会话 {conversation_id} 不存在，创建新会话")
                
                # 创建消息队列
                message_queue = asyncio.Queue()
                message_queues[conversation_id] = message_queue
                
                # 初始化用户反馈和澄清问题列表
                user_feedbacks[conversation_id] = []
                clarification_questions[conversation_id] = []
                
                # 创建研究智能体
                research_agent = get_research_agent(get_services(), conversation_id)
                
                # 获取用户的模型偏好设置
                user_models_config = await session_store.get_user_models_preference(str(current_user.id))
                
                # 如果有用户模型偏好，应用到研究智能体
                if user_models_config:
                    research_agent.set_models(user_models_config)
                    logger.info(f"应用用户模型偏好: {user_models_config}")
                
                # 保存会话信息到内存
                active_sessions[conversation_id] = {
                    "status": "created",
                    "agent": research_agent,
                    "last_activity": get_cn_time(),
                    "question": "",
                    "started_at": get_cn_time(),
                    "messages": [],
                    "report": "",
                    "error": "",
                    "clarification_questions": [],
                    "user_id": str(current_user.id),
                    "models_config": user_models_config
                }
                
                # 创建持久化会话存储
                await session_store.create_session(
                    session_id=conversation_id,
                    question="",
                    user_id=str(current_user.id),
                    metadata={"models_config": user_models_config} if user_models_config else None
                )
                
                session = active_sessions[conversation_id]
            else:
                # 验证会话所有权
                if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
                    raise HTTPException(status_code=403, detail="无权访问此研究会话")
                
                # 创建新的研究智能体并加载会话状态
                research_agent = get_research_agent(get_services(), conversation_id)
                await research_agent.memory.load_from_mongodb(conversation_id)
                
                # 将会话加载到内存中
                active_sessions[conversation_id] = {
                    "status": session_from_db.status,
                    "agent": research_agent,
                    "last_activity": get_cn_time(),
                    "question": session_from_db.question,
                    "started_at": session_from_db.started_at,
                    "user_id": session_from_db.user_id,
                    "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
                    "report": session_from_db.report,
                    "error": session_from_db.error,
                    "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else []
                }
                session = active_sessions[conversation_id]
        
        if not document:
            raise HTTPException(status_code=404, detail="找不到指定的笔记")
        
        await session["agent"].add_note_context(document_id, str(current_user.id), document.content, document.title)
        logger.info(f"笔记 {document_id} 已成功选择")
        return {
            "status": "success",
            "message": "笔记已成功选择"
        }
            
    except Exception as e:
        logger.error(f"选择笔记失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"选择笔记失败: {str(e)}")

# 获取上下文列表接口
@app.get("/api/research/contexts/{conversation_id}")
async def get_contexts(
    conversation_id: str,
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取指定会话的所有上下文列表"""
    try:
        # 先检查内存中的会话
        if conversation_id in active_sessions:
            session = active_sessions[conversation_id]
        else:
            # 从持久化存储中获取会话
            session_from_db = await session_store.get_session(conversation_id)
            if not session_from_db:
                raise HTTPException(status_code=404, detail="找不到指定的研究会话")
            
            # 验证会话所有权
            if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
                raise HTTPException(status_code=403, detail="无权访问此研究会话")
            
            # 创建新的研究智能体并加载会话状态
            research_agent = get_research_agent(get_services(), conversation_id)
            await research_agent.memory.load_from_mongodb(conversation_id)
            
            # 将会话加载到内存中
            active_sessions[conversation_id] = {
                "status": session_from_db.status,
                "agent": research_agent,
                "last_activity": get_cn_time(),
                "question": session_from_db.question,
                "started_at": session_from_db.started_at,
                "user_id": session_from_db.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
                "report": session_from_db.report,
                "error": session_from_db.error,
                "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else [],
                "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False,  # 从元数据中获取标志
                "enable_search": session.metadata.get("enable_search", True) if session.metadata else True  # 从元数据中获取搜索标志
            }
            session = active_sessions[conversation_id]
        
        # 获取所有上下文
        contexts = session["agent"].memory.get_all_contexts()
        # logger.info(f"获取上下文列表: {contexts}")
        # 格式化上下文信息
        context_list = []
        for ctx in contexts:
            context_info = {
                "context_id": ctx["context_id"],
                "metadata": ctx["metadata"],
                "is_valid": True
            }
            
            # 如果是文件上下文，检查文件状态
            if isinstance(ctx, FileContext):
                status = ctx.check_file_status()
                context_info["is_valid"] = status["is_valid"]
                context_info["file_status"] = status
            
            context_list.append(context_info)
        
        return {
            "status": "success",
            "contexts": context_list,
            "total": len(context_list)
        }
        
    except Exception as e:
        import traceback
        error_stack = traceback.format_exc()
        logger.error(f"获取上下文列表失败: {str(e)}\n{error_stack}")
        raise HTTPException(status_code=500, detail=f"获取上下文列表失败: {str(e)}")

# 删除上下文接口
@app.post("/api/research/contexts/delete")
async def delete_context(
    request: DeleteContextRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """删除指定的上下文"""
    try:
        # 先检查内存中的会话
        if request.conversation_id in active_sessions:
            session = active_sessions[request.conversation_id]
        else:
            # 从持久化存储中获取会话
            session_from_db = await session_store.get_session(request.conversation_id)
            if not session_from_db:
                raise HTTPException(status_code=404, detail="找不到指定的研究会话")
            
            # 验证会话所有权
            if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
                raise HTTPException(status_code=403, detail="无权访问此研究会话")
            
            # 创建新的研究智能体并加载会话状态
            research_agent = get_research_agent(get_services(), request.conversation_id)
            await research_agent.memory.load_from_mongodb(request.conversation_id)
            
            # 将会话加载到内存中
            active_sessions[request.conversation_id] = {
                "status": session_from_db.status,
                "agent": research_agent,
                "last_activity": get_cn_time(),
                "question": session_from_db.question,
                "started_at": session_from_db.started_at,
                "user_id": session_from_db.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
                "report": session_from_db.report,
                "error": session_from_db.error,
                "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else [],
                "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False,  # 从元数据中获取标志
                "enable_search": session.metadata.get("enable_search", True) if session.metadata else True  # 从元数据中获取搜索标志
            }
            session = active_sessions[request.conversation_id]
        
        # 删除上下文
        success = session["agent"].remove_context(request.context_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="找不到指定的上下文")
        
        # 保存到MongoDB
        await session["agent"].memory.save_to_mongodb()
        
        return {
            "status": "success",
            "message": "上下文已成功删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_stack = traceback.format_exc()
        logger.error(f"删除上下文失败: {str(e)}\n{error_stack}")
        raise HTTPException(status_code=500, detail=f"删除上下文失败: {str(e)}")


# endregion
# 创建会话请求模型
class CreateSessionRequest(BaseModel):
    user_id: str
    metadata: Optional[Dict[str, Any]] = None

# 创建会话响应模型
class CreateSessionResponse(BaseModel):
    session_id: str
    message: str

# 创建会话
@app.post("/api/research/create_session", response_model=CreateSessionResponse)
async def create_session(
    request: CreateSessionRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """创建新的研究会话"""
    # 创建会话ID
    session_id = str(uuid.uuid4())
    logger.info(f"用户{current_user.id} 创建研究会话: {session_id}")
    
    # 创建消息队列
    message_queue = asyncio.Queue()
    message_queues[session_id] = message_queue
    
    # 初始化用户反馈和澄清问题列表
    user_feedbacks[session_id] = []
    clarification_questions[session_id] = []
    
    # 创建研究智能体
    research_agent = get_research_agent(get_services(), session_id)
    
    # 获取用户的模型偏好设置
    user_models_config = await session_store.get_user_models_preference(str(current_user.id))
    
    # 如果有用户模型偏好，应用到研究智能体
    if user_models_config:
        research_agent.set_models(user_models_config)
        logger.info(f"应用用户模型偏好: {user_models_config}")
    
    # 保存会话信息到内存(用于临时访问)
    active_sessions[session_id] = {
        "status": "created",  # 初始状态为created
        "agent": research_agent,
        "last_activity": get_cn_time(),
        "question": "",  # 初始问题为空
        "started_at": get_cn_time(),
        "messages": [],
        "report": "",
        "error": "",
        "clarification_questions": [],
        "user_id": str(current_user.id),
        "models_config": user_models_config
    }
    
    # 创建持久化会话存储
    await session_store.create_session(
        session_id=session_id,
        question="",  # 初始问题为空
        user_id=str(current_user.id),
        metadata={"models_config": user_models_config} if user_models_config else None
    )
    
    return CreateSessionResponse(
        session_id=session_id,
        message="研究会话已创建"
    )

# 新的数据模型
# 定义用户偏好模型
class UserPreferences(BaseModel):
    professional: Optional[int] = None
    critical: Optional[int] = None
    comparison: Optional[int] = None
    organization: Optional[int] = None
    cutting_edge: Optional[int] = None
    coverage: Optional[int] = None

class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    user_id: str
    metadata: Optional[Dict[str, Any]] = None

class StartResearchRequest(BaseModel):
    """启动研究请求模型"""
    session_id: str
    question: str
    benchmark_id: Optional[int] = None
    enable_cognition_search: Optional[bool] = False
    enable_search: Optional[bool] = True
    user_preferences: Optional[UserPreferences] = None

class CreateSessionResponse(BaseModel):
    """创建会话响应模型"""
    session_id: str
    message: str

class StartResearchResponse(BaseModel):
    """启动研究响应模型"""
    session_id: str
    message: str

class UpdateResearchStatusRequest(BaseModel):
    session_id: str
    enable_cognition_search: bool

class UpdateSearchStatusRequest(BaseModel):
    session_id: str
    enable_search: bool

# region v2端点
@app.post("/api/v2/research/create_session", response_model=CreateSessionResponse)
async def create_session_v2(
    request: CreateSessionRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """创建新的研究会话（V2版本）"""
    # 创建会话ID
    session_id = str(uuid.uuid4())
    logger.info(f"用户{current_user.id} 创建研究会话: {session_id}")
    
    # 创建消息队列
    message_queue = asyncio.Queue()
    message_queues[session_id] = message_queue
    
    # 初始化用户反馈和澄清问题列表
    user_feedbacks[session_id] = []
    clarification_questions[session_id] = []
    
    # 创建研究智能体
    research_agent = get_research_agent(get_services(), session_id)
    
    # 获取用户的模型偏好设置
    user_models_config = await session_store.get_user_models_preference(str(current_user.id))
    
    # 如果有用户模型偏好，应用到研究智能体
    if user_models_config:
        research_agent.set_models(user_models_config)
        logger.info(f"应用用户模型偏好: {user_models_config}")
    
    # 保存会话信息到内存(用于临时访问)
    active_sessions[session_id] = {
        "status": "created",  # 初始状态为created
        "agent": research_agent,
        "last_activity": get_cn_time(),
        "question": "",  # 初始问题为空
        "started_at": get_cn_time(),
        "messages": [],
        "report": "",
        "error": "",
        "clarification_questions": [],
        "user_id": str(current_user.id),
        "models_config": user_models_config,
        "enable_cognition_search": False,
        "enable_search": True  # 默认启用搜索
    }
    
    # 创建持久化会话存储
    await session_store.create_session(
        session_id=session_id,
        question="",  # 初始问题为空
        user_id=str(current_user.id),
        metadata={"models_config": user_models_config} if user_models_config else None
    )
    
    return CreateSessionResponse(
        session_id=session_id,
        message="研究会话已创建"
    )

@app.post("/api/v2/research/start", response_model=StartResearchResponse)
async def start_research_v2(
    request: StartResearchRequest,
    background_tasks: BackgroundTasks,
    services=Depends(get_services),
    current_user: UserDB = Depends(check_user_token_budget)
):
    """启动新的研究任务（V2版本）"""
    print(f"start_research_v2: {request}")
    # import pdb; pdb.set_trace()
    # 检查会话是否存在
    if request.session_id not in active_sessions:
        session_from_db = await session_store.get_session(request.session_id)
        if not session_from_db:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        
        # 创建新的研究智能体并加载会话状态
        research_agent = get_research_agent(get_services(), request.session_id)
        
        await research_agent.memory.load_from_mongodb(request.session_id)
        
        # 设置认知搜索状态
        research_agent.set_enable_cognition_search(request.enable_cognition_search)
        
        # 设置搜索状态
        research_agent.set_enable_search(request.enable_search)

        # 将会话加载到内存中
        active_sessions[request.session_id] = {
            "status": session_from_db.status,
            "agent": research_agent,
            "last_activity": get_cn_time(),
            "question": session_from_db.question,
            "started_at": session_from_db.started_at,
            "user_id": session_from_db.user_id,
            "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
            "report": session_from_db.report,
            "error": session_from_db.error,
            "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else [],
            "enable_cognition_search": request.enable_cognition_search,
            "enable_search": request.enable_search
        }
    
    session = active_sessions[request.session_id]
    research_agent = session["agent"]
    research_agent.set_enable_cognition_search(request.enable_cognition_search)
    research_agent.set_enable_search(request.enable_search)
    # 更新用户偏好（draft相关）
    research_agent.update_user_preference(request.user_preferences.model_dump())
    # 再次检查用户模型偏好（以防在会话创建后配置发生变化）
    user_models_config = await session_store.get_user_models_preference(str(current_user.id))
    if user_models_config:
        research_agent.set_models(user_models_config)
        logger.info(f"在研究任务启动前再次应用用户模型偏好: {user_models_config}")
        
        # 更新会话中的模型配置记录
        session["models_config"] = user_models_config
        await session_store.set_session_models_config(request.session_id, user_models_config)
    
    # 准备输入数据
    input_data = {
        "question": request.question,
        "user_id": str(current_user.id),
    }
    
    # 如果有benchmark_id，添加到输入数据中
    if request.benchmark_id is not None:
        input_data["benchmark_id"] = request.benchmark_id
        
        # 如果是benchmark，尝试加载标准答案
        benchmark_data = load_benchmark_data()
        if 0 <= request.benchmark_id < len(benchmark_data):
            benchmark_item = benchmark_data[request.benchmark_id]
            research_agent.standard_answer = benchmark_item.get("answer", "未提供标准答案")
    
    # 更新会话状态
    session["status"] = "running"
    session["question"] = request.question
    session["last_activity"] = get_cn_time()
    
    # 更新持久化存储
    await session_store.update_session(
        session_id=request.session_id,
        question=request.question,
        status="running"
    )
    
    # 在后台启动研究任务
    background_tasks.add_task(run_agent_and_send_to_websocket, research_agent, input_data, request.session_id)
    
    return StartResearchResponse(
        session_id=request.session_id,
        message="研究任务已启动，请通过WebSocket连接获取结果"
    )

# 更新research状态
@app.post("/api/v2/research/update_status")
async def update_research_status(
    request: UpdateResearchStatusRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """更新研究状态"""
    session_id = request.session_id
    in_memory = session_id in active_sessions
    if in_memory:
        session = active_sessions[session_id]
    else:
        # 从持久化存储中获取状态
        session = await session_store.get_session(session_id)
        if session:
            # 获取会话关联的conversation_id
            conversation_id = session_id
            if not conversation_id:
                raise HTTPException(status_code=400, detail="conversation_id is required")
            # 将会话加载到内存中
            active_sessions[session_id] = {
                "status": session.status,
                "agent": get_research_agent(get_services(), conversation_id),
                "last_activity": get_cn_time(),
                "question": session.question,
                "started_at": session.started_at,
                "user_id": session.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session.messages] if session.messages else [],
                "report": session.report,
                "error": session.error,
                "clarification_questions": session.clarification_questions if session.clarification_questions else [],
                "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False,  # 从元数据中获取标志
                "enable_search": session.metadata.get("enable_search", True) if session.metadata else True  # 从元数据中获取搜索标志
            }
            session = active_sessions[session_id]
    if not session:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    # 更新认知搜索状态
    research_agent = session["agent"]
    session["enable_cognition_search"] = request.enable_cognition_search
    research_agent.set_enable_cognition_search(request.enable_cognition_search)

    logger.info(f"更新认知搜索状态: {request.enable_cognition_search}")
    # 更新持久化存储
    await session_store.update_session(
        session_id=session_id,
        metadata={"enable_cognition_search": request.enable_cognition_search}
    )
    
    return {"status": "success", "message": "研究状态已更新"}

# 更新搜索状态
@app.post("/api/v2/research/update_search_status")
async def update_search_status(
    request: UpdateSearchStatusRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """更新搜索状态"""
    session_id = request.session_id
    in_memory = session_id in active_sessions
    if in_memory:
        session = active_sessions[session_id]
    else:
        # 从持久化存储中获取状态
        session = await session_store.get_session(session_id)
        if session:
            # 获取会话关联的conversation_id
            conversation_id = session_id
            if not conversation_id:
                raise HTTPException(status_code=400, detail="conversation_id is required")
            # 将会话加载到内存中
            active_sessions[session_id] = {
                "status": session.status,
                "agent": get_research_agent(get_services(), conversation_id),
                "last_activity": get_cn_time(),
                "question": session.question,
                "started_at": session.started_at,
                "user_id": session.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session.messages] if session.messages else [],
                "report": session.report,
                "error": session.error,
                "clarification_questions": session.clarification_questions if session.clarification_questions else [],
                "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False,  # 从元数据中获取标志
                "enable_search": session.metadata.get("enable_search", True) if session.metadata else True  # 从元数据中获取搜索标志
            }
            session = active_sessions[session_id]
    if not session:
        raise HTTPException(status_code=404, detail="找不到指定的研究会话")
    
    # 更新搜索状态
    research_agent = session["agent"]
    session["enable_search"] = request.enable_search
    research_agent.set_enable_search(request.enable_search)

    logger.info(f"更新搜索状态: {request.enable_search}")
    # 更新持久化存储
    await session_store.update_session(
        session_id=session_id,
        metadata={"enable_search": request.enable_search}
    )
    
    return {"status": "success", "message": "搜索状态已更新"}

# endregion

# 用户编辑记录请求模型
class EditRecordRequest(BaseModel):
    type: str
    timestamp: str
    original_content: str
    edited_content: str

# 接收用户编辑记录API
@app.post("/api/research/{session_id}/edit_record")
async def add_edit_record(
    session_id: str,
    request: EditRecordRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """将用户编辑记录添加到研究轨迹中"""
    try:
        # 验证会话是否存在
        if session_id in active_sessions:
            session = active_sessions[session_id]
        else:
            # 尝试从持久化存储中获取会话
            session_from_db = await session_store.get_session(session_id)
            if not session_from_db:
                raise HTTPException(status_code=404, detail="找不到指定的研究会话")
            
            # 验证会话所有权
            if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
                raise HTTPException(status_code=403, detail="无权访问此研究会话")
            
            # 创建新的研究智能体并加载会话状态
            research_agent = get_research_agent(get_services(), session_id)
            await research_agent.memory.load_from_mongodb(session_id)
            
            # 将会话加载到内存中
            active_sessions[session_id] = {
                "status": session_from_db.status,
                "agent": research_agent,
                "last_activity": get_cn_time(),
                "question": session_from_db.question,
                "started_at": session_from_db.started_at,
                "user_id": session_from_db.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
                "report": session_from_db.report,
                "error": session_from_db.error,
                "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else [],
                "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False,  # 从元数据中获取标志
                "enable_search": session.metadata.get("enable_search", True) if session.metadata else True  # 从元数据中获取搜索标志
            }
            session = active_sessions[session_id]
        
        # 获取研究智能体
        research_agent = session["agent"]
        
        # 确保research_trajectory存在
        if not hasattr(research_agent, "memory") or not hasattr(research_agent.memory, "research_trajectory"):
            logger.warning(f"研究智能体没有research_trajectory属性，无法保存编辑记录: {session_id}")
            return {"status": "error", "message": "研究智能体不支持轨迹记录"}
        
        # 添加用户编辑记录到轨迹
        if "user_edits" not in research_agent.memory.research_trajectory:
            research_agent.memory.research_trajectory["user_edits"] = []
        
        # 创建编辑记录
        edit_record = {
            "type": request.type,
            "timestamp": request.timestamp,
            "original_content": request.original_content,
            "edited_content": request.edited_content
        }
        
        # 添加编辑记录
        research_agent.memory.research_trajectory["user_edits"].append(edit_record)
        
        # 保存更新后的轨迹
        save_research_trajectory(research_agent, session_id, "用户编辑")
        
        return {"status": "success", "message": "编辑记录已添加到研究轨迹"}
    except Exception as e:
        logger.error(f"添加编辑记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"添加编辑记录失败: {str(e)}")

class ReportFeedbackRequest(BaseModel):
    sessionId:str
    rating: str
    comment: str
    timestamp: str

@app.post("/research/report_feedback")
async def submit_report_feedback(
    feedback: ReportFeedbackRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    session_store = SessionStore()
    
    result = await session_store.add_report_feedback(
        session_id=feedback.sessionId,
        user_id=str(current_user.id),
        rating=feedback.rating,
        comment=feedback.comment
    )
    
    if not result:
        raise HTTPException(status_code=500, detail="保存反馈失败")
    
    return {"status": "success", "message": "反馈已提交"}

@app.get("/api/admin/token-usage")
async def get_all_users_token_usage(
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """
    获取所有用户的token使用统计（仅限管理员）
    """
    try:
        # 创建会话存储实例
        store = SessionStore()
        
        # 获取所有用户token使用统计
        usage_stats = await store.get_all_users_token_usage()
        
        # 如果没有数据，返回空列表
        if not usage_stats:
            return {
                "status": "success",
                "message": "暂无token使用记录",
                "data": []
            }
        
        # 计算总体统计信息
        total_input_tokens = sum(stats.get("total_input_tokens", 0) for stats in usage_stats)
        total_output_tokens = sum(stats.get("total_output_tokens", 0) for stats in usage_stats)
        total_cost = sum(stats.get("total_cost", 0) for stats in usage_stats)
        total_sessions = sum(stats.get("sessions_count", 0) for stats in usage_stats)
        
        # 返回结果
        return {
            "status": "success",
            "message": f"成功获取{len(usage_stats)}个用户的token使用统计",
            "data": usage_stats,
            "summary": {
                "total_users": len(usage_stats),
                "total_sessions": total_sessions,
                "total_input_tokens": total_input_tokens,
                "total_output_tokens": total_output_tokens,
                "total_cost": total_cost
            }
        }
    except Exception as e:
        logger.error(f"获取token使用统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取token使用统计失败: {str(e)}")

@app.get("/api/admin/sessions-token-usage")
async def get_all_sessions_token_usage(
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(100, description="返回的最大记录数"),
    current_admin: UserDB = Depends(get_current_admin_user)
):
    """
    获取所有会话的token使用统计（仅限管理员）
    """
    try:
        # 创建会话存储实例
        store = SessionStore()
        
        # 获取所有会话token使用统计
        usage_stats = await store.get_all_sessions_token_usage(limit=limit, skip=skip)
        
        # 如果没有数据，返回空列表
        if not usage_stats:
            return {
                "status": "success",
                "message": "暂无会话token使用记录",
                "data": []
            }
        
        # 计算返回的会话总体统计信息
        total_input_tokens = sum(stats.get("total_input_tokens", 0) for stats in usage_stats)
        total_output_tokens = sum(stats.get("total_output_tokens", 0) for stats in usage_stats)
        total_cost = sum(stats.get("total_cost", 0) for stats in usage_stats)
        
        # 返回结果
        return {
            "status": "success",
            "message": f"成功获取{len(usage_stats)}个会话的token使用统计",
            "data": usage_stats,
            "summary": {
                "total_sessions": len(usage_stats),
                "total_input_tokens": total_input_tokens,
                "total_output_tokens": total_output_tokens,
                "total_cost": total_cost
            },
            "pagination": {
                "skip": skip,
                "limit": limit
            }
        }
    except Exception as e:
        logger.error(f"获取会话token使用统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话token使用统计失败: {str(e)}")

@app.get("/api/user/token-usage")
async def get_current_user_token_usage(
    current_user: UserDB = Depends(get_current_active_user)
):
    """
    获取当前用户的token使用统计
    """
    try:
        # 创建会话存储实例
        store = SessionStore()
        
        # 获取当前用户的token使用统计
        user_id = str(current_user.id)
        usage_stats = await store.get_user_token_usage(user_id)
        
        # 如果没有数据，返回默认值
        if not usage_stats:
            return {
                "status": "success",
                "message": "暂无token使用记录",
                "data": {
                    "user_id": user_id,
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_cost": 0,
                    "sessions_count": 0,
                    "models_usage": {}
                }
            }
        
        return {
            "status": "success",
            "message": "成功获取用户token使用统计",
            "data": usage_stats
        }
    except Exception as e:
        logger.error(f"获取用户token使用统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户token使用统计失败: {str(e)}")

@app.get("/api/research/request_context/{context_id}")
async def get_context_content(
    context_id: str,
    conversation_id: str = Query(..., description="会话ID"),
    current_user: UserDB = Depends(get_current_active_user)
):
    """获取特定上下文的内容"""
    try:
        logger.info(f"获取上下文内容: {context_id}, {conversation_id}")
        if conversation_id not in active_sessions:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        logger.info(f"获取上下文内容: {conversation_id}")
        session = active_sessions[conversation_id]
        memory = session["agent"].memory
        
        if not memory:
            raise HTTPException(status_code=404, detail="找不到会话内存")
            
        # 获取所有上下文
        contexts = memory.get_all_contexts()
        
        # 查找指定的上下文
        target_context = None
        for ctx in contexts:
            if ctx["context_id"] == context_id:
                target_context = ctx
                break
                
        if not target_context:
            raise HTTPException(status_code=404, detail="找不到指定的上下文")
            
        # 获取上下文类型
        context_type = target_context.get("metadata", {}).get("type")
        
        if context_type == "file":
            # 如果是文件类型，读取文件内容
            file_path = target_context.get("metadata", {}).get("file_path")
            if not file_path or not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail="找不到指定的文件")
                
            # 读取文件内容
            with open(file_path, "rb") as f:
                file_content = f.read()
                
            # 获取文件类型
            file_type = os.path.splitext(file_path)[1].lower()
            
            # 根据文件类型设置正确的 Content-Type
            content_type = {
                ".pdf": "application/pdf",
                ".doc": "application/msword",
                ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt": "text/plain",
                ".md": "text/markdown",
                ".jpg": "image/jpeg",
                ".jpeg": "image/jpeg",
                ".png": "image/png"
            }.get(file_type, "application/octet-stream")
            
            # 返回文件内容
            return Response(
                content=file_content,
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename=\"{os.path.basename(file_path)}\""
                }
            )
        else:
            # 如果不是文件类型，返回上下文内容
            return {
                "context_id": context_id,
                "content": target_context.get("content", ""),
                "metadata": target_context.get("metadata", {})
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取上下文内容失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取上下文内容失败: {str(e)}")

# 提交编辑请求
@app.post("/research/{session_id}/edit_request")
async def submit_edit_request(
    session_id: str, 
    request: EditRequest,
    background_tasks: BackgroundTasks,
    current_user: UserDB = Depends(check_user_token_budget)
):
    """提交编辑请求（专门的 chat to edit 功能）"""
    if session_id not in active_sessions:
        # 尝试从持久化存储中获取会话
        session_from_db = await session_store.get_session(session_id)
        if not session_from_db:
            raise HTTPException(status_code=404, detail="找不到指定的研究会话")
        
        # 验证会话所有权
        if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        
        # 创建新的研究智能体并加载会话状态
        research_agent = get_research_agent(get_services(), session_id)
        
        # 初始化会话状态
        active_sessions[session_id] = {
            "agent": research_agent,
            "status": session_from_db.status,
            "last_activity": get_cn_time(),
            "question": session_from_db.question,
            "started_at": session_from_db.started_at,
            "user_id": session_from_db.user_id,
            "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session_from_db.messages] if session_from_db.messages else [],
            "report": session_from_db.report,
            "error": session_from_db.error,
            "clarification_questions": session_from_db.clarification_questions if session_from_db.clarification_questions else [],
            "enable_cognition_search": session_from_db.metadata.get("enable_cognition_search", False) if session_from_db.metadata else False,
            "enable_search": session_from_db.metadata.get("enable_search", True) if session_from_db.metadata else True
        }
    
    session = active_sessions[session_id]
    research_agent = session["agent"]
    
    logger.info(f"收到编辑请求: {session_id}, 内容: {request.edit_content[:100]}...")
    
    
    # 确保消息队列存在
    if session_id not in message_queues:
        message_queues[session_id] = asyncio.Queue()

    if session.get("user_id") and str(session.get("user_id")) != str(current_user.id):
        raise HTTPException(status_code=403, detail="无权访问此研究会话")

    # 获取研究智能体
    research_agent = active_sessions[session_id]["agent"]

    user_models_config = await session_store.get_user_models_preference(str(current_user.id))
    if user_models_config:
        research_agent.set_models(user_models_config)
        logger.info(f"在研究任务启动前再次应用用户模型偏好: {user_models_config}")
        
        # 更新会话中的模型配置记录
        session["models_config"] = user_models_config
        await session_store.set_session_models_config(session_id, user_models_config)

    # 重要：将编辑请求消息也保存到数据库的messages字段中
    edit_message = {
        "type": "update",
        "timestamp": get_cn_time(),
        "content": {
            "agent": "Feedback",
            "content": request.edit_content,
            "round": research_agent.memory.round,
        }
    }
    
    # 添加到内存中的session["messages"]
    if "messages" not in active_sessions[session_id]:
        active_sessions[session_id]["messages"] = []
    active_sessions[session_id]["messages"].append(edit_message)
    # user_feedbacks[session_id].append(request.edit_content)
    
    # 保存到数据库
    await session_store.add_message(session_id, "update", {
        "agent": "Feedback",
        "content": request.edit_content,
        "round": research_agent.memory.round,
    })
    
    # 设置编辑指令和模式
    research_agent.set_edit_instruction(request.edit_content)
    
    # 准备输入数据，标记这是一个编辑请求
    input_data = {
        "edit_request": True,
        "edit_content": request.edit_content,
        "conversation_id": session_id,
        "user_id": str(current_user.id),
        "resume": True  # 让research agent知道要恢复执行
    }
    if session_id in message_queues:
        status_message = {
            "type": "status",
            "status": "running",
            "message": "研究任务已恢复"
        }
        await message_queues[session_id].put(status_message)
        logger.info(f"向WebSocket队列发送状态更新消息: {session_id}")
    
    # 使用后台任务运行编辑处理，让research agent内部调度
    background_tasks.add_task(run_agent_and_send_to_websocket, research_agent, input_data, session_id)
    
    return {
        "message": "编辑请求已提交，正在处理中",
        "session_id": session_id,
        "edit_content": request.edit_content
    }

@app.post("/api/research/content_preference")
async def submit_content_preference(
    request: ContentPreferenceRequest,
    current_user: UserDB = Depends(get_current_active_user)
):
    """提交用户对内容的偏好（点赞/取消点赞）"""
    try:
        logger.info(f"用户 {current_user.id} 提交内容偏好: {request}")
        
        # 检查会话是否存在
        conversation_id = request.conversation_id
        if conversation_id not in active_sessions:
            # 尝试从数据库加载会话
            session_from_db = await session_store.get_session(conversation_id)
            if not session_from_db:
                raise HTTPException(status_code=404, detail="找不到指定的研究会话")
            
            # 验证会话所有权
            if session_from_db.user_id and session_from_db.user_id != str(current_user.id):
                raise HTTPException(status_code=403, detail="无权访问此研究会话")
            
            # 如果会话不在内存中，创建一个简单的会话对象以便添加偏好
            research_agent = get_research_agent(get_services(), conversation_id)
            await research_agent.memory.load_from_mongodb(conversation_id)
            
            active_sessions[conversation_id] = {
                "status": session.status,
                "agent": get_research_agent(get_services(), conversation_id),
                "last_activity": get_cn_time(),
                "question": session.question,
                "started_at": session.started_at,
                "user_id": session.user_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else msg for msg in session.messages] if session.messages else [],
                "report": session.report,
                "error": session.error,
                "clarification_questions": session.clarification_questions if session.clarification_questions else [],
                "enable_cognition_search": session.metadata.get("enable_cognition_search", False) if session.metadata else False,
                "token_usage": session.token_usage if session.token_usage else {}
            }
        
        session = active_sessions[conversation_id]
        # 确保消息队列存在
        if conversation_id not in message_queues:
            message_queues[conversation_id] = asyncio.Queue()
        # 验证会话所有权
        if session.get("user_id") != str(current_user.id):
            raise HTTPException(status_code=403, detail="无权访问此研究会话")
        
        # 获取研究智能体
        research_agent = session["agent"]
        
        # 构建元数据
        metadata = {
            "title": request.title,
            "timestamp": request.timestamp or get_cn_time().isoformat(),
            "user_id": str(current_user.id)
        }
        
        # 根据liked状态调用不同的方法
        if request.liked:
            # 用户点赞：调用add_user_content_preference
            result = research_agent.add_user_content_preference(
                url=request.url,
                metadata=metadata
            )
        else:
            # 用户取消点赞：调用remove_user_content_preference
            result = research_agent.remove_user_content_preference(
                url=request.url
            )
        
        if result:
            return {
                "status": "success",
                "message": f"内容偏好已{'添加' if request.liked else '移除'}",
                "data": {
                    "url": request.url,
                    "title": request.title,
                    "liked": request.liked,
                    "timestamp": metadata["timestamp"]
                }
            }
        else:
            raise HTTPException(status_code=500, detail="保存内容偏好失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交内容偏好失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交内容偏好失败: {str(e)}")
