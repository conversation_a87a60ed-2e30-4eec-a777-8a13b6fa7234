from backend.documents.crud import DocumentRepository
from backend.documents.models import Document, DocumentCreate, DocumentUpdate, DocumentResponse, DocumentTreeNode, DocumentCognitionDB, DocumentCognitionResponse, ExtractCognitionRequest, DocumentCognitionListResponse
from typing import Optional, List, Dict, Any
from bson import ObjectId
from fastapi import HTTPException, status
from datetime import datetime
import logging
import jwt
from datetime import timedelta
from backend.auth.security import SECRET_KEY  # 导入统一的SECRET_KEY
from backend.auth.models import UserDB
from backend.auth.crud import UserRepository
from backend.db.mongodb import PyObjectId
from backend.db.dependencies import get_mongodb_collection
from motor.motor_asyncio import AsyncIOMotorCollection
import secrets
import uuid
import os
import json
import re
import requests
import asyncio
from xml.etree import ElementTree
from backend.config import settings
from backend.utils import get_cn_time
from openai import AsyncOpenAI, AsyncAzureOpenAI
# 修改数据库连接导入
from backend.db.mongodb import mongo_manager

logger = logging.getLogger(__name__)

# 认知提取的系统提示词
COGNITION_EXTRACTION_PROMPT = """请分析以下文本并提取有价值的"认知"内容。

认知的定义：
一个"认知"是指文本中体现的有价值思考，包括：

1. 深度见解和推理过程：展现深度思考、复杂推理的内容段落，特别关注：
   - 重要概念之间的联系和关系
   - 对复杂问题的分析和解释
   - 有价值的结论和洞察
   - 实用的方法论和框架

2. 重要的学术发现和技术进展：
   - 新的研究方法或技术
   - 重要的实验结果和数据分析
   - 技术发展趋势的总结
   - 问题解决方案的比较分析

提取要求：
- 优先提取有价值的分析、总结、结论
- 包括学术综述中的关键发现
- 接受技术文档中的重要信息点
- 确保提取的内容对读者有学习价值
- 必须同时提供中文和英文版本

输出格式要求：
如果找到认知内容，请严格按照以下XML格式输出（必须包含中英文双语版本）：

<cognition_item>
  <metadata>
    <type>[Synthesis|Analysis|Method|Insight|Summary]</type>
    <confidence>[High|Medium|Low]</confidence>
    <primary_topic>主要话题</primary_topic>
    <related_topic>["相关话题1", "相关话题2"]</related_topic>
    <complexity_level>[Simple|Moderate|Complex]</complexity_level>
  </metadata>
  
  <think_zh>
    [简要说明为什么这段内容有价值，它提供了什么洞察或信息 - 中文版本]
  </think_zh>
  
  <think_en>
    [Brief explanation of why this content is valuable and what insights or information it provides - English version]
  </think_en>
  
  <question_zh>
    [这个认知回答的核心问题 - 中文版本]
  </question_zh>
  
  <question_en>
    [The core question this cognition addresses - English version]
  </question_en>
  
  <answer_zh>
    [提取的认知内容，保持原文的核心信息 - 中文版本]
  </answer_zh>
  
  <answer_en>
    [Extracted cognition content, maintaining core information from the original text - English version]
  </answer_en>
  
  <abstract_zh>
    [认知摘要，概括核心观点 - 中文版本]
  </abstract_zh>
  
  <abstract_en>
    [Cognition abstract, summarizing core viewpoints - English version]
  </abstract_en>
  
  <context_note>
    [可选：额外的背景信息]
  </context_note>
  
  <connections>
    [可选：与其他概念的关联]
  </connections>
</cognition_item>

如果没有找到任何有价值的认知内容，请回复：No cognitions found

待分析文本：
"""

# Topic选择的系统提示词
TOPIC_SELECTION_PROMPT = """请根据以下认知内容，为其选择合适的主要话题(primary_topic)和相关话题(related_topics)。

规则：
1. 必须选择1个primary_topic (从顶级节点中选择)
2. 必须选择2个related_topics (从对应primary_topic的子节点中选择)
3. 严格按照提供的话题树结构选择，不能自创话题

可选的话题树结构：
{topic_tree}

认知内容：
问题：{question}
答案：{answer}
思考过程：{think}

请严格按照以下JSON格式输出：
{{
  "primary_topic": "选择的主要话题ID",
  "related_topics": ["相关话题ID1", "相关话题ID2"]
}}

如果无法确定合适的话题，请选择最相关的通用话题。
"""

class DocumentService:
    def __init__(self):
        self.document_repo = DocumentRepository()
        self.user_repo = UserRepository()
        # 话题树数据
        self.topic_tree = {
            "Architecture": {
                "id": "098bd141e32a395506957e08443ff41f",
                "children": {
                    "sequence modeling paradigm": {
                        "id": "acaa477a5dd0683aaba837f93fb5b859",
                        "children": {
                            "sparse attention": {"id": "bed0c21e3a179a71ec7b3efeef75ef01"},
                            "linear attention": {"id": "973c7c1be7d7ad755056bf7ae83ac6eb"},
                            "hybrid attention": {"id": "e828a10e2c5cce769063cfa966b6f51d"}
                        }
                    },
                    "architecture enhancement": {
                        "id": "7b0c097e71eeac96d2239648c7352222",
                        "children": {
                            "long context": {"id": "5c2086e5d5d4650d88c1c261c38b60cb"},
                            "mixture-of-experts": {"id": "eb76c25d56f746d90064085248386330"}
                        }
                    },
                    "multimodal architecture": {
                        "id": "6e1dbcb37821c8144a7902e14a611cc4",
                        "children": {
                            "auto-regressive multimodal": {"id": "5a554b3b5aa9f55348fb1f8d1b54b595"},
                            "diffusion-based multimodal": {"id": "174c27cb15d31a1a9513d54e7ca7ddf1"},
                            "transfusion-based multimodal": {"id": "7152ac8bd315c92e82d8fceb170c775f"}
                        }
                    }
                }
            },
            "LLMOps": {
                "id": "0e551daa7d757246ba906364d6f23984",
                "children": {
                    "Data Engineering": {
                        "id": "a4f0b0e16e4c0f67fc96e0753a90153e",
                        "children": {
                            "Human Annotation": {"id": "aa7f5d3ba2242446c5eda0aacb4d42dc"},
                            "Data Filtering": {"id": "c7ecc7f311e56436b0b549c5e4ecbfb0"},
                            "Data Synthesis": {"id": "91b62d86e3ceb410887ad323939fb1b4"},
                            "Cognition Engineering": {"id": "d632a551b1c77bf5c58d19a8f2196bf6"},
                            "Human-AI Collabration": {"id": "865398195565c44596cd67ad154eaf95"}
                        }
                    },
                    "Pretrain": {
                        "id": "a3bc9faed5d3c00e093252f8e82df5b0",
                        "children": {
                            "Pretraining Recipe": {"id": "dbe39fcfb3c8f6316a9f225f6d66ee07"},
                            "Scaling Law": {"id": "d7e815102f90a6b03467c180f98d86b9"}
                        }
                    },
                    "Mid-train": {
                        "id": "09ddd01cbff43376587b5fe4de8a8edf",
                        "children": {
                            "continue pretraining": {"id": "7953fb2be079064823f80660a49cdd55"},
                            "long-context training": {"id": "c73995b402f92addc45edf55142203a7"}
                        }
                    },
                    "Post-train": {
                        "id": "2e63e549624c2dbc09fcbf65b5671973",
                        "children": {
                            "supervised fine-tuning": {"id": "f4ddc93491efca4d3bb1fb61a8f9e61b"},
                            "reinforcement learning": {"id": "4972aa628780ae370fb6d5bd50dfe1bf"},
                            "self-iterative learning": {"id": "ecfa9bfb8b2d177f26789cb53df0d35d"}
                        }
                    },
                    "Inference-time": {
                        "id": "738f8f3d1d2340c4ed6e466a9e1dda4d",
                        "children": {
                            "Prompt Engineering": {"id": "505c7f80227d4ca1f3b8f08a304fca71"},
                            "Context Engineering": {"id": "8b1296f9f3691064541a7b21f799b5b3"}
                        }
                    },
                    "Evaluation": {
                        "id": "5904fb0ab4f563d0f196134b13edaf1e",
                        "children": {
                            "Benchmark": {"id": "7b5232acdf6d5c272856646bb78c351f"},
                            "Evaluation Metric": {"id": "fb7088709ce7ed7585176c8006887e42"}
                        }
                    }
                }
            },
            "Reasoning": {
                "id": "8e502ea82882c400ac491db8c6bab482",
                "children": {
                    "long thought training": {"id": "75dbd1c05809a9bb6b4c465716635950"},
                    "reinforcement learning scaling": {"id": "8fdd7e2693945ab7a1496819ad8fa628"},
                    "efficient reasoning": {"id": "e0dcb1f5b92b52c3cbcc456754a853e4"}
                }
            },
            "Agent": {
                "id": "aaaf601826a915aba08293d16078e3e5",
                "children": {
                    "benchmark": {
                        "id": "4b605f774ad029d90633d97b4db92467",
                        "children": {
                            "agent environment": {"id": "f55d0d0947f86c9fe04916eb5e6f5f4a"},
                            "agent reward": {"id": "ff29df399c50d8607f18e42906d70c6f"}
                        }
                    },
                    "agentic architecture": {
                        "id": "a8c1281da5410cf96a9fc511c942a86e",
                        "children": {
                            "tool use": {"id": "0e849c0b08a409fa4b4d2ea7420e3f1b"},
                            "multi-agent": {"id": "aee0ad0ba007bbc4a420ce1e8846f95c"},
                            "human-agent interaction": {"id": "431eb40c1af2a43ee6f63b6d02b9d1d4"},
                            "memory management": {"id": "b9f89e368e8edbe49194ecde686d464f"}
                        }
                    },
                    "training": {
                        "id": "1ebed451fa503390bbd4807b6ed55d5f",
                        "children": {
                            "agentic RL": {"id": "5bb1d0566f317b547c33982a68aaea29"},
                            "agentic SFT": {"id": "483c197584f88227efa508a236cece57"}
                        }
                    }
                }
            },
            "System Efficiency": {
                "id": "a1a8b68e4293c516e413664337d08eec",
                "children": {
                    "Efficient Training": {
                        "id": "beb5273226a6089e7a3255ce2ff1f809",
                        "children": {
                            "distributed training": {"id": "21b91b9d36ffa039b4c25d84399f211f"},
                            "RL training infra": {"id": "1e3fa5b3fc8580b118b74afc99fb8d3e"}
                        }
                    },
                    "Efficient Inference": {
                        "id": "2f1a0993452fe4db58350152b9249b9f",
                        "children": {
                            "quantization and pruning": {"id": "83b9fe4f9012c4c1c69edf205f6e25b5"},
                            "knowledge distillation": {"id": "4352c45f1ec9110e044dc5ed2bf49698"},
                            "speculative-decoding": {"id": "6eb9e61a472a4b7492a18c29151d9d6f"},
                            "kv-cache management": {"id": "7e91f052a81e088df716e2fc7be7382d"}
                        }
                    }
                }
            },
            "RL Scaling": {
                "id": "74b8b828c1232f98fb11f0a87059be9b",
                "children": {
                    "code start": {"id": "b124cda37282c51f6053bc6898ef2727"},
                    "RL data selection": {"id": "0f4c9fc1caaa7337a8b7b807c1dda912"},
                    "reward model": {"id": "dca81a2441b01a8f3c4bc6112542f958"},
                    "entropy control": {"id": "a5d3a62fea86ba6faa9200ca16938a5b"}
                }
            },
            "Safety & Security": {
                "id": "ed8cb514280ba84cfd75f71599319656",
                "children": {
                    "safety alignment": {"id": "80384bef4c3b029ff308a7c39c583c28"},
                    "Privacy": {"id": "0c1464d9ac1f84dba44dd6083baca599"},
                    "Explanation": {"id": "fdb0d7eab084d1ee042b68cff5e7d85e"},
                    "Emerging Risks": {"id": "464e05416019fae155b542e39132aa2a"}
                }
            }
        }

    async def _get_creator_info(self, creator_id: str) -> Dict[str, Optional[str]]:
        """获取创建者信息"""
        try:
            user = await self.user_repo.get_by_id(creator_id)
            if user:
                result = {
                    "creator_name": user.username,
                    "creator_email": user.email
                }
                return result
            return {"creator_name": None, "creator_email": None}
        except Exception as e:
            logger.error(f"获取创建者信息失败: creator_id={creator_id}, error={e}")
            return {"creator_name": None, "creator_email": None}

    async def create_document(self, document: DocumentCreate, creator_id: str) -> DocumentResponse:
        logger.warning(f"开始创建文档，创建者ID: {creator_id}")
        # 如果提供了parent_id，验证父节点是否存在
        if document.parent_id is not None:  # 只验证非None的父节点
            logger.warning(f"验证父节点是否存在: {document.parent_id}")
            parent_doc = await self.document_repo.get_document_by_id(document.parent_id)
            if not parent_doc:
                logger.warning(f"父节点不存在: {document.parent_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="父节点不存在"
                )
        
        # 创建新文档
        logger.warning("开始创建新文档")
        doc = await self.document_repo.create_document(document, creator_id)
        logger.warning(f"文档创建成功，原始ID: {doc}")
        doc_dict = doc.model_dump()
        doc_dict["id"] = str(doc.id)  # 确保 id 是字符串类型
        logger.warning(f"文档数据准备完成，ID: {doc_dict['id']}")
        
        # 获取创建者信息
        creator_info = await self._get_creator_info(doc.creator_id)
        doc_dict.update(creator_info)
        
        return DocumentResponse(**doc_dict)

    async def get_document_by_id(self, document_id: str) -> DocumentResponse:
        logger.warning(f"开始获取文档，ID: {document_id}")
        # 获取单个文档
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            logger.warning(f"文档不存在: {document_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        logger.warning(f"成功获取文档: {doc.id}")
        doc_dict = doc.model_dump()
        doc_dict["id"] = str(doc.id)  # 确保 id 是字符串类型
        
        # 获取创建者信息
        creator_info = await self._get_creator_info(doc.creator_id)
        doc_dict.update(creator_info)
        
        return DocumentResponse(**doc_dict)

    async def get_document_tree(self, user_id: str) -> List[DocumentTreeNode]:
        # 获取所有文档
        documents = await self.document_repo.get_all_documents(user_id)
        # logger.warning(f"获取到的文档列表: {documents}")
        
        # 获取用户有权访问的所有文档ID
        accessible_doc_ids = set()
        for doc in documents:
            accessible_doc_ids.add(str(doc.id))
        
        # 转换为节点列表
        nodes = []
        for doc in documents:
            parent_id = doc.parent_id
            
            # 对于协作文档（用户不是owner但是collaborator）
            if doc.owner_id != user_id and user_id in doc.collaborators:
                # 只有在以下情况下才将其显示为根节点：
                # 1. 原本就是根文档（parent_id 为 None）
                # 2. 父文档不在用户的可访问文档列表中
                if parent_id is None or parent_id not in accessible_doc_ids:
                    parent_id = None
                # 否则保持原有的层级关系
                
            node = DocumentTreeNode(
                id=str(doc.id),  # 转换为字符串
                title=doc.title,  # 使用title而不是name
                parent_id=parent_id,
                has_children=False,  # 使用has_children而不是is_folder
                created_at=doc.created_at,
                updated_at=doc.updated_at
            )
            nodes.append(node)
        
        # logger.warning(f"转换后的节点列表: {nodes}")
        return nodes

    async def update_document(self, document_id: str, document: DocumentUpdate, updated_by: Optional[str] = None) -> DocumentResponse:
        # 获取当前文档
        current_doc = await self.document_repo.get_document_by_id(document_id)
        if not current_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查title是否变化并验证是否有效
        if document.title and document.title.strip() != current_doc.title:
            if not document.title.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="文档标题不能为空"
                )
        
        # 检查parent_id是否变化并验证是否会造成循环引用
        if document.parent_id is not None and document.parent_id != current_doc.parent_id:
            if document.parent_id == document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能将文档设置为自己的子文档"
                )
            
            # 检查是否会造成循环引用
            async def is_descendant(child_id: str, target_id: str) -> bool:
                if child_id == target_id:
                    return True
                
                descendants = await self.document_repo.get_documents_by_parent_id(child_id)
                for descendant in descendants:
                    if await is_descendant(str(descendant.id), target_id):
                        return True
                return False
            
            if await is_descendant(document_id, document.parent_id):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能将文档移动到自己的子文档下"
                )
        
        # 准备更新数据
        update_data = document.model_dump(exclude_unset=True)
        update_data["updated_at"] = datetime.utcnow()
        
        # 添加修改者信息
        if updated_by:
            update_data["updated_by"] = updated_by
            # 获取修改者姓名
            try:
                user_info = await self._get_creator_info(updated_by)
                update_data["updated_by_name"] = user_info.get("creator_name", "未知用户")
            except Exception as e:
                logger.error(f"获取修改者信息失败: {e}")
                update_data["updated_by_name"] = "未知用户"
        
        updated_doc = await self.document_repo.update_document(document_id, DocumentUpdate(**update_data))
        if not updated_doc:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新文档失败"
            )
        
        # 处理父文档变更
        if document.parent_id is not None and document.parent_id != current_doc.parent_id:
            # 从原父文档中移除
            if current_doc.parent_id:
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(current_doc.parent_id)},
                    {"$pull": {"children": document_id}}
                )
            
            # 添加到新父文档
            if document.parent_id:
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(document.parent_id)},
                    {"$push": {"children": document_id}}
                )
        
        doc_dict = updated_doc.model_dump()
        doc_dict["id"] = str(updated_doc.id)
        
        # 获取创建者信息
        creator_info = await self._get_creator_info(updated_doc.creator_id)
        doc_dict.update(creator_info)
        
        return DocumentResponse(**doc_dict)

    async def delete_document(self, document_id: str, user_id: str, force: bool = False) -> bool:
        """删除文档 - 只有文档拥有者可以删除"""
        try:
            # 获取文档并检查权限
            doc = await self.document_repo.get_document_by_id(document_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文档不存在"
                )
            
            # 检查权限：只有文档拥有者可以删除文档
            if doc.owner_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有文档拥有者可以删除文档"
                )
            
            if force:
                return await self.document_repo.safe_delete_document(document_id)
            else:
                return await self.document_repo.delete_document(document_id)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除文档失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除文档失败"
            )

    async def delete_all_documents(self, force: bool = False) -> bool:
        """
        删除所有文档
        :param force: 是否强制删除（包括子文档）
        :return: 是否删除成功
        """
        if not force:
            # 检查是否存在有子文档的文档
            all_docs = await self.document_repo.find_many({})
            for doc in all_docs:
                child_docs = await self.document_repo.get_documents_by_parent_id(str(doc.id))
                if child_docs:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="存在包含子文档的文档，请先删除子文档或使用force=True参数强制删除"
                    )
        
        # 执行删除操作
        success = await self.document_repo.delete_all_documents()
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除文档时发生错误"
            )
        return success

    async def move_document(self, document_id: str, target_parent_id: Optional[str], position: str = "inside", reference_node_id: Optional[str] = None) -> DocumentResponse:
        """
        移动文档到新的父节点，支持精确的位置控制
        
        Args:
            document_id: 要移动的文档ID
            target_parent_id: 目标父节点ID，None表示移动到根层级
            position: 插入位置 ("inside", "before", "after")
            reference_node_id: 参考节点ID（当position为before/after时使用）
        """
        try:
            # 获取要移动的文档
            doc_to_move = await self.document_repo.get_document_by_id(document_id)
            if not doc_to_move:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="要移动的文档不存在"
                )

            # 检查目标父节点是否存在（如果不是根层级）
            if target_parent_id:
                target_parent = await self.document_repo.get_document_by_id(target_parent_id)
                if not target_parent:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="目标父节点不存在"
                    )
                
                # 检查是否会造成循环引用
                async def is_descendant(child_id: str, target_id: str) -> bool:
                    if child_id == target_id:
                        return True
                    
                    descendants = await self.document_repo.get_documents_by_parent_id(child_id)
                    for descendant in descendants:
                        if await is_descendant(str(descendant.id), target_id):
                            return True
                    return False
                
                if await is_descendant(document_id, target_parent_id):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="不能将文档移动到自己的子文档下"
                    )

            # 检查参考节点是否存在（当position为before/after时）
            reference_node = None
            if position in ["before", "after"] and reference_node_id:
                reference_node = await self.document_repo.get_document_by_id(reference_node_id)
                if not reference_node:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="参考节点不存在"
                    )

            # 获取当前文档的原父节点
            old_parent_id = doc_to_move.parent_id

            # 处理不同的插入位置
            if position == "inside":
                # 移动到目标节点内部
                new_parent_id = target_parent_id
            elif position in ["before", "after"] and reference_node:
                # 移动到参考节点的前面或后面，使用参考节点的父节点作为新父节点
                new_parent_id = reference_node.parent_id
            else:
                # 默认情况，移动到目标父节点内部
                new_parent_id = target_parent_id

            # 执行移动操作
            update_data = {
                "parent_id": new_parent_id,
                "updated_at": datetime.utcnow()
            }

            await self.document_repo.collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )

            # 更新原父节点的children列表（如果有）
            if old_parent_id:
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(old_parent_id)},
                    {"$pull": {"children": document_id}}
                )

            # 更新新父节点的children列表（如果有）
            if new_parent_id:
                # 检查是否已经在children列表中
                parent_doc = await self.document_repo.get_document_by_id(new_parent_id)
                if parent_doc and document_id not in (parent_doc.children or []):
                    
                    # 根据position决定插入位置
                    if position == "before" and reference_node_id:
                        # 插入到参考节点之前
                        children = parent_doc.children or []
                        if reference_node_id in children:
                            insert_index = children.index(reference_node_id)
                            children.insert(insert_index, document_id)
                        else:
                            children.append(document_id)
                        
                        await self.document_repo.collection.update_one(
                            {"_id": ObjectId(new_parent_id)},
                            {"$set": {"children": children}}
                        )
                    elif position == "after" and reference_node_id:
                        # 插入到参考节点之后
                        children = parent_doc.children or []
                        if reference_node_id in children:
                            insert_index = children.index(reference_node_id) + 1
                            children.insert(insert_index, document_id)
                        else:
                            children.append(document_id)
                        
                        await self.document_repo.collection.update_one(
                            {"_id": ObjectId(new_parent_id)},
                            {"$set": {"children": children}}
                        )
                    else:
                        # 默认添加到末尾
                        await self.document_repo.collection.update_one(
                            {"_id": ObjectId(new_parent_id)},
                            {"$push": {"children": document_id}}
                        )

            # 获取更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            if not updated_doc:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="移动文档后无法获取更新的文档"
                )
            
            # 构建返回数据
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"移动文档失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="移动文档失败"
            )

    async def share_document(
        self, 
        document_id: str, 
        user_id: str, 
        permissions: List[str] = ["read"],
        expiry_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """分享文档给其他用户"""
        # 获取文档并验证权限
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查当前用户是否有管理权限（是创建者或拥有admin权限）
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限分享此文档"
            )
        
        # 检查是否为文件夹（有子文档）
        children_count = await self.document_repo.collection.count_documents({"parent_id": document_id})
        is_folder = children_count > 0
        
        # 生成分享token，包含唯一标识符
        payload = {
            "document_id": document_id,
            "permissions": permissions,
            "shared_by": user_id,
            "created_at": datetime.utcnow().isoformat(),
            "share_id": str(uuid.uuid4()),  # 添加唯一标识符
            "timestamp": int(datetime.utcnow().timestamp() * 1000000),  # 微秒级时间戳
            "is_folder": is_folder  # 标识是否为文件夹分享
        }
        
        if expiry_days:
            payload["exp"] = datetime.utcnow() + timedelta(days=expiry_days)
        
        # 这里需要从config获取secret_key，暂时使用固定值
        secret_key = SECRET_KEY  # 实际项目中应该从配置获取
        share_token = jwt.encode(payload, secret_key, algorithm="HS256")
        
        # 更新文档的权限设置
        current_permissions = doc.permissions.copy()
        for permission in permissions:
            if permission not in current_permissions:
                current_permissions[permission] = []
            # 这里暂时不添加具体用户，因为是公开分享
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "permissions": current_permissions,
                    "shared": True,  # 添加分享状态标识
                    "share_token": share_token,  # 保存当前有效的分享token
                    "is_folder_share": is_folder,  # 保存是否为文件夹分享
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        return {
            "share_token": share_token,
            "share_url": f"/share/{share_token}",
            "permissions": permissions,
            "expiry_days": expiry_days,
            "is_folder": is_folder,
            "created_at": datetime.utcnow().isoformat()
        }
    
    async def revoke_share(self, document_id: str, user_id: str) -> bool:
        """撤销文档分享"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查权限
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限撤销分享"
            )
        
        # 重置权限为默认状态
        default_permissions = {
            "read": [],
            "write": [],
            "admin": [doc.owner_id]
        }
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "permissions": default_permissions,
                    "shared": False,  # 标识分享已被撤销
                    "updated_at": datetime.utcnow()
                },
                "$unset": {
                    "share_token": ""  # 移除分享token
                }
            }
        )
        
        return True
    
    async def get_shared_document(self, share_token: str) -> DocumentResponse:
        """通过分享token获取文档或文件夹"""
        try:
            # 解码token
            secret_key = SECRET_KEY  # 实际项目中应该从配置获取
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            permissions = payload.get("permissions", ["read"])
            is_folder_from_token = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 获取文档
            doc = await self.document_repo.get_document_by_id(document_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 重新检查是否为文件夹（有子文档）
            children_count = await self.document_repo.collection.count_documents({"parent_id": document_id})
            is_folder_current = children_count > 0
            
            # 优先使用数据库中存储的值，如果没有则使用重新计算的值
            is_folder_share = getattr(doc, 'is_folder_share', is_folder_from_token or is_folder_current)
            
            # 返回文档，但隐藏敏感信息
            doc_dict = doc.model_dump()
            doc_dict["id"] = str(doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            doc_dict["is_folder_share"] = is_folder_share  # 使用正确的文件夹标识
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )
    
    async def get_shared_folder_tree(self, share_token: str) -> List[DocumentTreeNode]:
        """获取分享文件夹的文档树"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            is_folder = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            if not is_folder:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="此链接不是文件夹分享"
                )
            
            # 获取根文档
            root_doc = await self.document_repo.get_document_by_id(document_id)
            if not root_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(root_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 递归获取所有相关文档（包括根文档和所有子文档）
            all_documents = []
            
            async def collect_documents(parent_id: str):
                # 如果是第一次调用（根文档），直接添加根文档
                if parent_id == document_id:
                    # 检查根文档是否有子文档
                    children_count = await self.document_repo.collection.count_documents({"parent_id": parent_id})
                    root_node = DocumentTreeNode(
                        id=str(root_doc.id),
                        title=root_doc.title,
                        parent_id=root_doc.parent_id,
                        has_children=children_count > 0,
                        created_at=root_doc.created_at,
                        updated_at=root_doc.updated_at
                    )
                    all_documents.append(root_node)
                
                # 获取子文档
                children_cursor = self.document_repo.collection.find({"parent_id": parent_id})
                async for child_doc in children_cursor:
                    child_id = str(child_doc["_id"])
                    
                    # 检查这个子文档是否还有子文档
                    grandchildren_count = await self.document_repo.collection.count_documents({"parent_id": child_id})
                    
                    child_node = DocumentTreeNode(
                        id=child_id,
                        title=child_doc.get("title", "无标题"),
                        parent_id=child_doc.get("parent_id"),
                        has_children=grandchildren_count > 0,
                        created_at=child_doc.get("created_at"),
                        updated_at=child_doc.get("updated_at")
                    )
                    all_documents.append(child_node)
                    
                    # 递归处理子文档
                    await collect_documents(child_id)
            
            # 从根文档开始收集所有文档
            await collect_documents(document_id)
            
            return all_documents
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )

    async def update_shared_document(self, share_token: str, document: DocumentUpdate, user_id: Optional[str] = None) -> DocumentResponse:
        """通过分享token更新文档"""
        try:
            # 解码token
            secret_key = SECRET_KEY  # 实际项目中应该从配置获取
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            permissions = payload.get("permissions", ["read"])
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 检查是否有写权限
            if "write" not in permissions and "admin" not in permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有编辑权限"
                )
            
            # 获取文档
            doc = await self.document_repo.get_document_by_id(document_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 更新文档，包含修改者信息
            update_data = document.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.utcnow()
            
            # 如果提供了用户ID，获取用户信息作为修改者
            if user_id:
                update_data["updated_by"] = user_id
                try:
                    user_info = await self._get_creator_info(user_id)
                    update_data["updated_by_name"] = user_info.get("creator_name", "协作用户")
                except:
                    update_data["updated_by_name"] = "协作用户"
            else:
                # 如果没有用户ID，使用分享用户标识
                update_data["updated_by"] = "shared_user"
                update_data["updated_by_name"] = "分享用户"
            
            await self.document_repo.collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )
            
            # 返回更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )
    
    async def copy_shared_document_to_user(self, share_token: str, user_id: str, target_parent_id: Optional[str] = None) -> DocumentResponse:
        """将分享的文档复制到用户的笔记中"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 获取原始拥有者信息
            original_owner_info = await self._get_creator_info(original_doc.owner_id)
            
            # 创建文档副本，包含原始文档信息
            copy_data = DocumentCreate(
                title=f"{original_doc.title} (副本)",
                content=original_doc.content,
                parent_id=target_parent_id,
                tags=original_doc.tags.copy() if original_doc.tags else [],
                metadata=original_doc.metadata.copy() if original_doc.metadata else {}
            )
            
            # 先创建基础文档
            new_doc = await self.document_repo.create_document(copy_data, user_id)
            
            # 然后更新原始文档信息
            await self.document_repo.collection.update_one(
                {"_id": ObjectId(str(new_doc.id))},
                {
                    "$set": {
                        "original_document_id": document_id,
                        "original_owner_id": original_doc.owner_id,
                        "original_owner_name": original_owner_info.get("creator_name", "未知用户"),
                        "copied_from_share": True,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            # 重新获取更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(str(new_doc.id))
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )
    
    async def add_collaborator(
        self, 
        document_id: str, 
        collaborator_id: str, 
        permissions: List[str], 
        user_id: str
    ) -> DocumentResponse:
        """添加协作者"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查权限
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限添加协作者"
            )
        
        # 更新协作者列表和权限
        collaborators = list(set(doc.collaborators + [collaborator_id]))
        current_permissions = doc.permissions.copy()
        
        for permission in permissions:
            if permission not in current_permissions:
                current_permissions[permission] = []
            if collaborator_id not in current_permissions[permission]:
                current_permissions[permission].append(collaborator_id)
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "collaborators": collaborators,
                    "permissions": current_permissions,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # 返回更新后的文档
        updated_doc = await self.document_repo.get_document_by_id(document_id)
        doc_dict = updated_doc.model_dump()
        doc_dict["id"] = str(updated_doc.id)
        return DocumentResponse(**doc_dict)
    
    async def remove_collaborator(
        self, 
        document_id: str, 
        collaborator_id: str, 
        user_id: str
    ) -> DocumentResponse:
        """移除协作者"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查权限
        if doc.owner_id != user_id and user_id not in doc.permissions.get("admin", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限移除协作者"
            )
        
        # 更新协作者列表和权限
        collaborators = [c for c in doc.collaborators if c != collaborator_id]
        current_permissions = doc.permissions.copy()
        
        for permission_list in current_permissions.values():
            if collaborator_id in permission_list:
                permission_list.remove(collaborator_id)
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "collaborators": collaborators,
                    "permissions": current_permissions,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        # 返回更新后的文档
        updated_doc = await self.document_repo.get_document_by_id(document_id)
        doc_dict = updated_doc.model_dump()
        doc_dict["id"] = str(updated_doc.id)
        return DocumentResponse(**doc_dict)
    
    async def leave_collaboration(self, document_id: str, user_id: str) -> bool:
        """协作者退出协作"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        # 检查用户是否是协作者
        if user_id not in doc.collaborators:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="您不是此文档的协作者"
            )
        
        # 不允许文档拥有者退出协作
        if doc.owner_id == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档拥有者不能退出协作，请删除文档或转移所有权"
            )
        
        # 从协作者列表中移除用户
        collaborators = [c for c in doc.collaborators if c != user_id]
        current_permissions = doc.permissions.copy()
        
        # 从所有权限列表中移除用户
        for permission_list in current_permissions.values():
            if user_id in permission_list:
                permission_list.remove(user_id)
        
        await self.document_repo.collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$set": {
                    "collaborators": collaborators,
                    "permissions": current_permissions,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        return True

    async def check_document_permission(
        self, 
        document_id: str, 
        user_id: str, 
        required_permission: str = "read"
    ) -> bool:
        """检查用户对文档的权限"""
        doc = await self.document_repo.get_document_by_id(document_id)
        if not doc:
            return False
        
        # 文档所有者拥有所有权限
        if doc.owner_id == user_id:
            return True
        
        # 检查具体权限
        return user_id in doc.permissions.get(required_permission, [])

    async def add_shared_document_to_user_notes(self, share_token: str, user_id: str) -> DocumentResponse:
        """将分享的文档添加到用户的笔记中（协作模式）"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 检查用户是否已经是协作者
            if user_id in original_doc.collaborators:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="此文档已在您的笔记中"
                )
            
            # 将用户添加为协作者
            collaborators = list(set(original_doc.collaborators + [user_id]))
            
            # 根据分享权限设置用户权限
            permissions = payload.get("permissions", ["read"])
            current_permissions = original_doc.permissions.copy()
            
            for permission in permissions:
                if permission not in current_permissions:
                    current_permissions[permission] = []
                if user_id not in current_permissions[permission]:
                    current_permissions[permission].append(user_id)
            
            # 更新原文档的协作者和权限
            await self.document_repo.collection.update_one(
                {"_id": ObjectId(document_id)},
                {
                    "$set": {
                        "collaborators": collaborators,
                        "permissions": current_permissions,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            # 返回更新后的文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )

    async def add_shared_folder_to_user_notes(self, share_token: str, user_id: str) -> DocumentResponse:
        """将分享的文件夹添加到用户的笔记中（复制模式）"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            is_folder = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            if not is_folder:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="此链接不是文件夹分享"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 获取原始拥有者信息
            original_owner_info = await self._get_creator_info(original_doc.owner_id)
            
            # 递归复制文件夹及其所有子文档
            async def copy_document_recursive(source_doc_id: str, target_parent_id: Optional[str] = None) -> str:
                # 获取源文档
                source_doc = await self.document_repo.get_document_by_id(source_doc_id)
                if not source_doc:
                    return None
                
                # 创建文档副本
                copy_data = DocumentCreate(
                    title=source_doc.title,
                    content=source_doc.content,
                    parent_id=target_parent_id,
                    tags=source_doc.tags.copy() if source_doc.tags else [],
                    metadata=source_doc.metadata.copy() if source_doc.metadata else {}
                )
                
                # 创建新文档
                new_doc = await self.document_repo.create_document(copy_data, user_id)
                
                # 更新原始文档信息
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(str(new_doc.id))},
                    {
                        "$set": {
                            "original_document_id": source_doc_id,
                            "original_owner_id": original_doc.owner_id,
                            "original_owner_name": original_owner_info.get("creator_name", "未知用户"),
                            "copied_from_share": True,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                
                # 递归复制子文档
                children_cursor = self.document_repo.collection.find({"parent_id": source_doc_id})
                async for child_doc in children_cursor:
                    child_id = str(child_doc["_id"])
                    await copy_document_recursive(child_id, str(new_doc.id))
                
                return str(new_doc.id)
            
            # 开始复制
            root_copy_id = await copy_document_recursive(document_id)
            
            # 获取复制后的根文档
            copied_doc = await self.document_repo.get_document_by_id(root_copy_id)
            doc_dict = copied_doc.model_dump()
            doc_dict["id"] = str(copied_doc.id)
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(copied_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )

    async def add_shared_folder_to_user_notes_collaborative(self, share_token: str, user_id: str) -> DocumentResponse:
        """将分享的文件夹添加到用户的笔记中（协作模式）"""
        try:
            # 解码token
            secret_key = SECRET_KEY
            payload = jwt.decode(share_token, secret_key, algorithms=["HS256"])
            
            document_id = payload.get("document_id")
            is_folder = payload.get("is_folder", False)
            
            if not document_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的分享链接"
                )
            
            if not is_folder:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="此链接不是文件夹分享"
                )
            
            # 获取原文档
            original_doc = await self.document_repo.get_document_by_id(document_id)
            if not original_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享的文档不存在"
                )
            
            # 检查文档是否仍然处于分享状态
            if not getattr(original_doc, 'shared', False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已被撤销"
                )
            
            # 检查用户是否已经是协作者
            if user_id in original_doc.collaborators:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="此文件夹已在您的笔记中"
                )
            
            # 获取分享权限
            permissions = payload.get("permissions", ["read"])
            
            # 递归添加协作者到文件夹及所有子文档
            async def add_collaborator_recursive(doc_id: str):
                # 获取文档
                doc = await self.document_repo.get_document_by_id(doc_id)
                if not doc:
                    return
                
                # 添加用户为协作者
                collaborators = list(set(doc.collaborators + [user_id]))
                current_permissions = doc.permissions.copy()
                
                # 设置用户权限
                for permission in permissions:
                    if permission not in current_permissions:
                        current_permissions[permission] = []
                    if user_id not in current_permissions[permission]:
                        current_permissions[permission].append(user_id)
                
                # 更新文档
                await self.document_repo.collection.update_one(
                    {"_id": ObjectId(doc_id)},
                    {
                        "$set": {
                            "collaborators": collaborators,
                            "permissions": current_permissions,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                
                # 递归处理子文档
                children_cursor = self.document_repo.collection.find({"parent_id": doc_id})
                async for child_doc in children_cursor:
                    child_id = str(child_doc["_id"])
                    await add_collaborator_recursive(child_id)
            
            # 开始递归添加协作者
            await add_collaborator_recursive(document_id)
            
            # 返回更新后的根文档
            updated_doc = await self.document_repo.get_document_by_id(document_id)
            doc_dict = updated_doc.model_dump()
            doc_dict["id"] = str(updated_doc.id)
            doc_dict["share_permissions"] = permissions  # 添加分享权限信息
            
            # 获取创建者信息
            creator_info = await self._get_creator_info(updated_doc.creator_id)
            doc_dict.update(creator_info)
            
            return DocumentResponse(**doc_dict)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="分享链接已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的分享链接"
            )

    async def extract_cognitions_from_documents(self, document_ids: List[str], user_id: str) -> Dict[str, Any]:
        """从文档中提取认知"""
        try:
            # 验证文档数量限制
            if len(document_ids) > 5:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="最多只能选择5个文档进行认知提取"
                )
            
            # 获取文档内容
            documents = []
            for doc_id in document_ids:
                doc = await self.get_document_by_id(doc_id)
                if not doc:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"文档 {doc_id} 不存在"
                    )
                # 检查用户权限（简化版本，可以根据需要扩展）
                if doc.owner_id != user_id and user_id not in doc.collaborators:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"无权访问文档 {doc_id}"
                    )
                documents.append(doc)
            
            # 提取每个文档的认知
            individual_cognitions = []
            for doc in documents:
                if doc.content.strip():  # 确保文档有内容
                    cognition = await self._extract_single_document_cognition(doc.content, doc.title, str(doc.id))
                    if cognition:
                        individual_cognitions.append({
                            "document_id": str(doc.id),  # 确保转换为字符串
                            "document_title": doc.title,
                            "cognition": cognition
                        })
            
            if not individual_cognitions:
                return {
                    "success": False,
                    "message": "从选定的文档中未能提取到有效的认知内容",
                    "individual_cognitions": [],
                    "synthesized_cognition": None
                }
            
            # 如果有多个认知，进行合成
            synthesized_cognition = None
            if len(individual_cognitions) > 1:
                synthesized_cognition = await self._synthesize_cognitions(individual_cognitions)
            
            # 只保存综合认知，不保存个人认知
            saved_synthesized = None
            if synthesized_cognition:
                saved_synthesized = await self._save_document_cognition(
                    user_id=user_id,
                    source_document_ids=[str(doc.id) for doc in documents],  # 确保转换为字符串
                    source_document_titles=[doc.title for doc in documents],
                    cognition_data=synthesized_cognition,
                    is_synthesized=True,
                    parent_cognition_ids=[]  # 不记录父认知ID，因为没有保存个人认知
                )
                
                return {
                    "success": True,
                    "message": f"成功从 {len(individual_cognitions)} 个文档中合成了1个综合认知",
                    "individual_cognitions": [],  # 不返回个人认知
                    "synthesized_cognition": saved_synthesized,
                    "total_documents": len(documents),
                    "total_cognitions": 1  # 只有一个综合认知
                }
            
            elif len(individual_cognitions) == 1:
                # 如果只有一个认知，直接保存为综合认知
                single_cognition = individual_cognitions[0]
                saved_synthesized = await self._save_document_cognition(
                    user_id=user_id,
                    source_document_ids=[single_cognition["document_id"]],
                    source_document_titles=[single_cognition["document_title"]],
                    cognition_data=single_cognition["cognition"],
                    is_synthesized=True,  # 标记为综合认知
                    parent_cognition_ids=[]
                )
                
                return {
                    "success": True,
                    "message": f"成功提取了1个认知",
                    "individual_cognitions": [],
                    "synthesized_cognition": saved_synthesized,
                    "total_documents": len(documents),
                    "total_cognitions": 1
                }
            
            else:
                return {
                    "success": False,
                    "message": "未能提取到有效的认知内容",
                    "individual_cognitions": [],
                    "synthesized_cognition": None,
                    "total_documents": len(documents),
                    "total_cognitions": 0
                }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"提取文档认知失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"提取认知过程中发生错误: {str(e)}"
            )

    async def _extract_single_document_cognition(self, content: str, title: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """从单个文档中提取认知"""
        try:
            # 检查内容长度
            logger.info(f"开始提取文档认知 - 文档ID: {doc_id}, 标题: {title}, 内容长度: {len(content)}")
            
            if len(content.strip()) < 50:
                logger.warning(f"文档内容过短，跳过认知提取 - 文档ID: {doc_id}, 内容长度: {len(content)}")
                return None
            
            # 构建提示词
            full_prompt = COGNITION_EXTRACTION_PROMPT + f"\n\n标题: {title}\n内容:\n{content[:5000]}"  # 限制内容长度
            logger.info(f"准备调用AI服务 - 文档ID: {doc_id}, 提示词长度: {len(full_prompt)}")
            
            # 调用AI服务
            ai_response = await self._call_ai_service(full_prompt)
            logger.info(f"AI服务响应 - 文档ID: {doc_id}, 响应长度: {len(ai_response) if ai_response else 0}")
            logger.info(f"AI响应内容预览: {ai_response[:500] if ai_response else 'None'}...")
            
            if not ai_response:
                logger.warning(f"AI服务返回空响应 - 文档ID: {doc_id}")
                return None
                
            if "No cognitions found" in ai_response:
                logger.info(f"AI服务未找到认知内容 - 文档ID: {doc_id}")
                return None
            
            # 解析XML响应
            logger.info(f"开始解析XML响应 - 文档ID: {doc_id}")
            cognition_data = self._parse_cognition_xml(ai_response)
            logger.info(f"XML解析成功 - 文档ID: {doc_id}, 认知数据: {cognition_data}")
            
            return cognition_data
            
        except Exception as e:
            logger.error(f"提取单个文档认知失败 - 文档ID: {doc_id}, 错误: {str(e)}")
            logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
            return None

    async def _call_ai_service(self, prompt: str) -> str:
        """调用AI服务进行认知提取"""
        try:
            # 使用Azure OpenAI配置
            from openai import AsyncAzureOpenAI
            
            client = AsyncAzureOpenAI(
                api_key=settings.AZURE_API_KEY,
                azure_endpoint=settings.AZURE_API_URL,
                api_version=settings.AZURE_API_VERSION
            )
            
            response = await client.chat.completions.create(
                model=settings.AI_MODEL_COGNITION_EXTRACTION,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.6,
                max_tokens=2000,
                timeout=settings.AI_REQUEST_TIMEOUT
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Azure OpenAI服务调用失败: {str(e)}")
            # 备用：尝试使用OpenRouter（如果配置了）
            if settings.OPENROUTER_API_KEY:
                try:
                    response = requests.post(
                        url="https://openrouter.ai/api/v1/chat/completions",
                        headers={
                            "Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                        },
                        json={
                            "model": "anthropic/claude-3.5-sonnet",
                            "messages": [
                                {"role": "user", "content": prompt}
                            ],
                        },
                        timeout=settings.AI_REQUEST_TIMEOUT
                    )
                    response.raise_for_status()
                    data = response.json()
                    return data['choices'][0]['message']['content']
                except Exception as backup_error:
                    logger.error(f"OpenRouter备用服务也失败: {str(backup_error)}")
            
            raise Exception(f"AI服务调用失败: {str(e)}")

    async def _call_synthesis_ai_service(self, prompt: str) -> str:
        """调用AI服务进行认知合成"""
        try:
            # 使用Azure OpenAI配置
            from openai import AsyncAzureOpenAI
            
            client = AsyncAzureOpenAI(
                api_key=settings.AZURE_API_KEY,
                azure_endpoint=settings.AZURE_API_URL,
                api_version=settings.AZURE_API_VERSION
            )
            
            response = await client.chat.completions.create(
                model=settings.AI_MODEL_COGNITION_SYNTHESIS,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.6,
                max_tokens=2000,
                timeout=settings.AI_REQUEST_TIMEOUT
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Azure OpenAI认知合成服务调用失败: {str(e)}")
            # 备用：尝试使用OpenRouter（如果配置了）
            if settings.OPENROUTER_API_KEY:
                try:
                    response = requests.post(
                        url="https://openrouter.ai/api/v1/chat/completions",
                        headers={
                            "Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                        },
                        json={
                            "model": "anthropic/claude-3.5-sonnet",
                            "messages": [
                                {"role": "user", "content": prompt}
                            ],
                        },
                        timeout=settings.AI_REQUEST_TIMEOUT
                    )
                    response.raise_for_status()
                    data = response.json()
                    return data['choices'][0]['message']['content']
                except Exception as backup_error:
                    logger.error(f"OpenRouter备用服务也失败: {str(backup_error)}")
            
            raise Exception(f"认知合成AI服务调用失败: {str(e)}")

    def _parse_cognition_xml(self, xml_string: str) -> Dict[str, Any]:
        """解析认知提取的XML响应"""
        try:
            logger.info(f"开始解析XML - 原始XML长度: {len(xml_string)}")
            logger.info(f"原始XML内容: {xml_string}")
            
            # 清理XML字符串
            xml_string = self._clean_xml_string(xml_string)
            
            # 如果XML不包含cognition_item，尝试提取内容
            if '<cognition_item>' not in xml_string:
                logger.warning("XML中未找到cognition_item标签，尝试提取内容")
                return self._extract_cognition_from_text(xml_string)
            
            # 包装在根标签中确保XML有效
            xml_string_wrapped = f"<root>{xml_string}</root>"
            logger.info(f"包装后的XML: {xml_string_wrapped[:500]}...")
            
            root = ElementTree.fromstring(xml_string_wrapped)
            logger.info("XML解析成功")
            
            cognition_item = root.find('cognition_item')
            if cognition_item is None:
                logger.error("XML中未找到cognition_item元素")
                # 尝试查找可能的其他结构
                logger.info(f"根元素的子元素: {[child.tag for child in root]}")
                # 如果没有cognition_item，尝试从文本中提取
                return self._extract_cognition_from_text(xml_string)
            
            logger.info("找到cognition_item元素")
            cognition = {}
            
            # 解析metadata
            metadata_node = cognition_item.find('metadata')
            if metadata_node is not None:
                logger.info("找到metadata节点")
                metadata = {}
                for child in metadata_node:
                    logger.info(f"处理metadata子元素: {child.tag} = {child.text}")
                    if child.tag == 'related_topic':
                        # 处理列表格式
                        topics_text = child.text.strip('[]') if child.text else ""
                        if topics_text:
                            metadata[child.tag] = [t.strip().strip('"\'') for t in topics_text.split(',')]
                        else:
                            metadata[child.tag] = []
                    else:
                        metadata[child.tag] = child.text if child.text else ''
                cognition['metadata'] = metadata
                logger.info(f"解析的metadata: {metadata}")
            else:
                logger.warning("未找到metadata节点")
            
            # 解析其他标签
            for tag in ['think_zh', 'think_en', 'question_zh', 'question_en', 'answer_zh', 'answer_en', 'abstract_zh', 'abstract_en', 'context_note', 'connections']:
                node = cognition_item.find(tag)
                value = node.text.strip() if node is not None and node.text else ''
                cognition[tag] = value
                logger.info(f"解析{tag}: {value[:100]}...")
            
            logger.info(f"最终解析结果: {cognition}")
            return cognition
            
        except Exception as e:
            logger.error(f"解析认知XML失败: {str(e)}")
            logger.error(f"XML内容: {xml_string}")
            # 尝试从原始文本提取内容
            try:
                return self._extract_cognition_from_text(xml_string)
            except:
                raise Exception(f"解析认知内容失败: {str(e)}")

    def _clean_xml_string(self, xml_string: str) -> str:
        """清理XML字符串，移除可能导致解析错误的字符"""
        try:
            # 移除XML声明
            if xml_string.startswith('<?xml'):
                xml_string = xml_string.split('?>', 1)[1] if '?>' in xml_string else xml_string
            
            # 移除多余的空白字符
            xml_string = xml_string.strip()
            
            # 转义特殊字符
            xml_string = xml_string.replace('&', '&amp;')
            xml_string = xml_string.replace('<', '&lt;').replace('>', '&gt;')
            
            # 恢复XML标签
            import re
            # 恢复 cognition_item 及其子标签
            xml_tags = ['cognition_item', 'metadata', 'type', 'confidence', 'primary_topic', 'related_topic', 
                       'complexity_level', 'think_zh', 'think_en', 'question_zh', 'question_en', 'answer_zh', 'answer_en', 'abstract_zh', 'abstract_en', 'context_note', 'connections']
            for tag in xml_tags:
                xml_string = xml_string.replace(f'&lt;{tag}&gt;', f'<{tag}>')
                xml_string = xml_string.replace(f'&lt;/{tag}&gt;', f'</{tag}>')
            
            return xml_string
            
        except Exception as e:
            logger.warning(f"XML清理失败: {str(e)}")
            return xml_string

    def _extract_cognition_from_text(self, text: str) -> Dict[str, Any]:
        """从普通文本中提取认知内容"""
        try:
            logger.info("尝试从文本中提取认知内容")
            
            # 简单的文本解析逻辑
            lines = text.split('\n')
            cognition = {
                'think_zh': '',
                'think_en': '',
                'question_zh': '',
                'question_en': '',
                'answer_zh': '',
                'answer_en': '',
                'abstract_zh': '',
                'abstract_en': '',
                'context_note': '',
                'connections': '',
                'metadata': {
                    'type': 'Analysis',
                    'confidence': 'Medium',
                    'primary_topic': '',
                    'related_topic': [],
                    'complexity_level': 'Moderate'
                }
            }
            
            # 尝试找到问题和答案
            current_section = None
            content_buffer = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 识别段落标题
                if any(keyword in line.lower() for keyword in ['问题', 'question', '疑问']):
                    if current_section and content_buffer:
                        cognition[current_section] = '\n'.join(content_buffer)
                    current_section = 'question_zh'
                    content_buffer = []
                elif any(keyword in line.lower() for keyword in ['答案', 'answer', '回答', '洞察', '结论']):
                    if current_section and content_buffer:
                        cognition[current_section] = '\n'.join(content_buffer)
                    current_section = 'answer_zh'
                    content_buffer = []
                elif any(keyword in line.lower() for keyword in ['思考', 'think', '分析']):
                    if current_section and content_buffer:
                        cognition[current_section] = '\n'.join(content_buffer)
                    current_section = 'think_zh'
                    content_buffer = []
                elif any(keyword in line.lower() for keyword in ['摘要', 'abstract', '总结']):
                    if current_section and content_buffer:
                        cognition[current_section] = '\n'.join(content_buffer)
                    current_section = 'abstract_zh'
                    content_buffer = []
                else:
                    if current_section:
                        content_buffer.append(line)
                    elif not cognition['answer_zh']:  # 默认作为答案
                        cognition['answer_zh'] = line
            
            # 保存最后一个section
            if current_section and content_buffer:
                cognition[current_section] = '\n'.join(content_buffer)
            
            # 如果没有问题，生成一个默认问题
            if not cognition['question_zh']:
                cognition['question_zh'] = "这个内容想要表达什么核心观点？"
            if not cognition['question_en']:
                cognition['question_en'] = "What core point does this content want to express?"
            
            # 如果没有答案，使用整个文本
            if not cognition['answer_zh']:
                cognition['answer_zh'] = text[:500]  # 限制长度
            if not cognition['answer_en']:
                cognition['answer_en'] = text[:500]  # 限制长度
            
            # 如果没有摘要，使用整个文本
            if not cognition['abstract_zh']:
                cognition['abstract_zh'] = text[:200]  # 限制长度
            if not cognition['abstract_en']:
                cognition['abstract_en'] = text[:200]  # 限制长度
                
            # 如果没有思考过程，生成默认内容
            if not cognition['think_zh']:
                cognition['think_zh'] = "从文档中提取的重要信息和观点"
            if not cognition['think_en']:
                cognition['think_en'] = "Important information and insights extracted from the document"
            
            logger.info(f"从文本提取的认知: {cognition}")
            return cognition
            
        except Exception as e:
            logger.error(f"从文本提取认知失败: {str(e)}")
            raise Exception(f"从文本提取认知失败: {str(e)}")

    async def _synthesize_cognitions(self, cognitions: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """合成多个认知为一个综合认知"""
        try:
            # 构建合成提示词
            cognition_summaries = []
            for item in cognitions:
                cog = item["cognition"]
                summary = f"文档: {item['document_title']}\n问题: {cog.get('question_zh', '')}\n答案: {cog.get('answer_zh', '')}\n"
                cognition_summaries.append(summary)
            
            synthesis_prompt = f"""请将以下多个认知合成为一个综合性的认知洞察：

{chr(10).join(cognition_summaries)}

请找出这些认知之间的共同模式、互补关系或更高层次的洞察，并按照XML格式返回合成结果（必须包含中英文双语版本）：

<cognition_item>
  <metadata>
    <type>Synthesis</type>
    <confidence>[High|Medium|Low]</confidence>
    <primary_topic>知识合成</primary_topic>
    <related_topic>["相关主题1", "相关主题2"]</related_topic>
    <complexity_level>Complex</complexity_level>
  </metadata>
  
  <think_zh>
    [说明如何从多个认知中提取出更高层次的洞察 - 中文版本]
  </think_zh>
  
  <think_en>
    [Explanation of how higher-level insights are extracted from multiple cognitions - English version]
  </think_en>
  
  <question_zh>
    [综合性问题 - 中文版本]
  </question_zh>
  
  <question_en>
    [Comprehensive question - English version]
  </question_en>
  
  <answer_zh>
    [合成的认知内容 - 中文版本]
  </answer_zh>
  
  <answer_en>
    [Synthesized cognition content - English version]
  </answer_en>
  
  <abstract_zh>
    [认知摘要，概括核心观点 - 中文版本]
  </abstract_zh>
  
  <abstract_en>
    [Cognition abstract, summarizing core viewpoints - English version]
  </abstract_en>
  
  <context_note>
    [可选背景信息]
  </context_note>
  
  <connections>
    [与源认知的关联]
  </connections>
</cognition_item>
"""
            
            ai_response = await self._call_synthesis_ai_service(synthesis_prompt)
            if ai_response and "No cognitions found" not in ai_response:
                return self._parse_cognition_xml(ai_response)
            
            return None
            
        except Exception as e:
            logger.error(f"合成认知失败: {str(e)}")
            return None

    async def _save_document_cognition(
        self, 
        user_id: str, 
        source_document_ids: List[str], 
        source_document_titles: List[str],
        cognition_data: Dict[str, Any],
        is_synthesized: bool = False,
        parent_cognition_ids: List[str] = None
    ) -> DocumentCognitionResponse:
        """保存文档认知到数据库"""
        try:
            db = get_database()
            collection = db["document_cognitions"]
            
            metadata = cognition_data.get('metadata', {})
            
            # 为认知选择合适的topic
            topic_selection = await self._select_topics_for_cognition(cognition_data)
            logger.info(f"为认知选择的话题: {topic_selection}")
            
            # 构建数据库文档
            db_doc = DocumentCognitionDB(
                user_id=user_id,
                source_document_ids=source_document_ids,
                source_document_titles=source_document_titles,
                abstract_zh=cognition_data.get('abstract_zh', ''),
                abstract_en=cognition_data.get('abstract_en', ''),
                think_zh=cognition_data.get('think_zh', ''),
                think_en=cognition_data.get('think_en', ''),
                question_zh=cognition_data.get('question_zh', ''),
                question_en=cognition_data.get('question_en', ''),
                answer_zh=cognition_data.get('answer_zh', ''),
                answer_en=cognition_data.get('answer_en', ''),
                primary_topic=topic_selection.get('primary_topic') or metadata.get('primary_topic'),
                related_topic=topic_selection.get('related_topic') or metadata.get('related_topic', []),
                cognition_type=metadata.get('type'),
                confidence_level=metadata.get('confidence'),
                complexity_level=metadata.get('complexity_level'),
                is_synthesized=is_synthesized,
                parent_cognition_ids=parent_cognition_ids or [],
                raw_at=datetime.utcnow(),  # 设置raw_at为当前时间
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # 插入数据库
            result = await collection.insert_one(db_doc.dict(by_alias=True))
            
            # 返回响应模型
            response = DocumentCognitionResponse(
                id=str(result.inserted_id),
                **db_doc.dict(exclude={'id'})
            )
            # 确保上传状态字段都被包含
            response.upload_status = db_doc.upload_status
            response.upload_requested_at = db_doc.upload_requested_at
            response.reviewed_at = db_doc.reviewed_at
            response.reviewed_by = db_doc.reviewed_by
            response.reviewer_name = db_doc.reviewer_name
            response.review_comment = db_doc.review_comment
            response.cognition_platform_id = db_doc.cognition_platform_id
            response.raw_at = db_doc.raw_at  # 确保raw_at字段被包含
            
            return response
            
        except Exception as e:
            logger.error(f"保存文档认知失败: {str(e)}")
            raise Exception(f"保存认知失败: {str(e)}")

    async def get_user_document_cognitions(
        self, 
        user_id: str, 
        skip: int = 0, 
        limit: int = 20
    ) -> DocumentCognitionListResponse:
        """获取用户的文档认知列表"""
        try:
            db = get_database()
            collection = db["document_cognitions"]
            
            # 查询条件
            query = {"user_id": user_id}
            
            # 获取总数
            total = await collection.count_documents(query)
            
            # 分页查询
            cursor = collection.find(query).sort("created_at", -1).skip(skip).limit(limit)
            items = []
            
            # 获取用户信息
            user_info = await self._get_creator_info(user_id)
            
            async for doc in cursor:
                item = DocumentCognitionResponse(
                    id=str(doc["_id"]),
                    user_id=doc["user_id"],
                    source_document_ids=doc["source_document_ids"],
                    source_document_titles=doc["source_document_titles"],
                    abstract_zh=doc["abstract_zh"],
                    abstract_en=doc.get("abstract_en"),
                    think_zh=doc["think_zh"],
                    think_en=doc.get("think_en"),
                    question_zh=doc["question_zh"],
                    question_en=doc.get("question_en"),
                    answer_zh=doc["answer_zh"],
                    answer_en=doc.get("answer_en"),
                    primary_topic=doc.get("primary_topic"),
                    related_topic=doc.get("related_topic", []),
                    cognition_type=doc.get("cognition_type"),
                    value_score=doc.get("value_score"),
                    novelty_score=doc.get("novelty_score"),
                    confidence_level=doc.get("confidence_level"),
                    temporal_relevance=doc.get("temporal_relevance"),
                    complexity_level=doc.get("complexity_level"),
                    upload_status=doc.get("upload_status", "not_uploaded"),
                    upload_requested_at=doc.get("upload_requested_at"),
                    reviewed_at=doc.get("reviewed_at"),
                    reviewed_by=doc.get("reviewed_by"),
                    reviewer_name=doc.get("reviewer_name"),
                    review_comment=doc.get("review_comment"),
                    cognition_platform_id=doc.get("cognition_platform_id"),
                    source=doc["source"],
                    extraction_method=doc["extraction_method"],
                    is_synthesized=doc["is_synthesized"],
                    parent_cognition_ids=doc.get("parent_cognition_ids", []),
                    raw_at=doc.get("raw_at", doc["created_at"]),  # 使用raw_at，fallback到created_at
                    created_at=doc["created_at"],
                    updated_at=doc["updated_at"]
                )
                
                # 添加用户名信息
                item.user_name = user_info.get("creator_name", "未知用户")
                items.append(item)
            
            pages = (total + limit - 1) // limit
            
            return DocumentCognitionListResponse(
                items=items,
                total=total,
                page=(skip // limit) + 1,
                size=limit,
                pages=pages
            )
            
        except Exception as e:
            logger.error(f"获取用户文档认知列表失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取认知列表失败"
            )

    async def get_document_cognition_detail(self, cognition_id: str, user_id: str) -> DocumentCognitionResponse:
        """获取文档认知详情"""
        try:
            db = get_database()
            collection = db["document_cognitions"]
            
            # 尝试两种查询方式：ObjectId和字符串形式的_id
            doc = await collection.find_one({
                "_id": ObjectId(cognition_id),
                "user_id": user_id
            })
            
            # 如果ObjectId查询失败，尝试字符串查询
            if not doc:
                doc = await collection.find_one({
                    "_id": cognition_id,
                    "user_id": user_id
                })
            
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="认知不存在或无权访问"
                )
            
            # 获取用户信息
            user_info = await self._get_creator_info(user_id)
            
            item = DocumentCognitionResponse(
                id=str(doc["_id"]),
                user_id=doc["user_id"],
                source_document_ids=doc["source_document_ids"],
                source_document_titles=doc["source_document_titles"],
                abstract_zh=doc["abstract_zh"],
                abstract_en=doc.get("abstract_en"),
                think_zh=doc["think_zh"],
                think_en=doc.get("think_en"),
                question_zh=doc["question_zh"],
                question_en=doc.get("question_en"),
                answer_zh=doc["answer_zh"],
                answer_en=doc.get("answer_en"),
                primary_topic=doc.get("primary_topic"),
                related_topic=doc.get("related_topic", []),
                cognition_type=doc.get("cognition_type"),
                value_score=doc.get("value_score"),
                novelty_score=doc.get("novelty_score"),
                confidence_level=doc.get("confidence_level"),
                temporal_relevance=doc.get("temporal_relevance"),
                complexity_level=doc.get("complexity_level"),
                upload_status=doc.get("upload_status", "not_uploaded"),
                upload_requested_at=doc.get("upload_requested_at"),
                reviewed_at=doc.get("reviewed_at"),
                reviewed_by=doc.get("reviewed_by"),
                reviewer_name=doc.get("reviewer_name"),
                review_comment=doc.get("review_comment"),
                cognition_platform_id=doc.get("cognition_platform_id"),
                source=doc["source"],
                extraction_method=doc["extraction_method"],
                is_synthesized=doc["is_synthesized"],
                parent_cognition_ids=doc.get("parent_cognition_ids", []),
                raw_at=doc.get("raw_at", doc["created_at"]),  # 使用raw_at，fallback到created_at
                created_at=doc["created_at"],
                updated_at=doc["updated_at"]
            )
            
            # 添加用户名信息
            item.user_name = user_info.get("creator_name", "未知用户")
            return item
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取文档认知详情失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取认知详情失败"
            )

    async def test_cognition_extraction(self, test_content: str = None) -> Dict[str, Any]:
        """测试认知提取功能"""
        try:
            if not test_content:
                test_content = """
                人工智能的发展正在经历一个关键的转折点。大语言模型的出现标志着AI从专用工具向通用智能的转变。
                这种转变的核心在于模型能够理解和生成自然语言，这是人类认知的基础能力之一。
                
                更重要的是，这些模型展现出了一种"涌现能力"——当参数量和训练数据达到一定规模时，
                模型会突然获得之前没有的能力，如推理、数学计算、代码生成等。这种现象类似于物理学中的相变，
                暗示着智能可能存在某种临界点机制。
                
                这对未来的AI发展具有深远意义：如果我们能理解这种涌现的机制，就可能找到通往通用人工智能的捷径。
                """
            
            logger.info("开始测试认知提取功能")
            result = await self._extract_single_document_cognition(
                content=test_content,
                title="AI发展的认知测试",
                doc_id="test_doc_001"
            )
            
            return {
                "test_success": result is not None,
                "result": result,
                "message": "测试成功" if result else "测试失败：未能提取到认知"
            }
            
        except Exception as e:
            logger.error(f"认知提取测试失败: {str(e)}")
            return {
                "test_success": False,
                "result": None,
                "message": f"测试失败：{str(e)}"
            }
    
    def _format_topic_tree_for_prompt(self) -> str:
        """格式化话题树用于提示词"""
        formatted_lines = []
        for primary_name, primary_data in self.topic_tree.items():
            formatted_lines.append(f"【{primary_name}】 ID: {primary_data['id']}")
            if 'children' in primary_data:
                for second_name, second_data in primary_data['children'].items():
                    formatted_lines.append(f"  - {second_name} (ID: {second_data['id']})")
                    if 'children' in second_data:
                        for third_name, third_data in second_data['children'].items():
                            formatted_lines.append(f"    * {third_name} (ID: {third_data['id']})")
        return "\n".join(formatted_lines)
    
    async def _select_topics_for_cognition(self, cognition_data: Dict[str, Any]) -> Dict[str, Any]:
        """为认知选择合适的话题"""
        try:
            # 使用AI选择话题（优化版）
            topic_selection = await self._ai_select_topics_optimized(cognition_data)
            
            if topic_selection and self._validate_topic_selection(topic_selection):
                logger.info(f"AI选择的话题: {topic_selection}")
                return topic_selection
            else:
                logger.warning(f"AI话题选择无效或失败，使用默认话题")
                
            # 如果AI选择失败，返回默认LLMOps话题
            default_topics = {
                "primary_topic": "LLMOps",  
                "related_topic": ["Data Engineering", "Pretrain"]  
            }
            logger.info(f"使用默认话题: {default_topics}")
            return default_topics
            
        except Exception as e:
            logger.error(f"选择话题失败: {str(e)}")
            return {
                "primary_topic": "0e551daa7d757246ba906364d6f23984", 
                "related_topics": ["a4f0b0e16e4c0f67fc96e0753a90153e", "a3bc9faed5d3c00e093252f8e82df5b0"]
            }

    async def _ai_select_topics_optimized(self, cognition_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用AI选择话题（优化版）"""
        try:
            # 构建简洁的话题树展示
            topic_display = self._build_simple_topic_display()
            
            # 构建简洁有效的提示词
            prompt = f"""请为以下认知内容选择合适的话题分类。

认知内容：
问题：{cognition_data.get('question_zh', '')}
答案：{cognition_data.get('answer_zh', '')}

可选话题分类：
{topic_display}

请选择：
1. 一个主要分类（从Architecture、LLMOps、Reasoning、Agent、System Efficiency、RL Scaling、Safety & Security中选择）
2. 该主要分类下的两个子话题

直接返回JSON格式：
{{"primary": "主要分类名称", "related": ["子话题1", "子话题2"]}}

例如：
{{"primary": "LLMOps", "related": ["Data Engineering", "Pretrain"]}}"""

            # 调用AI服务
            ai_response = await self._call_ai_service(prompt)
            
            if ai_response:
                # 解析响应并转换为ID格式
                topic_selection = self._parse_simple_topic_response(ai_response)
                if topic_selection:
                    return topic_selection
            
            return None
            
        except Exception as e:
            logger.error(f"AI话题选择失败: {str(e)}")
            return None

    def _build_simple_topic_display(self) -> str:
        """构建简洁的话题展示"""
        display = """
Architecture:
  - sequence modeling paradigm (sparse attention, linear attention, hybrid attention)
  - architecture enhancement (long context, mixture-of-experts)
  - multimodal architecture (auto-regressive multimodal, diffusion-based multimodal, transfusion-based multimodal)

LLMOps:
  - Data Engineering (Human Annotation, Data Filtering, Data Synthesis, Cognition Engineering, Human-AI Collabration)
  - Pretrain (Pretraining Recipe, Scaling Law)
  - Mid-train (continue pretraining, long-context training)
  - Post-train (supervised fine-tuning, reinforcement learning, self-iterative learning)
  - Inference-time (Prompt Engineering, Context Engineering)
  - Evaluation (Benchmark, Evaluation Metric)

Reasoning:
  - long thought training
  - reinforcement learning scaling
  - efficient reasoning

Agent:
  - benchmark (agent environment, agent reward)
  - agentic architecture (tool use, multi-agent, human-agent interaction, memory management)
  - training (agentic RL, agentic SFT)

System Efficiency:
  - Efficient Training (distributed training, RL training infra)
  - Efficient Inference (quantization and pruning, knowledge distillation, speculative-decoding, kv-cache management)

RL Scaling:
  - code start
  - RL data selection
  - reward model
  - entropy control

Safety & Security:
  - safety alignment
  - Privacy
  - Explanation
  - Emerging Risks
"""
        return display.strip()

    def _parse_simple_topic_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析简化的AI话题选择响应，直接返回名称"""
        try:
            import json
            import re
            
            logger.info(f"AI话题选择响应: {response}")
            
            # 先尝试解析JSON
            parsed_response = None
            
            # 方法1：直接解析
            try:
                parsed_response = json.loads(response.strip())
            except:
                pass
            
            # 方法2：查找JSON结构
            if not parsed_response:
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    try:
                        json_str = response[json_start:json_end]
                        parsed_response = json.loads(json_str)
                    except:
                        pass
            
            # 方法3：修复常见格式问题
            if not parsed_response:
                try:
                    cleaned = response.replace('```json', '').replace('```', '').strip()
                    cleaned = cleaned.replace("'", '"')
                    parsed_response = json.loads(cleaned)
                except:
                    pass
            
            if not parsed_response:
                logger.warning(f"无法解析AI响应: {response}")
                return None
            
            # 获取AI选择的话题名称
            primary_name = parsed_response.get("primary")
            related_names = parsed_response.get("related", [])
            
            if not primary_name or not related_names or len(related_names) != 2:
                logger.warning(f"AI话题选择格式不正确: primary={primary_name}, related={related_names}")
                return None
            
            # 验证选择的话题是否在话题树中
            if not self._validate_topic_names(primary_name, related_names):
                logger.warning(f"选择的话题不在话题树中: primary={primary_name}, related={related_names}")
                return None
            
            # 直接返回名称，不转换为ID
            result = {
                "primary_topic": primary_name,
                "related_topic": related_names
            }
            
            logger.info(f"话题选择成功: {result}")
            return result
            
        except Exception as e:
            logger.error(f"解析简化话题响应失败: {str(e)}")
            return None

    def _validate_topic_names(self, primary_name: str, related_names: List[str]) -> bool:
        """验证话题名称是否在话题树中"""
        try:
            # 验证主要话题是否存在
            primary_found = False
            for name, data in self.topic_tree.items():
                if name.lower() == primary_name.lower():
                    primary_found = True
                    break
            
            if not primary_found:
                logger.warning(f"主要话题不存在: {primary_name}")
                return False
            
            # 验证相关话题是否都存在
            for related_name in related_names:
                found = False
                for primary_tree_name, primary_data in self.topic_tree.items():
                    if 'children' in primary_data:
                        if self._find_topic_name_in_tree(primary_data['children'], related_name):
                            found = True
                            break
                if not found:
                    logger.warning(f"相关话题不存在: {related_name}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证话题名称失败: {str(e)}")
            return False

    def _find_topic_name_in_tree(self, children_dict: Dict[str, Any], target_name: str) -> bool:
        """在话题树的子节点中查找指定名称"""
        for child_name, child_data in children_dict.items():
            if child_name.lower() == target_name.lower():
                return True
            # 如果有子子话题，递归查找
            if 'children' in child_data:
                if self._find_topic_name_in_tree(child_data['children'], target_name):
                    return True
        return False

    def _validate_topic_selection(self, topic_selection: Dict[str, Any]) -> bool:
        """验证话题选择是否有效（兼容性方法）"""
        # 使用严格验证替代原来的验证
        return self._strict_validate_topic_selection(topic_selection)

    def _find_topic_in_tree(self, tree_node: Dict[str, Any], target_id: str) -> bool:
        """在话题树中查找指定ID"""
        if tree_node.get('id') == target_id:
            return True
        
        if 'children' in tree_node:
            for child_name, child_data in tree_node['children'].items():
                if self._find_topic_in_tree(child_data, target_id):
                    return True
        
        return False

    def _strict_validate_topic_selection(self, topic_selection: Dict[str, Any]) -> bool:
        """严格验证话题选择是否完全符合话题树"""
        try:
            primary_topic = topic_selection.get("primary_topic")
            related_topic = topic_selection.get("related_topic", [])
            
            # 验证primary_topic是否是顶级分类
            primary_found = False
            primary_tree = None
            for primary_name, primary_data in self.topic_tree.items():
                if primary_data['id'] == primary_topic:
                    primary_found = True
                    primary_tree = primary_data
                    break
            
            if not primary_found:
                logger.warning(f"Primary topic不是顶级分类: {primary_topic}")
                return False
            
            # 验证related_topic数量
            if len(related_topic) != 2:
                logger.warning(f"Related topic数量不对: {len(related_topic)}, 应该是2个")
                return False
            
            # 验证related_topic是否都属于该primary_topic下
            for topic in related_topic:
                if not self._find_topic_in_tree(primary_tree, topic):
                    logger.warning(f"Related topic {topic} 不属于 primary topic {primary_topic}")
                    return False
            
            logger.info(f"话题选择验证通过: primary={primary_topic}, related={related_topic}")
            return True
            
        except Exception as e:
            logger.error(f"严格验证话题选择失败: {str(e)}")
            return False

    async def upload_cognition_to_platform(self, cognition_id: str, user_id: str) -> Dict[str, Any]:
        """上传认知到认知平台"""
        try:
            # 获取认知详情
            db = get_database()
            collection = db["document_cognitions"]
            
            # 尝试两种查询方式：ObjectId和字符串形式的_id
            cognition = await collection.find_one({
                "_id": ObjectId(cognition_id),
                "user_id": user_id
            })
            
            # 如果ObjectId查询失败，尝试字符串查询
            if not cognition:
                cognition = await collection.find_one({
                    "_id": cognition_id,
                    "user_id": user_id
                })
            
            if not cognition:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="认知不存在或无权访问"
                )
            
            # 检查是否已经上传过
            if cognition.get("upload_status") != "not_uploaded":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"认知已处于{cognition.get('upload_status')}状态，无法重复上传"
                )
            
            # 更新状态为等待审核 - 同样需要兼容两种ID格式
            now = datetime.utcnow()
            
            # 先尝试ObjectId更新
            update_result = await collection.update_one(
                {"_id": ObjectId(cognition_id)},
                {
                    "$set": {
                        "upload_status": "pending_review",
                        "upload_requested_at": now,
                        "updated_at": now
                    }
                }
            )
            
            # 如果ObjectId更新失败，尝试字符串更新
            if update_result.modified_count == 0:
                update_result = await collection.update_one(
                    {"_id": cognition_id},
                    {
                        "$set": {
                            "upload_status": "pending_review",
                            "upload_requested_at": now,
                            "updated_at": now
                        }
                    }
                )
            
            if update_result.modified_count == 0:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="更新认知状态失败"
                )
            
            return {
                "success": True,
                "message": "认知已提交审核，请等待管理员审核",
                "upload_status": "pending_review",
                "upload_requested_at": now
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"上传认知到平台失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"上传认知失败: {str(e)}"
            )

    async def get_pending_cognitions_for_review(
        self, 
        skip: int = 0, 
        limit: int = 20
    ) -> Dict[str, Any]:
        """获取待审核的认知列表（管理员使用）"""
        try:
            db = get_database()
            collection = db["document_cognitions"]
            
            # 查询条件：状态为pending_review
            query = {"upload_status": "pending_review"}
            
            # 获取总数
            total = await collection.count_documents(query)
            
            # 分页查询
            cursor = collection.find(query).sort("upload_requested_at", 1).skip(skip).limit(limit)
            items = []
            
            # 收集所有用户ID以便批量获取用户信息
            user_ids = set()
            cognition_docs = []
            
            async for doc in cursor:
                cognition_docs.append(doc)
                user_ids.add(doc["user_id"])
            
            # 批量获取用户信息
            user_info_cache = {}
            for user_id in user_ids:
                user_info = await self._get_creator_info(user_id)
                user_info_cache[user_id] = user_info.get("creator_name", "未知用户")
            
            # 构建响应
            for doc in cognition_docs:
                item = DocumentCognitionResponse(
                    id=str(doc["_id"]),
                    user_id=doc["user_id"],
                    source_document_ids=doc["source_document_ids"],
                    source_document_titles=doc["source_document_titles"],
                    abstract_zh=doc["abstract_zh"],
                    abstract_en=doc.get("abstract_en"),
                    think_zh=doc["think_zh"],
                    think_en=doc.get("think_en"),
                    question_zh=doc["question_zh"],
                    question_en=doc.get("question_en"),
                    answer_zh=doc["answer_zh"],
                    answer_en=doc.get("answer_en"),
                    primary_topic=doc.get("primary_topic"),
                    related_topic=doc.get("related_topic", []),
                    cognition_type=doc.get("cognition_type"),
                    value_score=doc.get("value_score"),
                    novelty_score=doc.get("novelty_score"),
                    confidence_level=doc.get("confidence_level"),
                    temporal_relevance=doc.get("temporal_relevance"),
                    complexity_level=doc.get("complexity_level"),
                    upload_status=doc.get("upload_status", "not_uploaded"),
                    upload_requested_at=doc.get("upload_requested_at"),
                    reviewed_at=doc.get("reviewed_at"),
                    reviewed_by=doc.get("reviewed_by"),
                    reviewer_name=doc.get("reviewer_name"),
                    review_comment=doc.get("review_comment"),
                    cognition_platform_id=doc.get("cognition_platform_id"),
                    source=doc["source"],
                    extraction_method=doc["extraction_method"],
                    is_synthesized=doc["is_synthesized"],
                    parent_cognition_ids=doc.get("parent_cognition_ids", []),
                    raw_at=doc.get("raw_at", doc["created_at"]),  # 使用raw_at，fallback到created_at
                    created_at=doc["created_at"],
                    updated_at=doc["updated_at"]
                )
                
                # 添加用户名信息
                item.user_name = user_info_cache.get(doc["user_id"], "未知用户")
                items.append(item)
            
            return {
                "items": items,
                "total": total,
                "page": (skip // limit) + 1,
                "size": limit,
                "pages": (total + limit - 1) // limit
            }
            
        except Exception as e:
            logger.error(f"获取待审核认知列表失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取待审核认知列表失败"
            )

    async def review_cognition(
        self, 
        cognition_id: str, 
        reviewer_id: str,
        reviewer_name: str,
        action: str, 
        comment: str = None
    ) -> Dict[str, Any]:
        """审核认知（管理员使用）"""
        try:
            if action not in ["approve", "reject"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的审核操作，必须是 approve 或 reject"
                )
            
            db = get_database()
            collection = db["document_cognitions"]
            
            # 尝试两种查询方式：ObjectId和字符串形式的_id
            cognition = await collection.find_one({
                "_id": ObjectId(cognition_id),
                "upload_status": "pending_review"
            })
            
            # 如果ObjectId查询失败，尝试字符串查询
            if not cognition:
                cognition = await collection.find_one({
                    "_id": cognition_id,
                    "upload_status": "pending_review"
                })
            
            if not cognition:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="认知不存在或不在待审核状态"
                )
            
            now = datetime.utcnow()
            
            if action == "approve":
                # 审核通过，将认知添加到认知平台
                platform_id = await self._add_to_cognition_platform(cognition)
                
                # 更新认知状态
                update_data = {
                    "upload_status": "approved",
                    "reviewed_at": now,
                    "reviewed_by": reviewer_id,
                    "reviewer_name": reviewer_name,
                    "review_comment": comment or "审核通过",
                    "cognition_platform_id": platform_id,
                    "updated_at": now
                }
            else:
                # 审核不通过
                update_data = {
                    "upload_status": "rejected",
                    "reviewed_at": now,
                    "reviewed_by": reviewer_id,
                    "reviewer_name": reviewer_name,
                    "review_comment": comment or "审核不通过",
                    "updated_at": now
                }
            
            # 更新数据库 - 同样需要兼容两种ID格式
            # 先尝试ObjectId更新
            update_result = await collection.update_one(
                {"_id": ObjectId(cognition_id)},
                {"$set": update_data}
            )
            
            # 如果ObjectId更新失败，尝试字符串更新
            if update_result.modified_count == 0:
                update_result = await collection.update_one(
                    {"_id": cognition_id},
                    {"$set": update_data}
                )
            
            if update_result.modified_count == 0:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="更新认知状态失败"
                )
            
            return {
                "success": True,
                "message": f"认知审核{('通过' if action == 'approve' else '不通过')}",
                "action": action,
                "cognition_platform_id": update_data.get("cognition_platform_id")
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"审核认知失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"审核认知失败: {str(e)}"
            )

    async def _add_to_cognition_platform(self, cognition: Dict[str, Any]) -> str:
        """将认知添加到认知平台"""
        try:
            # 直接创建CognitionDatabase实例而不使用依赖注入
            from backend.cognition.database import CognitionDatabase
            
            cognition_db = CognitionDatabase()
            
            # 获取用户信息
            user_info = await self._get_creator_info(cognition["user_id"])
            author_name = user_info.get("creator_name", "未知用户")
            
            # 构建认知平台数据，保持原始数据结构
            platform_data = {
                "abstract_zh": cognition["abstract_zh"],
                "abstract_en": cognition.get("abstract_en"),
                "think_zh": cognition["think_zh"],
                "think_en": cognition.get("think_en"),
                "question_zh": cognition["question_zh"],
                "question_en": cognition.get("question_en"),
                "answer_zh": cognition["answer_zh"],
                "answer_en": cognition.get("answer_en"),
                "source": "笔记提取",  # 简化来源信息，只保留"笔记提取"
                "author_id": cognition["user_id"],
                "author_name": author_name,
                "primary_topic": cognition.get("primary_topic"),
                "related_topic": cognition.get("related_topic", []),
                "tag": "笔记",  # 设置为"笔记"标签
                "blogger": None,
                "link": None,
                "content": None,
                "cover_url": None,
                # 保持原始的时间字段，确保raw_at字段被正确传递
                "raw_at": cognition.get("raw_at"),  # 保持原始创建时间
                "created_at": cognition.get("created_at"),  # 保持原始创建时间
                # 其他可能的字段也保持原样
                "cognition_type": cognition.get("cognition_type"),
                "value_score": cognition.get("value_score"),
                "novelty_score": cognition.get("novelty_score"),
                "confidence_level": cognition.get("confidence_level"),
                "temporal_relevance": cognition.get("temporal_relevance"),
                "complexity_level": cognition.get("complexity_level"),
                "extraction_method": cognition.get("extraction_method"),
                "is_synthesized": cognition.get("is_synthesized", False),
                "parent_cognition_ids": cognition.get("parent_cognition_ids", [])
            }
            
            logger.info(f"准备插入认知平台数据: {platform_data}")
            
            # 创建认知
            created_cognition = await cognition_db.create_cognition(platform_data)
            
            logger.info(f"成功创建认知平台记录: {created_cognition}")
            
            # 返回正确的ID
            cognition_id = str(created_cognition.get("id") or created_cognition.get("_id"))
            logger.info(f"返回认知平台ID: {cognition_id}")
            
            return cognition_id
            
        except Exception as e:
            logger.error(f"添加认知到平台失败: {str(e)}")
            logger.error(f"认知数据: {cognition}")
            raise Exception(f"添加到认知平台失败: {str(e)}")

def get_database():
    """获取数据库连接"""
    return mongo_manager.db