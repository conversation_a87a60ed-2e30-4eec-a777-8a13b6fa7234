from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from backend.db.mongodb import PyObjectId
from backend.utils import get_cn_time

class Document(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    title: str
    creator_id: str
    parent_id: Optional[str] = None  # 父节点ID，None表示根节点
    content: str = ""
    owner_id: str
    collaborators: List[str] = []
    permissions: Dict[str, List[str]] = {
        "read": [],
        "write": [],
        "admin": []
    }
    shared: bool = False  # 分享状态标识
    share_token: Optional[str] = None  # 当前有效的分享token
    is_folder_share: bool = False  # 是否为文件夹分享
    created_at: datetime = Field(default_factory=get_cn_time)
    updated_at: datetime = Field(default_factory=get_cn_time)
    updated_by: Optional[str] = None  # 最后修改者ID
    updated_by_name: Optional[str] = None  # 最后修改者姓名
    # 原始文档信息（用于从分享链接复制的文档）
    original_document_id: Optional[str] = None  # 原始文档ID
    original_owner_id: Optional[str] = None  # 原始文档拥有者ID
    original_owner_name: Optional[str] = None  # 原始文档拥有者姓名
    copied_from_share: bool = False  # 是否从分享链接复制
    tags: List[str] = []
    metadata: Dict[str, Any] = {}
    children: Optional[List[str]] = Field(default_factory=list)

class DocumentCreate(BaseModel):
    title: str
    parent_id: Optional[str] = None
    content: Optional[str] = ""
    tags: Optional[List[str]] = []
    metadata: Optional[Dict[str, Any]] = {}

class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    parent_id: Optional[str] = None
    updated_by: Optional[str] = None  # 最后修改者ID
    updated_by_name: Optional[str] = None  # 最后修改者姓名
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class DocumentTreeNode(BaseModel):
    id: str  # 改为字符串类型
    title: str  # 改为 title 而不是 name，与前端保持一致
    parent_id: Optional[str] = None
    has_children: bool = False  # 添加是否有子节点的标识
    created_at: Optional[datetime] = None  # 改为可选
    updated_at: Optional[datetime] = None  # 改为可选

class DocumentResponse(BaseModel):
    id: PyObjectId
    title: str
    parent_id: Optional[str] = None
    content: str
    owner_id: str
    creator_id: str
    creator_name: Optional[str] = None  # 创建者姓名
    creator_email: Optional[str] = None  # 创建者邮箱
    collaborators: List[str]
    permissions: Dict[str, List[str]]
    shared: Optional[bool] = False  # 分享状态标识
    is_folder_share: Optional[bool] = False  # 是否为文件夹分享
    created_at: datetime
    updated_at: datetime
    updated_by: Optional[str] = None  # 最后修改者ID
    updated_by_name: Optional[str] = None  # 最后修改者姓名
    # 原始文档信息
    original_document_id: Optional[str] = None
    original_owner_id: Optional[str] = None
    original_owner_name: Optional[str] = None
    copied_from_share: Optional[bool] = False
    tags: List[str]
    metadata: Dict[str, Any]
    children: Optional[List[str]] = []
    share_permissions: Optional[List[str]] = None

# 文档认知相关模型
class DocumentCognitionCreate(BaseModel):
    """创建文档认知的请求模型"""
    source_document_ids: List[str] = Field(..., description="源文档ID列表")
    cognition_data: Dict[str, Any] = Field(..., description="认知数据")

class DocumentCognitionDB(BaseModel):
    """文档认知数据库模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: str = Field(..., description="用户ID")
    source_document_ids: List[str] = Field(..., description="源文档ID列表")
    source_document_titles: List[str] = Field(default_factory=list, description="源文档标题列表")
    
    # 认知内容字段
    abstract_zh: str = Field(..., description="中文认知摘要")
    abstract_en: Optional[str] = Field(None, description="英文认知摘要")
    think_zh: str = Field(..., description="中文思考过程")
    think_en: Optional[str] = Field(None, description="英文思考过程")
    question_zh: str = Field(..., description="中文问题")
    question_en: Optional[str] = Field(None, description="英文问题")
    answer_zh: str = Field(..., description="中文答案")
    answer_en: Optional[str] = Field(None, description="英文答案")
    
    # 元数据
    primary_topic: Optional[str] = Field(None, description="主要话题")
    related_topic: List[str] = Field(default_factory=list, description="相关话题列表")
    cognition_type: Optional[str] = Field(None, description="认知类型")
    value_score: Optional[int] = Field(None, description="价值评分")
    novelty_score: Optional[int] = Field(None, description="新颖度评分")
    confidence_level: Optional[str] = Field(None, description="置信度")
    temporal_relevance: Optional[str] = Field(None, description="时间相关性")
    complexity_level: Optional[str] = Field(None, description="复杂度级别")
    
    # 上传状态相关字段
    upload_status: str = Field(default="not_uploaded", description="上传状态: not_uploaded, pending_review, approved, rejected")
    upload_requested_at: Optional[datetime] = Field(None, description="上传申请时间")
    reviewed_at: Optional[datetime] = Field(None, description="审核时间")
    reviewed_by: Optional[str] = Field(None, description="审核者ID")
    reviewer_name: Optional[str] = Field(None, description="审核者姓名")
    review_comment: Optional[str] = Field(None, description="审核意见")
    cognition_platform_id: Optional[str] = Field(None, description="认知平台中的ID（审核通过后）")
    
    # 其他字段
    source: str = Field(default="Document Extraction", description="来源")
    extraction_method: str = Field(default="AI_EXTRACTION", description="提取方法")
    is_synthesized: bool = Field(default=False, description="是否为合成认知")
    parent_cognition_ids: List[str] = Field(default_factory=list, description="父认知ID列表")
    raw_at: datetime = Field(default_factory=datetime.utcnow, description="原始创建时间")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            PyObjectId: str
        }

class DocumentCognitionResponse(BaseModel):
    """文档认知响应模型"""
    id: str
    user_id: str
    user_name: Optional[str] = Field(None, description="用户名")
    source_document_ids: List[str]
    source_document_titles: List[str]
    abstract_zh: str
    abstract_en: Optional[str] = None
    think_zh: str
    think_en: Optional[str] = None
    question_zh: str
    question_en: Optional[str] = None
    answer_zh: str
    answer_en: Optional[str] = None
    primary_topic: Optional[str] = None
    related_topic: List[str] = Field(default_factory=list)
    cognition_type: Optional[str] = None
    value_score: Optional[int] = None
    novelty_score: Optional[int] = None
    confidence_level: Optional[str] = None
    temporal_relevance: Optional[str] = None
    complexity_level: Optional[str] = None
    
    # 上传状态相关字段
    upload_status: str = Field(default="not_uploaded")
    upload_requested_at: Optional[datetime] = None
    reviewed_at: Optional[datetime] = None
    reviewed_by: Optional[str] = None
    reviewer_name: Optional[str] = None
    review_comment: Optional[str] = None
    cognition_platform_id: Optional[str] = None
    
    source: str
    extraction_method: str
    is_synthesized: bool
    parent_cognition_ids: List[str] = Field(default_factory=list)
    raw_at: datetime
    created_at: datetime
    updated_at: datetime

class ExtractCognitionRequest(BaseModel):
    """提取认知请求模型"""
    document_ids: List[str] = Field(..., min_items=1, max_items=5, description="文档ID列表，最多5个")
    
class DocumentCognitionListResponse(BaseModel):
    """文档认知列表响应模型"""
    items: List[DocumentCognitionResponse]
    total: int
    page: int
    size: int
    pages: int