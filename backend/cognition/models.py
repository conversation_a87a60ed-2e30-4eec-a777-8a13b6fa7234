from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from backend.db.mongodb import PyObjectId

class CognitionComment(BaseModel):
    """认知评论模型"""
    id: str = Field(default_factory=lambda: str(PyObjectId()))
    user_id: str
    username: str
    content: str
    created_at: datetime
    
class CognitionVote(BaseModel):
    """认知投票模型"""
    user_id: str
    vote_type: str  # 'like', 'neutral', 'dislike'
    created_at: datetime

class CognitionCreate(BaseModel):
    """创建认知的请求模型"""
    abstract_zh: str = Field(..., description="中文认知摘要")
    abstract_en: Optional[str] = Field(None, description="英文认知摘要")
    think_zh: str = Field(..., description="中文思考过程")
    think_en: Optional[str] = Field(None, description="英文思考过程")
    question_zh: str = Field(..., description="中文问题")
    question_en: Optional[str] = Field(None, description="英文问题")
    answer_zh: str = Field(..., description="中文答案")
    answer_en: Optional[str] = Field(None, description="英文答案")
    source: str = Field(..., description="来源")
    blogger: Optional[str] = Field(None, description="博主")
    link: Optional[str] = Field(None, description="链接")
    content: Optional[str] = Field(None, description="内容")
    primary_topic: Optional[str] = Field(None, description="主要话题")
    related_topics: Optional[List[str]] = Field(default_factory=list, description="相关话题列表")
    tag: Optional[str] = Field(None, description="标签：验证或猜想")
    cover_url: Optional[str] = Field(None, max_length=500, description="封面图片URL")
    # Synthesis fields
    source_analysis: Optional[str] = Field(None, description="来源分析")
    contradictions: Optional[str] = Field(None, description="矛盾分析")
    synthesis_reasoning: Optional[str] = Field(None, description="合成理由")
    source_cognition_ids: Optional[List[str]] = Field(default_factory=list, description="来源认知ID列表")

class CognitionUpdate(BaseModel):
    """更新认知的请求模型"""
    abstract_zh: Optional[str] = None
    abstract_en: Optional[str] = None
    think_zh: Optional[str] = None
    think_en: Optional[str] = None
    question_zh: Optional[str] = None
    question_en: Optional[str] = None
    answer_zh: Optional[str] = None
    answer_en: Optional[str] = None
    source: Optional[str] = None
    blogger: Optional[str] = None
    link: Optional[str] = None
    content: Optional[str] = None
    primary_topic: Optional[str] = None
    related_topics: Optional[List[str]] = None
    tag: Optional[str] = None
    cover_url: Optional[str] = Field(None, max_length=500, description="封面图片URL")

class CognitionDB(BaseModel):
    """认知数据库模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    abstract_zh: str = Field(..., description="中文认知摘要")
    abstract_en: Optional[str] = Field(None, description="英文认知摘要")
    think_zh: str = Field(..., description="中文思考过程")
    think_en: Optional[str] = Field(None, description="英文思考过程")
    question_zh: str = Field(..., description="中文问题")
    question_en: Optional[str] = Field(None, description="英文问题")
    answer_zh: str = Field(..., description="中文答案")
    answer_en: Optional[str] = Field(None, description="英文答案")
    source: str = Field(..., description="来源")
    author_id: str = Field(..., description="作者ID")
    author_name: str = Field(..., description="作者名称")
    blogger: Optional[str] = Field(None, description="博主")
    link: Optional[str] = Field(None, description="链接")
    content: Optional[str] = Field(None, description="内容")
    primary_topic: Optional[str] = Field(None, description="主要话题")
    related_topics: List[str] = Field(default_factory=list, description="相关话题列表")
    tag: Optional[str] = Field(None, description="标签：验证或猜想")
    cover_url: Optional[str] = Field(None, max_length=500, description="封面图片URL")
    like_count: int = Field(default=0, description="点赞数")
    neutral_count: int = Field(default=0, description="中立数")
    dislike_count: int = Field(default=0, description="点踩数")
    comments: List[CognitionComment] = Field(default_factory=list, description="评论列表")
    votes: List[CognitionVote] = Field(default_factory=list, description="投票记录")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    raw_at: Optional[datetime] = Field(None, description="原始创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            PyObjectId: str
        }

class SupabaseCognitionResponse(BaseModel):
    """Supabase认知响应模型"""
    id: str
    abstract_zh: Optional[str] = None
    abstract_en: Optional[str] = None
    think_zh: Optional[str] = None
    think_en: Optional[str] = None
    question_zh: Optional[str] = None
    question_en: Optional[str] = None
    answer_zh: Optional[str] = None
    answer_en: Optional[str] = None
    source: Optional[str] = None
    author_id: Optional[str] = None
    author_name: Optional[str] = None
    blogger: Optional[str] = None
    link: Optional[str] = None
    content: Optional[str] = None
    primary_topic: Optional[str] = None
    related_topics: List[str] = Field(default_factory=list, description="相关话题列表")
    tag: Optional[str] = Field(None, description="标签：验证或猜想")
    cover_url: Optional[str] = Field(None, max_length=500, description="封面图片URL")
    likes: int = Field(default=0, description="点赞数")
    neutral: int = Field(default=0, description="中立数")
    dislikes: int = Field(default=0, description="点踩数")
    comments: List[Dict[str, Any]] = Field(default_factory=list, description="评论列表")
    votes: List[Dict[str, Any]] = Field(default_factory=list, description="投票记录")
    created_at: Optional[datetime] = None
    raw_at: Optional[datetime] = None  # 新增字段，表示原始创建时间
    updated_at: Optional[datetime] = None
    user_vote: Optional[str] = None  # 当前用户的投票状态
    favorite_status: Optional[Dict[str, Any]] = None  # 当前用户的收藏状态
    read_status: Optional[Dict[str, Any]] = None  # 当前用户的已读状态
    source_analysis: Optional[str] = None
    contradictions: Optional[str] = None
    synthesis_reasoning: Optional[str] = None
    source_cognition_ids: Optional[List[str]] = None
    
    # 为了兼容性，提供旧版本字段的动态属性
    @property
    def abstract(self) -> str:
        """动态返回合适语言的摘要"""
        return self.abstract_zh or self.abstract_en or ""
    
    @property
    def think(self) -> str:
        """动态返回合适语言的思考过程"""
        return self.think_zh or self.think_en or ""
        
    @property
    def question(self) -> str:
        """动态返回合适语言的问题"""
        return self.question_zh or self.question_en or ""
        
    @property
    def answer(self) -> str:
        """动态返回合适语言的答案"""
        return self.answer_zh or self.answer_en or ""

class CognitionResponse(BaseModel):
    """认知响应模型（兼容旧版本）"""
    id: str
    abstract: str
    think: str
    question: str
    answer: str
    source: str
    author_id: str
    author_name: str
    blogger: Optional[str] = None
    link: Optional[str] = None
    content: Optional[str] = None
    like_count: int
    neutral_count: int
    dislike_count: int
    comments: List[CognitionComment]
    created_at: datetime
    updated_at: datetime
    user_vote: Optional[str] = None  # 当前用户的投票状态

class CommentCreate(BaseModel):
    """评论创建模型"""
    content: str = Field(..., min_length=1, max_length=500, description="评论内容")
    parent_id: Optional[str] = Field(None, description="父评论ID，用于回复")
    reply_to_username: Optional[str] = Field(None, description="被回复的用户名")

class VoteRequest(BaseModel):
    """投票请求模型"""
    vote_type: Optional[str] = Field(..., pattern="^(like|neutral|dislike|cancel)$", description="投票类型，cancel表示取消投票") 

# 收藏相关模型
class CollectionCreate(BaseModel):
    """创建收藏夹请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="收藏夹名称")
    description: Optional[str] = Field(None, max_length=500, description="收藏夹描述")

# 已读状态相关模型
class ReadStatusRequest(BaseModel):
    """已读状态切换请求模型"""
    is_read: bool = Field(..., description="是否已读")

class ReadStatusResponse(BaseModel):
    """已读状态响应模型"""
    is_read: bool = Field(False, description="是否已读")
    read_at: Optional[datetime] = Field(None, description="已读时间")

class CognitionReadStatus(BaseModel):
    """认知已读状态数据库模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: str = Field(..., description="用户ID")
    cognition_id: str = Field(..., description="认知ID")
    is_read: bool = Field(True, description="是否已读")
    read_at: datetime = Field(default_factory=datetime.utcnow, description="已读时间")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            PyObjectId: str
        }

class CollectionResponse(BaseModel):
    """收藏夹响应模型"""
    id: str
    user_id: str
    name: str
    description: Optional[str] = None
    is_default: bool = False
    created_at: datetime
    updated_at: datetime
    cognition_count: Optional[int] = 0  # 收藏夹中认知数量

class FavoriteRequest(BaseModel):
    """收藏请求模型"""
    collection_id: str = Field(..., description="收藏夹ID")

class FavoriteStatusResponse(BaseModel):
    """收藏状态响应模型"""
    is_favorited: bool = False
    collections: List[str] = Field(default_factory=list, description="收藏到的收藏夹ID列表")

class PaginatedCollectionResponse(BaseModel):
    """分页收藏夹响应模型"""
    items: List[CollectionResponse]
    total: int
    page: int
    pages: int
    page_size: int

class CollectionCognitionsResponse(BaseModel):
    """收藏夹认知列表响应模型"""
    collection: CollectionResponse
    items: List[SupabaseCognitionResponse]
    total: int
    page: int
    pages: int
    page_size: int 