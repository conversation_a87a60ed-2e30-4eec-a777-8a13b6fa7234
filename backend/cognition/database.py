from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from bson import ObjectId
from motor.motor_asyncio import Async<PERSON>MotorCollection
from fastapi import Depends
import logging
import time
from functools import wraps
import uuid
import asyncio

from backend.db.mongodb import MongoDBBase, get_cn_time, PyObjectId
from backend.db.dependencies import get_mongodb_collection
from backend.cognition.models import (
    CognitionDB, CognitionResponse, CognitionComment, CognitionVote,
    SupabaseCognitionResponse, CollectionResponse, FavoriteStatusResponse
)
from backend.cognition.vote_database import VoteDatabase
from backend.cognition.vote_models import VoteStats

logger = logging.getLogger(__name__)

def monitor_performance(func_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                if duration > 1.0:  # 记录超过1秒的查询
                    logger.warning(f"慢查询警告: {func_name} 耗时 {duration:.2f}秒")
                elif duration > 0.5:  # 记录超过0.5秒的查询
                    logger.info(f"查询性能: {func_name} 耗时 {duration:.2f}秒")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"查询失败: {func_name} 耗时 {duration:.2f}秒, 错误: {str(e)}")
                raise
        return wrapper
    return decorator

class CognitionDatabase(MongoDBBase[CognitionDB]):
    """认知平台数据库操作类"""
    
    def __init__(self, collection: Optional[AsyncIOMotorCollection] = None):
        if collection is not None:
            super().__init__(collection_name="cognitions_new", model_class=CognitionDB, injected_collection=collection)
            # 使用同一个数据库实例创建投票数据库
            self.vote_db = VoteDatabase(collection.database)
        else:
            super().__init__(collection_name="cognitions_new", model_class=CognitionDB)
            # 使用默认数据库创建投票数据库
            from backend.db.mongodb import mongo_manager
            database = mongo_manager.db
            self.vote_db = VoteDatabase(database)
        
        # 添加简单的内存缓存
        self._vote_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
        
        # 确保索引存在
        self._ensure_indexes()
    
    def _ensure_indexes(self):
        """确保必要的索引存在"""
        try:
            # 创建后台任务来设置索引
            asyncio.create_task(self._create_indexes())
        except Exception as e:
            logger.warning(f"创建索引任务失败: {str(e)}")
    
    async def _create_indexes(self):
        """创建数据库索引"""
        try:
            # 等待一下确保集合已创建
            await asyncio.sleep(1)
            
            # 创建创建时间索引
            try:
                await self._collection.create_index([
                    ("created_at", -1)
                ], name="created_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建创建时间索引失败: {str(e)}")
            
            # 创建投票相关索引
            try:
                await self._collection.create_index([
                    ("likes", -1),
                    ("created_at", -1)
                ], name="likes_created_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建点赞-创建时间索引失败: {str(e)}")
            
            # 创建搜索索引（适配新的中英文字段）
            try:
                await self._collection.create_index([
                    ("abstract_zh", "text"),
                    ("abstract_en", "text"),
                    ("think_zh", "text"),
                    ("think_en", "text"),
                    ("question_zh", "text"),
                    ("question_en", "text"),
                    ("answer_zh", "text"),
                    ("answer_en", "text")
                ], name="text_search_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建全文搜索索引失败: {str(e)}")
            
            # 创建用户投票查询索引
            try:
                await self._collection.create_index([
                    ("votes.user_id", 1)
                ], name="votes_user_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建投票用户索引失败: {str(e)}")
            
            logger.info("认知数据库索引创建完成")
            
        except Exception as e:
            logger.warning(f"创建认知数据库索引失败: {str(e)}")
    
    def _get_cache_key(self, user_id: str, cognition_ids: List[str]) -> str:
        """生成缓存键"""
        return f"votes:{user_id}:{hash(tuple(sorted(cognition_ids)))}"
    
    def _is_cache_valid(self, timestamp: float) -> bool:
        """检查缓存是否有效"""
        return time.time() - timestamp < self._cache_ttl
    
    def _clear_user_vote_cache(self, user_id: str):
        """清除用户投票缓存"""
        keys_to_remove = []
        for key in self._vote_cache:
            if f":votes:{user_id}:" in key:
                keys_to_remove.append(key)
        for key in keys_to_remove:
            del self._vote_cache[key]
    
    def _convert_doc_ids(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """转换文档中的ObjectId为字符串"""
        if '_id' in doc:
            doc['id'] = str(doc['_id'])
            if doc.get('_id'):
                del doc['_id']
                
        if 'author_id' in doc and isinstance(doc['author_id'], ObjectId):
            doc['author_id'] = str(doc['author_id'])
        
        # 转换votes中的user_id
        if 'votes' in doc:
            for vote in doc['votes']:
                if 'user_id' in vote and isinstance(vote['user_id'], ObjectId):
                    vote['user_id'] = str(vote['user_id'])
        
        # 转换comments中的user_id
        if 'comments' in doc:
            for comment in doc['comments']:
                if 'user_id' in comment and isinstance(comment['user_id'], ObjectId):
                    comment['user_id'] = str(comment['user_id'])
        
        return doc
    
    def _build_query(self, search: Optional[str] = None, topics: Optional[List[str]] = None, 
                     time_filter: Optional[str] = None, source_filter: Optional[str] = None, 
                     custom_query: Optional[Dict] = None) -> Dict[str, Any]:
        """构建查询条件"""
        query = custom_query.copy() if custom_query is not None else {}

        # 时间筛选
        if time_filter and time_filter != 'all':
            now = get_cn_time()
            logger.info(f"原始当前时间: {now}, 类型: {type(now)}, 时区: {now.tzinfo}")
            
            # 转换为 naive datetime 以匹配数据库中可能存储的格式
            if now.tzinfo is not None:
                # 如果是带时区的时间，转换为 naive datetime（保持东八区的时间值）
                now_naive = now.replace(tzinfo=None)
            else:
                now_naive = now
            
            # 初始化变量
            start_time = None
            end_time = None

            if time_filter == 'day':
                start_time = now_naive - timedelta(days=1)
            elif time_filter == 'week':
                start_time = now_naive - timedelta(weeks=1)
            elif time_filter == 'month':
                start_time = now_naive - timedelta(days=30)
            # 新增：对比时间段支持
            elif time_filter == 'yesterday':
                # 昨天：从前天开始到昨天结束
                start_time = now_naive - timedelta(days=2)
                end_time = now_naive - timedelta(days=1)
            elif time_filter == 'last_week':
                # 上周：从两周前开始到一周前结束
                start_time = now_naive - timedelta(weeks=2)
                end_time = now_naive - timedelta(weeks=1)
            elif time_filter == 'last_month':
                # 上月：从两个月前开始到一个月前结束
                start_time = now_naive - timedelta(days=60)
                end_time = now_naive - timedelta(days=30)
                
            if start_time:
                # 创建兼容多种时间格式的查询条件
                time_conditions = []

                if end_time:
                    # 时间范围查询（用于yesterday, last_week, last_month）
                    # 1. 原始datetime格式查询
                    time_conditions.append({
                        "raw_at": {"$gte": start_time, "$lt": end_time}
                    })

                    # 2. 字符串格式查询 (ISO格式)
                    start_time_str = start_time.isoformat()
                    end_time_str = end_time.isoformat()
                    time_conditions.append({
                        "raw_at": {"$gte": start_time_str, "$lt": end_time_str}
                    })

                    # 3. UTC时间查询（以防数据库存储的是UTC时间）
                    utc_start_time = start_time - timedelta(hours=8)  # 东八区转UTC
                    utc_end_time = end_time - timedelta(hours=8)
                    time_conditions.append({
                        "raw_at": {"$gte": utc_start_time, "$lt": utc_end_time}
                    })
                else:
                    # 单向时间查询（用于day, week, month）
                    # 1. 原始datetime格式查询
                    time_conditions.append({"raw_at": {"$gte": start_time}})

                    # 2. 字符串格式查询 (ISO格式)
                    start_time_str = start_time.isoformat()
                    time_conditions.append({"raw_at": {"$gte": start_time_str}})

                    # 3. UTC时间查询（以防数据库存储的是UTC时间）
                    utc_start_time = start_time - timedelta(hours=8)  # 东八区转UTC
                    time_conditions.append({"raw_at": {"$gte": utc_start_time}})

                # 使用$or来匹配任何一种时间格式
                if "$and" in query:
                    query["$and"].append({"$or": time_conditions})
                elif "$or" in query:
                    existing_or = query.pop("$or")
                    query["$and"] = [{"$or": existing_or}, {"$or": time_conditions}]
                else:
                    query["$or"] = time_conditions
                    
                    logger.info(f"时间筛选详情 - filter: {time_filter}, start_time: {start_time}, now_naive: {now_naive}")
                    logger.info(f"时间筛选条件: {time_conditions}")

        # 搜索、话题筛选和来源筛选
        filter_conditions = []
        if search:
            filter_conditions.append({"$or": [
                {"abstract_zh": {"$regex": search, "$options": "i"}},
                {"abstract_en": {"$regex": search, "$options": "i"}},
                {"question_zh": {"$regex": search, "$options": "i"}},
                {"question_en": {"$regex": search, "$options": "i"}},
                {"answer_zh": {"$regex": search, "$options": "i"}},
                {"answer_en": {"$regex": search, "$options": "i"}}
            ]})
        
        if topics:
            filter_conditions.append({"$or": [{"primary_topic": {"$in": topics}}, {"related_topic": {"$in": topics}}]})
        
        # 添加来源筛选
        if source_filter and source_filter != 'all':
            filter_conditions.append({"source": source_filter})
        
        if filter_conditions:
            if '$and' in query:
                query['$and'].extend(filter_conditions)
            else:
                if '$or' in query:
                    # 如果已经有时间筛选的$or，需要将所有条件包装在$and中
                    existing_or = query.pop('$or')
                    query['$and'] = [{"$or": existing_or}] + filter_conditions
                else:
                    query['$and'] = filter_conditions

        logger.info(f"最终查询条件: {query}")
        return query

    @monitor_performance("get_cognitions_paginated")
    async def get_cognitions_paginated(self, skip: int = 0, limit: int = 20, search: Optional[str] = None, 
                                     sort: Optional[str] = None, topics: Optional[List[str]] = None, 
                                     user_id: Optional[str] = None, time_filter: Optional[str] = None, 
                                     source_filter: Optional[str] = None, custom_query: Optional[Dict] = None) -> List[Dict[str, Any]]:
        logger.info(f"分页查询参数 - skip: {skip}, limit: {limit}, search: {search}, sort: {sort}, topics: {topics}, time_filter: {time_filter}, source_filter: {source_filter}")
        query = self._build_query(search, topics, time_filter, source_filter, custom_query)
        try:
            sort_condition = {"raw_at": -1} # Default sort

            logger.info(f"执行查询 - query: {query}, sort: {sort_condition}")
            cursor = self._collection.find(query).sort(list(sort_condition.items())).skip(skip).limit(limit)
            docs = await cursor.to_list(length=None)
            logger.info(f"查询返回文档数: {len(docs)}")
            
            # 检查前几个文档的时间字段
            if docs:
                for i, doc in enumerate(docs[:3]):  # 只检查最后5个文档
                    raw_at = doc.get('raw_at')
                    logger.info(f"文档{i+1} raw_at: {raw_at} (类型: {type(raw_at)})")
            
            # 转换文档ID
            results = []
            cognition_ids = []
            for doc in docs:
                converted_doc = self._convert_doc_ids(doc)
                results.append(converted_doc)
                cognition_ids.append(converted_doc['id'])
            
            # 批量获取投票统计信息
            if cognition_ids:
                vote_stats_dict = await self.vote_db.get_multiple_cognitions_vote_stats(cognition_ids, user_id)
                
                # 将投票统计信息添加到结果中
                for result in results:
                    cognition_id = result['id']
                    if cognition_id in vote_stats_dict:
                        vote_stats = vote_stats_dict[cognition_id]
                        # 更新认知文档中的投票计数（保持向后兼容）
                        result['likes'] = vote_stats.like_count
                        result['neutral'] = vote_stats.neutral_count
                        result['dislikes'] = vote_stats.dislike_count
                        result['user_vote'] = vote_stats.user_vote
                        # 添加新的投票统计字段
                        result['vote_stats'] = {
                            'like_count': vote_stats.like_count,
                            'neutral_count': vote_stats.neutral_count,
                            'dislike_count': vote_stats.dislike_count,
                            'user_vote': vote_stats.user_vote
                        }
                    else:
                        # 如果没有投票统计，使用数据库中的默认值
                        result['likes'] = result.get('likes', 0)
                        result['neutral'] = result.get('neutral', 0)
                        result['dislikes'] = result.get('dislikes', 0)
                        result['user_vote'] = None
                        result['vote_stats'] = {
                            'like_count': result.get('likes', 0),
                            'neutral_count': result.get('neutral', 0),
                            'dislike_count': result.get('dislikes', 0),
                            'user_vote': None
                        }
            logger.info(f"最终返回结果数: {len(results)}")
            # 如果是most_liked排序，确保结果按likes降序排列
            if sort == "most_liked":
                results.sort(key=lambda x: (x.get('likes', 0), x.get('raw_at', '1970-01-01')), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"获取认知列表失败: {str(e)}")
            return []
    
    @monitor_performance("get_cognitions_count")
    async def get_cognitions_count(self, search: Optional[str] = None, topics: Optional[List[str]] = None, 
                                 time_filter: Optional[str] = None, source_filter: Optional[str] = None, 
                                 custom_query: Optional[Dict] = None) -> int:
        query = self._build_query(search, topics, time_filter, source_filter, custom_query)
        try:
            logger.info(f"执行认知数量查询 - query: {query}")
            count = await self._collection.count_documents(query)
            logger.info(f"查询结果数量: {count}")
            return count
            
        except Exception as e:
            logger.error(f"获取认知总数失败: {str(e)}")
            return 0
    
    @monitor_performance("get_cognition_by_id")
    async def get_cognition_by_id(self, cognition_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """根据ID获取认知"""
        try:
            if not ObjectId.is_valid(cognition_id):
                return None
            
            doc = await self._collection.find_one({"_id": ObjectId(cognition_id)})
            if not doc:
                return None
            
            result = self._convert_doc_ids(doc)
            
            # 获取投票统计信息
            vote_stats = await self.vote_db.get_cognition_vote_stats(cognition_id, user_id)
            
            # 更新认知文档中的投票计数（保持向后兼容）
            result['likes'] = vote_stats.like_count
            result['neutral'] = vote_stats.neutral_count
            result['dislikes'] = vote_stats.dislike_count
            result['user_vote'] = vote_stats.user_vote
            
            # 添加新的投票统计字段
            result['vote_stats'] = {
                'like_count': vote_stats.like_count,
                'neutral_count': vote_stats.neutral_count,
                'dislike_count': vote_stats.dislike_count,
                'user_vote': vote_stats.user_vote
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取认知失败: {str(e)}")
            return None
    
    @monitor_performance("create_cognition")
    async def create_cognition(self, cognition_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新认知"""
        try:
            # 准备时间字段
            current_time = get_cn_time().replace(tzinfo=None)
            
            # 创建认知文档
            cognition_doc = {
                **cognition_data,
                "likes": 0,
                "neutral": 0,
                "dislikes": 0,
                "votes": [],
                "comments": [],
                # 只有当传入数据中没有这些字段时才使用当前时间
                "created_at": cognition_data.get("created_at") or current_time,
                "updated_at": current_time,  # updated_at 总是使用当前时间
                "raw_at": cognition_data.get("raw_at") or cognition_data.get("created_at") or current_time  # 优先使用传入的raw_at，否则使用created_at，最后才使用当前时间
            }
            
            logger.info(f"创建认知文档，时间字段: created_at={cognition_doc['created_at']}, raw_at={cognition_doc['raw_at']}, updated_at={cognition_doc['updated_at']}")
            
            # 插入数据库
            result = await self._collection.insert_one(cognition_doc)
            
            # 返回创建的认知
            created_cognition = await self._collection.find_one({"_id": result.inserted_id})
            if created_cognition:
                return self._convert_doc_ids(created_cognition)
            else:
                raise Exception("创建认知后无法找到记录")
                
        except Exception as e:
            logger.error(f"创建认知失败: {str(e)}")
            raise
    
    @monitor_performance("update_cognition")
    async def update_cognition(self, cognition_id: str, cognition_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新认知"""
        try:
            # 设置更新时间
            cognition_data["updated_at"] = get_cn_time().replace(tzinfo=None)  # 确保存储为naive datetime
            
            # 支持ObjectId和字符串ID
            if ObjectId.is_valid(cognition_id):
                query = {"_id": ObjectId(cognition_id)}
            else:
                query = {"id": cognition_id}
            
            result = await self._collection.update_one(
                query,
                {"$set": cognition_data}
            )
            
            if result.modified_count == 0:
                return None
            
            # 返回更新后的文档
            doc = await self._collection.find_one(query)
            return self._convert_doc_ids(doc) if doc else None
        except Exception as e:
            logger.error(f"更新认知失败: {str(e)}")
            raise
    
    @monitor_performance("delete_cognition")
    async def delete_cognition(self, cognition_id: str) -> bool:
        """删除认知"""
        try:
            # 先删除相关的收藏记录
            favorites_db = CognitionFavoritesDatabase()
            await favorites_db.remove_all_favorites_for_cognition(cognition_id)
            
            # 支持ObjectId和字符串ID
            if ObjectId.is_valid(cognition_id):
                query = {"_id": ObjectId(cognition_id)}
            else:
                query = {"id": cognition_id}
            
            result = await self._collection.delete_one(query)
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除认知失败: {str(e)}")
            raise
    
    @monitor_performance("add_comment")
    async def add_comment(self, cognition_id: str, comment: Dict[str, Any]) -> bool:
        """添加评论"""
        try:
            # 生成评论ID
            comment["id"] = str(uuid.uuid4())
            comment["created_at"] = get_cn_time().replace(tzinfo=None)  # 确保存储为naive datetime
            
            # 支持ObjectId和字符串ID
            if ObjectId.is_valid(cognition_id):
                query = {"_id": ObjectId(cognition_id)}
            else:
                query = {"id": cognition_id}
            
            result = await self._collection.update_one(
                query,
                {
                    "$push": {"comments": comment},
                    "$set": {"updated_at": get_cn_time().replace(tzinfo=None)}  # 确保存储为naive datetime
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"添加评论失败: {str(e)}")
            raise
    
    @monitor_performance("update_vote")
    async def update_vote(self, cognition_id: str, user_id: str, vote_type: str) -> bool:
        """更新用户投票 - 使用新的投票系统，支持取消投票"""
        try:
            # 验证认知是否存在
            cognition = await self.get_cognition_by_id(cognition_id)
            if not cognition:
                logger.warning(f"认知不存在: {cognition_id}")
                return False
            
            # 获取当前用户的投票状态
            current_vote = await self.vote_db.get_user_vote(user_id, cognition_id)
            current_vote_type = current_vote.vote_type if current_vote else None
            
            # 实现正确的单选投票逻辑
            if vote_type == "cancel" or (current_vote_type == vote_type):
                # 取消投票
                success = await self.vote_db.delete_vote(user_id, cognition_id)
                action = "取消投票" if success else "取消投票失败"
            else:
                # 创建或更新投票
                vote = await self.vote_db.create_vote(user_id, cognition_id, vote_type)
                success = vote is not None
                action = f"投票: {vote_type}" if success else "投票失败"
            
            if success:
                # 清除用户投票缓存
                self._clear_user_vote_cache(user_id)
                logger.info(f"用户 {user_id} 对认知 {cognition_id} {action}")
                return True
            else:
                logger.error(f"投票操作失败: user_id={user_id}, cognition_id={cognition_id}, vote_type={vote_type}")
                return False
                
        except Exception as e:
            logger.error(f"更新投票失败: {str(e)}")
            return False
    
    @monitor_performance("get_user_vote")
    async def get_user_vote(self, cognition_id: str, user_id: str) -> Optional[str]:
        """获取用户对指定认知的投票状态 - 使用新的投票系统"""
        try:
            vote = await self.vote_db.get_user_vote(user_id, cognition_id)
            return vote.vote_type if vote else None
        except Exception as e:
            logger.error(f"获取用户投票状态失败: {str(e)}")
            return None
    
    @monitor_performance("get_cognitions_by_ids")
    async def get_cognitions_by_ids(self, cognition_ids: List[str], user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """根据ID列表批量获取认知"""
        try:
            if not cognition_ids:
                return []
            
            valid_object_ids = [ObjectId(id) for id in cognition_ids if ObjectId.is_valid(id)]
            
            if not valid_object_ids:
                return []
            
            query = {"_id": {"$in": valid_object_ids}}
            cursor = self._collection.find(query)
            docs = await cursor.to_list(length=len(valid_object_ids))
            
            docs_dict = {str(doc['_id']): doc for doc in docs}
            sorted_docs = [docs_dict.get(id) for id in cognition_ids if id in docs_dict]

            results = [self._convert_doc_ids(doc) for doc in sorted_docs]
            
            if results:
                result_ids = [res['id'] for res in results]
                vote_stats_dict = await self.vote_db.get_multiple_cognitions_vote_stats(result_ids, user_id)
                
                for result in results:
                    cognition_id = result['id']
                    if cognition_id in vote_stats_dict:
                        vote_stats = vote_stats_dict[cognition_id]
                        result['likes'] = vote_stats.like_count
                        result['user_vote'] = vote_stats.user_vote
            
            return results
            
        except Exception as e:
            logger.error(f"批量获取认知失败: {str(e)}")
            return []

    @monitor_performance("get_user_votes_batch")
    async def get_user_votes_batch(self, cognition_ids: List[str], user_id: str) -> Dict[str, str]:
        """批量获取用户投票状态"""
        try:
            # 检查缓存
            cache_key = self._get_cache_key(user_id, cognition_ids)
            if cache_key in self._vote_cache:
                cached_data, timestamp = self._vote_cache[cache_key]
                if self._is_cache_valid(timestamp):
                    return cached_data
            
            # 构建查询条件，支持混合ID类型
            or_conditions = []
            for cognition_id in cognition_ids:
                if ObjectId.is_valid(cognition_id):
                    or_conditions.append({"_id": ObjectId(cognition_id)})
                else:
                    or_conditions.append({"id": cognition_id})
            
            query = {"$or": or_conditions}
            
            # 只获取id和votes字段
            cursor = self._collection.find(query, {"id": 1, "_id": 1, "votes": 1})
            results = await cursor.to_list(length=len(cognition_ids))
            
            user_votes = {}
            for doc in results:
                doc_id = doc.get("id") or str(doc.get("_id"))
                votes = doc.get("votes", [])
                for vote in votes:
                    if str(vote.get("user_id")) == str(user_id):
                        user_votes[doc_id] = vote.get("vote_type")
                        break
            
            # 缓存结果
            self._vote_cache[cache_key] = (user_votes, time.time())
            
            return user_votes
        except Exception as e:
            logger.error(f"批量获取用户投票状态失败: {str(e)}")
            raise
    
    @monitor_performance("search_cognitions")
    async def search_cognitions(self, query: str, skip: int = 0, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索认知"""
        try:
            # 构建搜索条件
            search_condition = {
                "$or": [
                    {"abstract_zh": {"$regex": query, "$options": "i"}},
                    {"abstract_en": {"$regex": query, "$options": "i"}},
                    {"think_zh": {"$regex": query, "$options": "i"}},
                    {"think_en": {"$regex": query, "$options": "i"}},
                    {"question_zh": {"$regex": query, "$options": "i"}},
                    {"question_en": {"$regex": query, "$options": "i"}},
                    {"answer_zh": {"$regex": query, "$options": "i"}},
                    {"answer_en": {"$regex": query, "$options": "i"}}
                ]
            }
            
            # 执行搜索
            cursor = self._collection.find(search_condition).sort("raw_at", -1).skip(skip).limit(limit)
            docs = await cursor.to_list(length=None)
            
            # 转换文档ID
            results = []
            for doc in docs:
                results.append(self._convert_doc_ids(doc))
            
            return results
            
        except Exception as e:
            logger.error(f"搜索认知失败: {str(e)}")
            return []

    @monitor_performance("get_all_topics")
    async def get_all_topics(self) -> Dict[str, Any]:
        """获取所有可用的topics - 返回预定义的分层结构"""
        try:
            # 预定义的话题分层结构
            topic_hierarchy = [
                {
                    "primary_topic": "Architectures",
                    "related_topic": [
                        "Sequence-Modeling-Paradigms",
                        "Transformer-Based-Models", 
                        "State-Space-Models",
                        "Hybrid-Architectures",
                        "Architectural-Enhancements",
                        "Long-Context-Handling",
                        "Mixture-of-Experts",
                        "Generative-and-Multimodal-Models",
                        "Generative-Modeling-Foundations",
                        "Multi-Modal-Learning",
                        "Vision-Language-Models-(VLMs)",
                        "Audio-and-Speech-Integration"
                    ]
                },
                {
                    "primary_topic": "Model-Lifecycle", 
                    "related_topic": [
                        "Data-Engineering",
                        "Knowledge-Data-Curation",
                        "Data-Filtering-and-Cleaning",
                        "Synthetic-Data-Generation",
                        "Cognition-Data-Engineering",
                        "Human-Cognition-Projections",
                        "AI-Generated-Cognition-Data",
                        "Foundational-Training",
                        "Pre-training-Strategies",
                        "Instruction-Fine-tuning",
                        "Parameter-Efficient-Fine-tuning",
                        "Preference-Learning-and-Alignment",
                        "Reinforcement-Learning-from-Feedback",
                        "Direct-Preference-Optimization",
                        "Constitutional-AI",
                        "Training-for-Cognition-(Act-II)",
                        "Reinforcement-Learning-for-Cognition",
                        "Rule-based-Reward-Scaling",
                        "Multi-stage-Training-Strategies",
                        "Supervised-Fine-tuning-for-Cognition",
                        "Distillation-from-Long-CoT",
                        "Trajectory-Synthesis-and-Translation",
                        "Iterative-Self-Reinforced-Learning",
                        "Data-Generation-via-TTS",
                        "Offline-Policy-Update-(SFT/DPO)"
                    ]
                },
                {
                    "primary_topic": "Cognition-and-Reasoning",
                    "related_topic": [
                        "Knowledge-Level-Capabilities-(Act-I)",
                        "Prompt-Engineering",
                        "In-Context-Learning",
                        "Knowledge-and-Memory",
                        "Knowledge-Editing-and-Unlearning",
                        "Retrieval-Augmented-Generation-(RAG)",
                        "Wisdom-Level-Capabilities-(Act-II)",
                        "Parallel-Sampling",
                        "Best-of-N-(BoN)",
                        "Majority-Voting-(Self-Consistency)",
                        "Tree-Search",
                        "Tree-of-Thought-(ToT)",
                        "Monte-Carlo-Tree-Search-(MCTS)",
                        "Multi-turn-Correction",
                        "Self-Correction-and-Refinement",
                        "Feedback-driven-Revision",
                        "Long-Chain-of-Thought-(Long-CoT)",
                        "Reflection-and-Backtracking",
                        "Divergent-Thinking"
                    ]
                },
                {
                    "primary_topic": "Systems-and-Efficiency",
                    "related_topic": [
                        "Efficient-Training",
                        "Distributed-Training-Paradigms",
                        "RL-and-MCTS-Infrastructure",
                        "Efficient-Inference",
                        "Model-Optimization",
                        "Quantization-and-Pruning",
                        "Knowledge-Distillation",
                        "Inference-Time-Acceleration",
                        "Speculative-Decoding",
                        "KV-Cache-Management",
                        "Test-Time-Scaling-Efficiency",
                        "Query-Aware-Strategies",
                        "Reasoning-Trace-Compression",
                        "Reasoning-in-Latent-Space"
                    ]
                },
                {
                    "primary_topic": "Agentic-AI",
                    "related_topic": [
                        "Agent-Architectures",
                        "Long-Horizon-Planning", 
                        "Tool-Use-and-API-Integration",
                        "Human-Agent-Interaction",
                        "Multi-Agent-Systems"
                    ]
                },
                {
                    "primary_topic": "Trust-and-Society",
                    "related_topic": [
                        "Evaluation-and-Benchmarking",
                        "Benchmark-Types",
                        "Evaluation-Paradigms",
                        "LLM-as-a-Judge",
                        "Evaluating-Cognitive-Processes",
                        "Interpretability-and-Analysis",
                        "Mechanistic-Interpretability",
                        "Representation-Engineering",
                        "Safety-and-Robustness",
                        "Adversarial-Resilience",
                        "Reasoning-based-Safety",
                        "Deliberative-Alignment",
                        "Chain-of-Verification",
                        "Ethics-and-Governance",
                        "Bias-Fairness-and-Toxicity",
                        "AI-Governance-and-Policy"
                    ]
                }
            ]
            
            # 构建响应数据
            all_topics = []
            categories = {}
            
            for category in topic_hierarchy:
                primary = category["primary_topic"]
                related = category["related_topic"]
                
                # 添加主分类
                all_topics.append(primary)
                categories[primary] = {
                    "type": "primary",
                    "color": self._get_primary_topic_color(primary),
                    "background": self._get_primary_topic_background(primary),
                    "children": related
                }
                
                # 添加子分类
                all_topics.extend(related)
                for topic in related:
                    categories[topic] = {
                        "type": "secondary", 
                        "parent": primary,
                        "color": self._get_secondary_topic_color(primary),
                        "background": self._get_secondary_topic_background(primary)
                    }
            
            return {
                "hierarchy": topic_hierarchy,
                "categories": categories,
                "all_topics": all_topics,
                "available_list": all_topics
            }
            
        except Exception as e:
            logger.error(f"获取可用topics失败: {str(e)}")
            return {
                "hierarchy": [],
                "categories": {},
                "all_topics": [],
                "available_list": []
            }
    
    def _get_primary_topic_color(self, topic: str) -> str:
        """获取主分类颜色"""
        color_map = {
            "Architectures": "#FF6B6B",
            "Model-Lifecycle": "#4ECDC4", 
            "Cognition-and-Reasoning": "#45B7D1",
            "Systems-and-Efficiency": "#96CEB4",
            "Agentic-AI": "#FFEAA7",
            "Trust-and-Society": "#DDA0DD"
        }
        return color_map.get(topic, "#667eea")
    
    def _get_primary_topic_background(self, topic: str) -> str:
        """获取主分类背景色"""
        background_map = {
            "Architectures": "#FFE5E5",
            "Model-Lifecycle": "#E5F9F7",
            "Cognition-and-Reasoning": "#E5F4FD", 
            "Systems-and-Efficiency": "#F0F9F5",
            "Agentic-AI": "#FDF8E7",
            "Trust-and-Society": "#F4E8F4"
        }
        return background_map.get(topic, "#EFEEFF")
    
    def _get_secondary_topic_color(self, parent: str) -> str:
        """获取子分类颜色（比主分类稍深）"""
        color_map = {
            "Architectures": "#E74C3C",
            "Model-Lifecycle": "#16A085",
            "Cognition-and-Reasoning": "#3498DB", 
            "Systems-and-Efficiency": "#27AE60",
            "Agentic-AI": "#F39C12",
            "Trust-and-Society": "#9B59B6"
        }
        return color_map.get(parent, "#5A67D8")
    
    def _get_secondary_topic_background(self, parent: str) -> str:
        """获取子分类背景色"""
        background_map = {
            "Architectures": "#FADBD8",
            "Model-Lifecycle": "#D5F4E6",
            "Cognition-and-Reasoning": "#D6EAF8",
            "Systems-and-Efficiency": "#D5F5E3", 
            "Agentic-AI": "#FDEBD0",
            "Trust-and-Society": "#EBDEF0"
        }
        return background_map.get(parent, "#E2E8F0")


class CognitionCollectionsDatabase(MongoDBBase):
    """认知收藏夹数据库操作类"""
    
    def __init__(self, collection: Optional[AsyncIOMotorCollection] = None):
        if collection is not None:
            super().__init__(collection_name="cognition_collections", injected_collection=collection)
        else:
            super().__init__(collection_name="cognition_collections")
    
    def _convert_doc_ids(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """转换文档中的ObjectId为字符串"""
        if '_id' in doc:
            doc['id'] = str(doc['_id'])
            if doc.get('_id'):
                del doc['_id']
        
        if 'user_id' in doc and isinstance(doc['user_id'], ObjectId):
            doc['user_id'] = str(doc['user_id'])
        
        return doc
    
    @monitor_performance("get_user_collections")
    async def get_user_collections(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的收藏夹列表"""
        try:
            cursor = self._collection.find({"user_id": user_id}).sort("created_at", 1)
            results = await cursor.to_list(length=None)
            
            collections = []
            for doc in results:
                doc = self._convert_doc_ids(doc)
                collections.append(doc)
            
            return collections
        except Exception as e:
            logger.error(f"获取用户收藏夹列表失败: {str(e)}")
            raise
    
    @monitor_performance("create_collection")
    async def create_collection(self, user_id: str, name: str, description: str = "", is_default: bool = False) -> Dict[str, Any]:
        """创建收藏夹"""
        try:
            collection_data = {
                "id": str(uuid.uuid4()),
                "user_id": user_id,
                "name": name,
                "description": description,
                "is_default": is_default,
                "created_at": get_cn_time().replace(tzinfo=None),  # 确保存储为naive datetime
                "updated_at": get_cn_time().replace(tzinfo=None)  # 确保存储为naive datetime
            }
            
            result = await self._collection.insert_one(collection_data)
            collection_data["_id"] = result.inserted_id
            
            return self._convert_doc_ids(collection_data)
        except Exception as e:
            logger.error(f"创建收藏夹失败: {str(e)}")
            raise
    
    @monitor_performance("delete_collection")
    async def delete_collection(self, collection_id: str) -> bool:
        """删除收藏夹"""
        try:
            # 先删除收藏夹中的所有收藏记录
            favorites_db = CognitionFavoritesDatabase()
            await favorites_db.remove_all_favorites_in_collection(collection_id)
            
            # 支持ObjectId和字符串ID
            if ObjectId.is_valid(collection_id):
                query = {"_id": ObjectId(collection_id)}
            else:
                query = {"id": collection_id}
            
            result = await self._collection.delete_one(query)
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除收藏夹失败: {str(e)}")
            raise
    
    @monitor_performance("update_collection")
    async def update_collection(self, collection_id: str, name: str, description: str = "") -> Optional[Dict[str, Any]]:
        """更新收藏夹"""
        try:
            update_data = {
                "name": name,
                "description": description,
                "updated_at": get_cn_time().replace(tzinfo=None)  # 确保存储为naive datetime
            }
            
            # 支持ObjectId和字符串ID
            if ObjectId.is_valid(collection_id):
                query = {"_id": ObjectId(collection_id)}
            else:
                query = {"id": collection_id}
            
            result = await self._collection.update_one(
                query,
                {"$set": update_data}
            )
            
            if result.modified_count == 0:
                return None
            
            # 返回更新后的文档
            doc = await self._collection.find_one(query)
            return self._convert_doc_ids(doc) if doc else None
        except Exception as e:
            logger.error(f"更新收藏夹失败: {str(e)}")
            raise
    
    @monitor_performance("ensure_default_collection")
    async def ensure_default_collection(self, user_id: str) -> Dict[str, Any]:
        """确保用户有默认收藏夹"""
        try:
            # 查找默认收藏夹
            default_collection = await self._collection.find_one({
                "user_id": user_id,
                "is_default": True
            })
            
            if default_collection:
                return self._convert_doc_ids(default_collection)
            
            # 创建默认收藏夹
            return await self.create_collection(user_id, "默认收藏夹", "系统自动创建的默认收藏夹", True)
        except Exception as e:
            logger.error(f"确保默认收藏夹失败: {str(e)}")
            raise
    
    @monitor_performance("get_collections_with_counts_batch")
    async def get_collections_with_counts_batch(self, user_id: str) -> List[Dict[str, Any]]:
        """批量获取收藏夹及其认知数量"""
        try:
            # 获取用户的收藏夹
            collections = await self.get_user_collections(user_id)
            
            # 获取每个收藏夹的认知数量
            favorites_db = CognitionFavoritesDatabase()
            for collection in collections:
                collection_id = collection["id"]
                count = await favorites_db.get_collection_cognitions_count(collection_id)
                collection["cognition_count"] = count
            
            return collections
        except Exception as e:
            logger.error(f"批量获取收藏夹及认知数量失败: {str(e)}")
            raise


class CognitionFavoritesDatabase(MongoDBBase):
    """认知收藏数据库操作类"""
    
    def __init__(self, collection: Optional[AsyncIOMotorCollection] = None):
        if collection is not None:
            super().__init__(collection_name="cognition_favorites", model_class=None, injected_collection=collection)
        else:
            super().__init__(collection_name="cognition_favorites", model_class=None)
        
        # 添加批量缓存
        self._batch_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
        
        # 确保索引存在
        self._ensure_indexes()
    
    def _ensure_indexes(self):
        """确保必要的索引存在"""
        try:
            # 创建后台任务来设置索引
            asyncio.create_task(self._create_indexes())
        except Exception as e:
            logger.warning(f"创建收藏夹索引任务失败: {str(e)}")
    
    async def _create_indexes(self):
        """创建收藏夹数据库索引"""
        try:
            # 等待一下确保集合已创建
            await asyncio.sleep(1)
            
            # 创建用户ID和认知ID的复合索引
            try:
                await self._collection.create_index([
                    ("user_id", 1),
                    ("cognition_id", 1)
                ], name="user_cognition_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建用户-认知复合索引失败: {str(e)}")
            
            # 创建收藏夹ID索引
            try:
                await self._collection.create_index([
                    ("collection_id", 1)
                ], name="collection_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建收藏夹索引失败: {str(e)}")
            
            # 创建认知ID索引（用于删除认知时清理收藏）
            try:
                await self._collection.create_index([
                    ("cognition_id", 1)
                ], name="cognition_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建认知索引失败: {str(e)}")
            
            logger.info("收藏夹数据库索引创建完成")
            
        except Exception as e:
            logger.warning(f"创建收藏夹数据库索引失败: {str(e)}")
    
    def _convert_doc_ids(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """转换文档中的ObjectId为字符串"""
        if '_id' in doc:
            doc['id'] = str(doc['_id'])
            if doc.get('_id'):
                del doc['_id']
        
        if 'user_id' in doc and isinstance(doc['user_id'], ObjectId):
            doc['user_id'] = str(doc['user_id'])
        
        return doc
    
    @monitor_performance("add_to_favorites")
    async def add_to_favorites(self, user_id: str, cognition_id: str, collection_id: str) -> bool:
        """添加到收藏夹"""
        try:
            # 清除用户收藏缓存
            self._clear_user_favorites_cache(user_id)
            
            # 检查是否已存在
            existing = await self._collection.find_one({
                "user_id": user_id,
                "cognition_id": cognition_id,
                "collection_id": collection_id
            })
            
            if existing:
                return True  # 已存在，返回成功
            
            # 插入新记录
            doc = {
                "id": str(uuid.uuid4()),
                "user_id": user_id,
                "cognition_id": cognition_id,
                "collection_id": collection_id,
                "created_at": get_cn_time().replace(tzinfo=None)  # 确保存储为naive datetime
            }
            
            result = await self._collection.insert_one(doc)
            return result.inserted_id is not None
            
        except Exception as e:
            logger.error(f"添加到收藏夹失败: {str(e)}")
            raise
    
    @monitor_performance("remove_from_favorites")
    async def remove_from_favorites(self, user_id: str, cognition_id: str, collection_id: str = None) -> bool:
        """从收藏夹移除"""
        try:
            # 清除用户收藏缓存
            self._clear_user_favorites_cache(user_id)
            
            query = {
                "user_id": user_id,
                "cognition_id": cognition_id
            }
            
            if collection_id:
                query["collection_id"] = collection_id
            
            result = await self._collection.delete_many(query)
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"从收藏夹移除失败: {str(e)}")
            raise
    
    @monitor_performance("get_favorite_status")
    async def get_favorite_status(self, user_id: str, cognition_id: str) -> Dict[str, Any]:
        """获取收藏状态"""
        try:
            cursor = self._collection.find({
                "user_id": user_id,
                "cognition_id": cognition_id
            })
            results = await cursor.to_list(length=None)
            
            collections = [result["collection_id"] for result in results]
            
            return {
                "is_favorited": len(collections) > 0,
                "collections": collections
            }
        except Exception as e:
            logger.error(f"获取收藏状态失败: {str(e)}")
            raise
    
    @monitor_performance("get_collection_cognitions")
    async def get_collection_cognitions(self, collection_id: str, skip: int = 0, limit: int = 20) -> Dict[str, Any]:
        """获取收藏夹中的认知列表"""
        try:
            logger.info(f"开始获取收藏夹 {collection_id} 的认知列表, skip={skip}, limit={limit}")
            total_count = await self.get_collection_cognitions_count(collection_id)
            logger.info(f"收藏夹 {collection_id} 共有 {total_count} 个认知")

            cursor = self._collection.find({"collection_id": collection_id}).sort("created_at", -1).skip(skip).limit(limit)
            favorites = await cursor.to_list(length=limit)
            logger.info(f"从数据库获取到 {len(favorites)} 条收藏记录: {favorites}")
            
            if not favorites:
                logger.info("没有找到收藏记录，返回空列表")
                return {"items": [], "total": total_count}
            
            cognition_ids = []
            for fav in favorites:
                if isinstance(fav, dict) and "cognition_id" in fav:
                    cognition_ids.append(fav["cognition_id"])
                else:
                    logger.warning(f"发现无效的收藏记录: {fav} (类型: {type(fav)})")

            logger.info(f"提取出的认知ID列表: {cognition_ids}")

            if not cognition_ids:
                logger.info("没有有效的认知ID，返回空列表")
                return {"items": [], "total": total_count}

            cognition_db = CognitionDatabase()
            cognitions = []
            for cognition_id in cognition_ids:
                logger.info(f"正在获取认知详情: {cognition_id}")
                cognition = await cognition_db.get_cognition_by_id(cognition_id)
                if cognition:
                    cognitions.append(cognition)
                else:
                    logger.warning(f"无法获取到认知详情: {cognition_id}")
            
            logger.info(f"成功获取 {len(cognitions)} 个认知详情，准备返回")
            return {"items": cognitions, "total": total_count}
        except Exception as e:
            logger.error(f"获取收藏夹认知列表失败: {str(e)}", exc_info=True)
            raise
    
    @monitor_performance("get_collection_cognitions_count")
    async def get_collection_cognitions_count(self, collection_id: str) -> int:
        """获取收藏夹中认知的数量"""
        try:
            return await self._collection.count_documents({"collection_id": collection_id})
        except Exception as e:
            logger.error(f"获取收藏夹认知数量失败: {str(e)}")
            raise
    
    @monitor_performance("get_favorite_statuses_batch")
    async def get_favorite_statuses_batch(self, cognition_ids: List[str], user_id: str) -> Dict[str, Dict[str, Any]]:
        """批量获取多个认知的收藏状态"""
        try:
            # 缓存键
            cache_key = self._get_batch_cache_key(user_id, cognition_ids)
            
            # 检查缓存
            if cache_key in self._batch_cache:
                cached_data, timestamp = self._batch_cache[cache_key]
                if self._is_cache_valid(timestamp):
                    return cached_data
            
            # 查询用户对这些认知的收藏记录
            query = {
                "user_id": user_id,
                "cognition_id": {"$in": cognition_ids}
            }
            
            favorites = await self._collection.find(query).to_list(length=None)
            
            # 构建收藏状态字典
            status_dict = {}
            
            # 按认知ID分组收藏记录
            cognition_favorites = {}
            for fav in favorites:
                cognition_id = fav["cognition_id"]
                if cognition_id not in cognition_favorites:
                    cognition_favorites[cognition_id] = []
                cognition_favorites[cognition_id].append(fav["collection_id"])
            
            # 为每个认知构建收藏状态
            for cognition_id in cognition_ids:
                collections = cognition_favorites.get(cognition_id, [])
                status_dict[cognition_id] = {
                    "is_favorited": len(collections) > 0,
                    "collections": collections
                }
            
            # 缓存结果
            self._batch_cache[cache_key] = (status_dict, time.time())
            
            return status_dict
            
        except Exception as e:
            logger.error(f"批量获取收藏状态失败: {str(e)}")
            raise

    def _get_batch_cache_key(self, user_id: str, cognition_ids: List[str]) -> str:
        """生成批量缓存键"""
        return f"batch_favorites:{user_id}:{hash(tuple(sorted(cognition_ids)))}"
    
    def _is_cache_valid(self, timestamp: float) -> bool:
        """检查缓存是否有效"""
        return time.time() - timestamp < self._cache_ttl

    def _clear_user_favorites_cache(self, user_id: str):
        """清除用户收藏缓存"""
        keys_to_remove = []
        for key in self._batch_cache:
            if f":batch_favorites:{user_id}:" in key:
                keys_to_remove.append(key)
        for key in keys_to_remove:
            del self._batch_cache[key]

    async def remove_all_favorites_for_cognition(self, cognition_id: str) -> bool:
        """删除某个认知的所有收藏记录"""
        try:
            result = await self._collection.delete_many({"cognition_id": cognition_id})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除认知收藏记录失败: {str(e)}")
            raise
    
    async def remove_all_favorites_in_collection(self, collection_id: str) -> bool:
        """删除收藏夹中的所有收藏记录"""
        try:
            result = await self._collection.delete_many({"collection_id": collection_id})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除收藏夹中的收藏记录失败: {str(e)}")
            raise

    @monitor_performance("get_favorited_cognitions")
    async def get_favorited_cognitions(self, user_id: str, skip: int = 0, limit: int = 20, 
                                     search: Optional[str] = None, time_filter: Optional[str] = None,
                                     source_filter: Optional[str] = None, sort: Optional[str] = None) -> Dict[str, Any]:
        """获取用户收藏的认知列表（支持搜索、时间筛选和来源筛选），并附加收藏状态"""
        try:
            # 管道阶段1：先找到该用户收藏的所有认知ID
            pipeline = [
                {"$match": {"user_id": user_id}},
                {"$group": {"_id": None, "cognition_ids": {"$addToSet": "$cognition_id"}}}
            ]
            
            result = await self._collection.aggregate(pipeline).to_list(length=1)
            
            if not result or not result[0].get("cognition_ids"):
                return {"items": [], "total": 0}
            
            cognition_ids = result[0]["cognition_ids"]
            
            # 管道阶段2：在cognitions_new集合上进行筛选和分页
            cognition_db = CognitionDatabase()
            
            # 构建筛选条件
            query = {
                "$or": [
                    {"id": {"$in": cognition_ids}},
                    {"_id": {"$in": [ObjectId(i) for i in cognition_ids if ObjectId.is_valid(i)]}}
                ]
            }
            if search:
                query["$or"] = [
                    {"abstract_zh": {"$regex": search, "$options": "i"}},
                    {"abstract_en": {"$regex": search, "$options": "i"}},
                    {"question_zh": {"$regex": search, "$options": "i"}},
                    {"question_en": {"$regex": search, "$options": "i"}},
                    {"answer_zh": {"$regex": search, "$options": "i"}},
                    {"answer_en": {"$regex": search, "$options": "i"}},
                ]
            
            if time_filter and time_filter != 'all':
                now = get_cn_time()
                
                # 转换为 naive datetime 以匹配数据库中可能存储的格式
                if now.tzinfo is not None:
                    # 如果是带时区的时间，转换为 naive datetime（保持东八区的时间值）
                    now_naive = now.replace(tzinfo=None)
                else:
                    now_naive = now
                
                # 初始化变量
                start_time = None
                end_time = None

                if time_filter == 'day':
                    start_time = now_naive - timedelta(days=1)
                elif time_filter == 'week':
                    start_time = now_naive - timedelta(weeks=1)
                elif time_filter == 'month':
                    start_time = now_naive - timedelta(days=30)
                # 新增：对比时间段支持
                elif time_filter == 'yesterday':
                    # 昨天：从前天开始到昨天结束
                    start_time = now_naive - timedelta(days=2)
                    end_time = now_naive - timedelta(days=1)
                elif time_filter == 'last_week':
                    # 上周：从两周前开始到一周前结束
                    start_time = now_naive - timedelta(weeks=2)
                    end_time = now_naive - timedelta(weeks=1)
                elif time_filter == 'last_month':
                    # 上月：从两个月前开始到一个月前结束
                    start_time = now_naive - timedelta(days=60)
                    end_time = now_naive - timedelta(days=30)
                    
                if start_time:
                    # 创建兼容多种时间格式的查询条件
                    time_conditions = []

                    if end_time:
                        # 时间范围查询（用于yesterday, last_week, last_month）
                        # 1. 原始datetime格式查询
                        time_conditions.append({
                            "raw_at": {"$gte": start_time, "$lt": end_time}
                        })

                        # 2. 字符串格式查询 (ISO格式)
                        start_time_str = start_time.isoformat()
                        end_time_str = end_time.isoformat()
                        time_conditions.append({
                            "raw_at": {"$gte": start_time_str, "$lt": end_time_str}
                        })

                        # 3. UTC时间查询（以防数据库存储的是UTC时间）
                        utc_start_time = start_time - timedelta(hours=8)  # 东八区转UTC
                        utc_end_time = end_time - timedelta(hours=8)
                        time_conditions.append({
                            "raw_at": {"$gte": utc_start_time, "$lt": utc_end_time}
                        })
                    else:
                        # 单向时间查询（用于day, week, month）
                        # 1. 原始datetime格式查询
                        time_conditions.append({"raw_at": {"$gte": start_time}})

                        # 2. 字符串格式查询 (ISO格式)
                        start_time_str = start_time.isoformat()
                        time_conditions.append({"raw_at": {"$gte": start_time_str}})

                        # 3. UTC时间查询（以防数据库存储的是UTC时间）
                        utc_start_time = start_time - timedelta(hours=8)  # 东八区转UTC
                        time_conditions.append({"raw_at": {"$gte": utc_start_time}})

                    # 使用$or来匹配任何一种时间格式
                    if "$and" in query:
                        query["$and"].append({"$or": time_conditions})
                    elif "$or" in query:
                        # 如果已经有其他$or条件，将其包装到$and中
                        existing_or = query.pop("$or")
                        query["$and"] = [{"$or": existing_or}, {"$or": time_conditions}]
                    else:
                        query["$or"] = time_conditions

            # 直接使用封装好的分页和计数方法
            items = await cognition_db.get_cognitions_paginated(
                skip=skip, limit=limit, search=search, sort=sort, time_filter=time_filter, 
                source_filter=source_filter, user_id=user_id, custom_query=query
            )
            total = await cognition_db.get_cognitions_count(
                search=search, time_filter=time_filter, source_filter=source_filter, custom_query=query
            )

            # 为结果附加收藏状态（因为我们确定这些都是收藏过的）
            for item in items:
                item['favorite_status'] = {'is_favorited': True, 'collections': []} # collections可以后续再精确查询

            return {"items": items, "total": total}
        except Exception as e:
            logger.error(f"获取收藏认知失败: {str(e)}", exc_info=True)
            raise


class CognitionReadStatusDatabase(MongoDBBase):
    """认知已读状态数据库操作类"""
    
    def __init__(self, collection: Optional[AsyncIOMotorCollection] = None):
        if collection is not None:
            super().__init__(collection_name="cognition_read_status", model_class=None, injected_collection=collection)
        else:
            super().__init__(collection_name="cognition_read_status", model_class=None)
        
        # 确保索引存在
        self._ensure_indexes()
    
    def _ensure_indexes(self):
        """确保必要的索引存在"""
        try:
            asyncio.create_task(self._create_indexes())
        except Exception as e:
            logger.warning(f"创建已读状态索引任务失败: {str(e)}")
    
    async def _create_indexes(self):
        """创建数据库索引"""
        try:
            await asyncio.sleep(1)
            
            # 创建用户ID和认知ID的复合索引
            try:
                await self._collection.create_index([
                    ("user_id", 1),
                    ("cognition_id", 1)
                ], name="user_cognition_idx", unique=True, background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建用户认知索引失败: {str(e)}")
            
            # 创建用户ID索引
            try:
                await self._collection.create_index([
                    ("user_id", 1)
                ], name="user_id_idx", background=True)
            except Exception as e:
                if "already exists" not in str(e):
                    logger.warning(f"创建用户ID索引失败: {str(e)}")
            
            logger.info("已读状态数据库索引创建完成")
            
        except Exception as e:
            logger.warning(f"创建已读状态数据库索引失败: {str(e)}")
    
    def _convert_doc_ids(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """转换文档中的ObjectId为字符串"""
        if '_id' in doc:
            doc['id'] = str(doc['_id'])
            if doc.get('_id'):
                del doc['_id']
        
        if 'user_id' in doc and isinstance(doc['user_id'], ObjectId):
            doc['user_id'] = str(doc['user_id'])
        
        return doc
    
    @monitor_performance("set_read_status")
    async def set_read_status(self, user_id: str, cognition_id: str, is_read: bool) -> Dict[str, Any]:
        """设置认知的已读状态"""
        try:
            now = get_cn_time()
            
            # 使用upsert操作，如果记录存在则更新，不存在则创建
            query = {"user_id": user_id, "cognition_id": cognition_id}
            update_data = {
                "$set": {
                    "is_read": is_read,
                    "updated_at": now
                }
            }
            
            if is_read:
                update_data["$set"]["read_at"] = now
            else:
                update_data["$unset"] = {"read_at": ""}
            
            # 如果是新记录，设置创建时间
            update_data["$setOnInsert"] = {
                "user_id": user_id,
                "cognition_id": cognition_id,
                "created_at": now
            }
            
            result = await self._collection.update_one(
                query,
                update_data,
                upsert=True
            )
            
            # 获取更新后的记录
            updated_doc = await self._collection.find_one(query)
            if updated_doc:
                return self._convert_doc_ids(updated_doc)
            
            return {
                "user_id": user_id,
                "cognition_id": cognition_id,
                "is_read": is_read,
                "read_at": now if is_read else None,
                "updated_at": now
            }
            
        except Exception as e:
            logger.error(f"设置已读状态失败: {str(e)}")
            raise
    
    @monitor_performance("get_read_status")
    async def get_read_status(self, user_id: str, cognition_id: str) -> Dict[str, Any]:
        """获取单个认知的已读状态"""
        try:
            doc = await self._collection.find_one({
                "user_id": user_id,
                "cognition_id": cognition_id
            })
            
            if doc:
                return {
                    "is_read": doc.get("is_read", False),
                    "read_at": doc.get("read_at")
                }
            
            return {"is_read": False, "read_at": None}
            
        except Exception as e:
            logger.error(f"获取已读状态失败: {str(e)}")
            return {"is_read": False, "read_at": None}
    
    @monitor_performance("get_read_statuses_batch")
    async def get_read_statuses_batch(self, cognition_ids: List[str], user_id: str) -> Dict[str, Dict[str, Any]]:
        """批量获取认知的已读状态"""
        try:
            if not cognition_ids:
                return {}
            
            # 查询数据库
            cursor = self._collection.find({
                "user_id": user_id,
                "cognition_id": {"$in": cognition_ids}
            })
            
            read_statuses = {}
            async for doc in cursor:
                cognition_id = doc.get("cognition_id")
                if cognition_id:
                    read_statuses[cognition_id] = {
                        "is_read": doc.get("is_read", False),
                        "read_at": doc.get("read_at")
                    }
            
            # 为没有记录的认知添加默认状态
            for cognition_id in cognition_ids:
                if cognition_id not in read_statuses:
                    read_statuses[cognition_id] = {"is_read": False, "read_at": None}
            
            return read_statuses
            
        except Exception as e:
            logger.error(f"批量获取已读状态失败: {str(e)}")
            # 返回默认状态
            return {cognition_id: {"is_read": False, "read_at": None} for cognition_id in cognition_ids}

# 依赖注入函数
async def get_cognition_database(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("cognitions_new"))
) -> CognitionDatabase:
    """获取认知数据库实例"""
    return CognitionDatabase(collection)

async def get_cognition_collections_database(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("cognition_collections"))
) -> CognitionCollectionsDatabase:
    """获取认知收藏夹数据库实例"""
    return CognitionCollectionsDatabase(collection)

async def get_cognition_favorites_database(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("cognition_favorites"))
) -> CognitionFavoritesDatabase:
    """获取认知收藏数据库实例"""
    return CognitionFavoritesDatabase(collection)

async def get_cognition_read_status_database(
    collection: AsyncIOMotorCollection = Depends(get_mongodb_collection("cognition_read_status"))
) -> CognitionReadStatusDatabase:
    """获取认知已读状态数据库实例"""
    return CognitionReadStatusDatabase(collection) 